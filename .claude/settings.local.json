{"permissions": {"allow": ["Bash(npm install:*)", "Bash(npm run dev:*)", "Bash(npm run build:*)", "mcp__supabase__list_projects", "<PERSON><PERSON>(vercel:*)", "mcp__supabase__apply_migration", "mcp__supabase__list_tables", "mcp__supabase__execute_sql", "Bash(grep:*)", "Bash(npm run lint)", "Bash(npx tsc:*)", "<PERSON><PERSON>(sed:*)", "Bash(rm:*)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["supabase"]}