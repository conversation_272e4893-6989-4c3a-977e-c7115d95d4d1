# Lullaby Clinic - Docker Compose for Production
version: '3.8'

services:
  # Main application
  lullaby-clinic:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: lullaby-clinic-app
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - VITE_APP_ENVIRONMENT=production
    networks:
      - lullaby-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /var/cache/nginx
      - /var/run
    volumes:
      - nginx-logs:/var/log/nginx

  # Nginx reverse proxy (optional, for load balancing)
  nginx-proxy:
    image: nginx:alpine
    container_name: lullaby-clinic-proxy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx-proxy.conf:/etc/nginx/nginx.conf:ro
      - nginx-proxy-logs:/var/log/nginx
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - lullaby-clinic
    networks:
      - lullaby-network
    profiles:
      - proxy

  # Monitoring (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: lullaby-clinic-monitoring
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - lullaby-network
    profiles:
      - monitoring

  # Log aggregation (optional)
  loki:
    image: grafana/loki:latest
    container_name: lullaby-clinic-logs
    restart: unless-stopped
    ports:
      - "3100:3100"
    volumes:
      - loki-data:/loki
    networks:
      - lullaby-network
    profiles:
      - logging

networks:
  lullaby-network:
    driver: bridge
    name: lullaby-clinic-network

volumes:
  nginx-logs:
    name: lullaby-clinic-nginx-logs
  nginx-proxy-logs:
    name: lullaby-clinic-proxy-logs
  prometheus-data:
    name: lullaby-clinic-prometheus-data
  loki-data:
    name: lullaby-clinic-loki-data