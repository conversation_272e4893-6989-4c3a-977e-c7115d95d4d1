# Simple Backend Integration Guide

## Overview

The Lullaby Clinic frontend is integrated with the backend through **Supabase as the primary database**. An optional Payload CMS backend can be connected for additional features.

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend (React + Vite)                 │
├─────────────────────────────────────────────────────────────┤
│  Backend Integration Service                                │
│  ├── Supabase Client (Primary)                             │
│  ├── Optional Backend Sync                                 │
│  └── Media Upload via Supabase Storage                     │
├─────────────────────────────────────────────────────────────┤
│  Primary: Supabase              │  Optional: Payload CMS    │
│  ├── User Authentication        │  ├── User Sync            │
│  ├── Database (PostgreSQL)      │  ├── Admin Interface      │
│  ├── Real-time Subscriptions    │  ├── Content Management   │
│  ├── Storage (Media Files)      │  └── Additional Features  │
│  └── Edge Functions             │                           │
└─────────────────────────────────────────────────────────────┘
```

## Quick Setup

### 1. Environment Configuration

```bash
# Required - Supabase
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Optional - Backend Integration
VITE_PAYLOAD_API_URL=http://localhost:3000
```

### 2. Start Systems

**Frontend:**
```bash
npm run dev
```

**Optional Backend (if using Payload CMS):**
```bash
cd /path/to/backend
npm run dev
```

## Usage Examples

### Fetch Data from Supabase

```tsx
import { useDoctors, useServices, useBlogPosts } from '@/hooks/useBackendContent';

function MyComponent() {
  // Fetch doctors
  const { doctors, error: doctorsError } = useDoctors({ 
    is_active: true 
  });

  // Fetch services
  const { services, error: servicesError } = useServices({ 
    category: 'facial' 
  });

  // Fetch blog posts
  const { posts, error: postsError } = useBlogPosts({ 
    language: 'th',
    is_featured: true 
  });

  if (doctorsError || servicesError || postsError) {
    return <div>Error loading data</div>;
  }

  return (
    <div>
      <h2>Doctors ({doctors?.length || 0})</h2>
      <h2>Services ({services?.length || 0})</h2>
      <h2>Blog Posts ({posts?.length || 0})</h2>
    </div>
  );
}
```

### Upload Media to Supabase Storage

```tsx
import { useMediaUpload } from '@/hooks/useBackendContent';

function ImageUpload() {
  const uploadMutation = useMediaUpload();

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const result = await uploadMutation.mutateAsync({ file });
      if (result.success) {
        console.log('File uploaded successfully:', result.data.url);
        // Use result.data.url for the uploaded file
      } else {
        console.error('Upload failed:', result.error);
      }
    } catch (error) {
      console.error('Upload error:', error);
    }
  };

  return (
    <div>
      <input 
        type="file" 
        onChange={handleFileUpload}
        accept="image/*"
      />
      {uploadMutation.isPending && <p>Uploading...</p>}
    </div>
  );
}
```

### Check Backend Status

```tsx
import { useBackendStatus, useSystemInfo } from '@/hooks/useBackendContent';

function SystemStatus() {
  const { data: backendStatus } = useBackendStatus();
  const { backend, database, integration } = useSystemInfo();

  return (
    <div>
      <h3>System Status</h3>
      <p>Database: {database.provider} - {database.status}</p>
      <p>Backend: {backend.available ? 'Connected' : 'Not Connected'}</p>
      <p>Integration Mode: {integration.mode}</p>
      <p>{integration.description}</p>
    </div>
  );
}
```

## Data Flow

### 1. User Authentication
```
User Login → Supabase Auth → Optional Backend Sync → User Session
```

### 2. Data Fetching
```
Component → React Hook → Supabase Database → Return Data
```

### 3. Media Upload
```
File Selection → Upload Hook → Supabase Storage → Return Public URL
```

### 4. Backend Sync (Optional)
```
User Action → Supabase → Notify Backend → Backend Processing
```

## Features

### ✅ Core Features (Supabase)
- User authentication and profiles
- Doctor profiles and specialties
- Service catalog with categories
- Blog posts and content
- Appointment booking
- Media file storage
- Real-time updates

### ✅ Optional Features (Backend Integration)
- User synchronization with Payload CMS
- Admin content management interface
- Enhanced media management
- Additional backend notifications

## API Reference

### Content Hooks

#### `useDoctors(filters?)`
Fetch doctors from Supabase.

**Parameters:**
- `filters.specialty?: string` - Filter by specialty
- `filters.is_active?: boolean` - Filter by active status

**Returns:**
- `doctors: Doctor[]` - Array of doctor objects
- `error: string | null` - Error message if any

#### `useServices(filters?)`
Fetch services from Supabase.

#### `useBlogPosts(filters?)`
Fetch blog posts from Supabase.

#### `useMediaUpload()`
Upload files to Supabase Storage.

#### `useBackendStatus()`
Check optional backend connectivity.

## Configuration

### Backend Integration Service
```typescript
// src/lib/backend-integration.ts
export const BACKEND_CONFIG = {
  API_URL: import.meta.env.VITE_PAYLOAD_API_URL || 'http://localhost:3000',
  ENABLE_BACKEND_SYNC: !!import.meta.env.VITE_PAYLOAD_API_URL,
  TIMEOUT: 5000,
};
```

### Supabase Configuration
```typescript
// src/lib/supabase/client.ts
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
```

## Troubleshooting

### Common Issues

1. **"Supabase connection failed"**
   - Check `VITE_SUPABASE_URL` and `VITE_SUPABASE_ANON_KEY`
   - Verify Supabase project is active

2. **"Backend not available"**
   - This is normal if `VITE_PAYLOAD_API_URL` is not set
   - Check backend server is running on correct port

3. **"Media upload failed"**
   - Check Supabase Storage bucket configuration
   - Verify file size and type restrictions

### Debug Information

```typescript
// Check backend status
import { getBackendStatus } from '@/lib/backend-integration';
console.log('Backend status:', getBackendStatus());

// Check Supabase connection
import { supabase } from '@/lib/supabase/client';
const { data, error } = await supabase.from('doctors').select('count');
console.log('Supabase connection:', error ? 'Failed' : 'Success');
```

## Benefits

1. **Simplicity**: Straightforward integration with minimal configuration
2. **Reliability**: Supabase provides robust, scalable infrastructure
3. **Flexibility**: Optional backend integration for enhanced features
4. **Performance**: Direct database access with built-in caching
5. **Real-time**: Native support for live updates and subscriptions

The integration is now simplified and focused on Supabase as the primary database with optional backend connectivity for enhanced features! 🎉
