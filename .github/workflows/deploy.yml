name: Deploy Lullaby Clinic

on:
  push:
    branches:
      - main
      - production
  pull_request:
    branches:
      - main

env:
  NODE_VERSION: 18

jobs:
  # Security and quality checks
  security-scan:
    name: <PERSON> Scan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run security audit
        run: npm audit --audit-level=moderate

      - name: Check for vulnerabilities
        run: npx audit-ci --config ./audit-ci.json

  # Build and test
  build-and-test:
    name: Build and Test
    runs-on: ubuntu-latest
    needs: security-scan
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint

      - name: Type check
        run: npx tsc --noEmit

      - name: Build application
        run: npm run build
        env:
          VITE_SUPABASE_URL: ${{ secrets.VITE_SUPABASE_URL }}
          VITE_SUPABASE_ANON_KEY: ${{ secrets.VITE_SUPABASE_ANON_KEY }}
          VITE_APP_ENVIRONMENT: production

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: dist-files
          path: dist/
          retention-days: 1

  # Deploy to Vercel (Production)
  deploy-vercel:
    name: Deploy to Vercel
    runs-on: ubuntu-latest
    needs: build-and-test
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/production'
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install Vercel CLI
        run: npm install -g vercel@latest

      - name: Deploy to Vercel
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/production" ]]; then
            vercel --prod --token=${{ secrets.VERCEL_TOKEN }}
          else
            vercel --token=${{ secrets.VERCEL_TOKEN }}
          fi
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

  # Deploy to Netlify (Staging)
  deploy-netlify:
    name: Deploy to Netlify
    runs-on: ubuntu-latest
    needs: build-and-test
    if: github.ref == 'refs/heads/main'
    environment: staging
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: dist-files
          path: dist/

      - name: Deploy to Netlify
        uses: netlify/actions/cli@master
        with:
          args: deploy --prod --dir=dist
        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
          NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}

  # Docker build and push
  docker-build:
    name: Build and Push Docker Image
    runs-on: ubuntu-latest
    needs: build-and-test
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/production'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ghcr.io/${{ github.repository }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Health check after deployment
  health-check:
    name: Post-Deployment Health Check
    runs-on: ubuntu-latest
    needs: [deploy-vercel]
    if: github.ref == 'refs/heads/production'
    steps:
      - name: Wait for deployment
        run: sleep 30

      - name: Health check
        run: |
          curl -f https://lullabyclinic.vercel.app/health || exit 1
          
      - name: Performance test
        run: |
          npx lighthouse-ci autorun --upload.target=temporary-public-storage
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

  # Notify deployment status
  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [deploy-vercel, deploy-netlify, docker-build, health-check]
    if: always()
    steps:
      - name: Notify success
        if: needs.deploy-vercel.result == 'success'
        run: |
          echo "🎉 Deployment successful!"
          echo "Production: https://lullabyclinic.vercel.app"
          echo "Staging: https://lullabyclinic-staging.netlify.app"

      - name: Notify failure
        if: failure()
        run: |
          echo "❌ Deployment failed!"
          echo "Check the logs for details."