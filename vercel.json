{"version": 2, "build": {"env": {"VITE_APP_ENVIRONMENT": "production"}}, "buildCommand": "npm run build", "outputDirectory": "dist", "framework": "vite", "installCommand": "npm install", "devCommand": "npm run dev", "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*\\.(?:js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "redirects": [{"source": "/home", "destination": "/"}, {"source": "/th", "destination": "/?lang=th"}, {"source": "/en", "destination": "/?lang=en"}, {"source": "/zh", "destination": "/?lang=zh"}], "rewrites": [{"source": "/((?!api/).*)", "destination": "/index.html"}], "cleanUrls": true, "trailingSlash": false}