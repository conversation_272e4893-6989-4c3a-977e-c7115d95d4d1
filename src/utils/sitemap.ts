interface SitemapEntry {
  url: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
  alternates?: { lang: string; url: string }[];
}

interface SitemapConfig {
  baseUrl: string;
  defaultLanguage: string;
  supportedLanguages: string[];
  routes: RouteConfig[];
}

interface RouteConfig {
  path: string;
  priority?: number;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  excludeFromSitemap?: boolean;
  dynamicRoutes?: {
    path: string;
    params: Record<string, string[]>;
  }[];
}

export class SitemapGenerator {
  private config: SitemapConfig;

  constructor(config: SitemapConfig) {
    this.config = config;
  }

  // Generate complete sitemap with multilingual support
  public generateSitemap(): string {
    const entries = this.generateSitemapEntries();
    return this.entriesToXML(entries);
  }

  // Generate sitemap entries for all routes and languages
  private generateSitemapEntries(): SitemapEntry[] {
    const entries: SitemapEntry[] = [];

    this.config.routes.forEach(route => {
      if (route.excludeFromSitemap) return;

      // Generate entries for static routes
      this.config.supportedLanguages.forEach(lang => {
        const entry = this.createSitemapEntry(route, lang);
        entries.push(entry);
      });

      // Generate entries for dynamic routes
      if (route.dynamicRoutes) {
        route.dynamicRoutes.forEach(dynamicRoute => {
          this.generateDynamicRouteEntries(dynamicRoute, route).forEach(entry => {
            entries.push(entry);
          });
        });
      }
    });

    return entries;
  }

  // Create sitemap entry for a specific route and language
  private createSitemapEntry(route: RouteConfig, lang: string): SitemapEntry {
    const url = this.buildUrl(route.path, lang);
    const alternates = this.generateAlternates(route.path);

    return {
      url,
      lastmod: new Date().toISOString().split('T')[0],
      changefreq: route.changefreq || 'weekly',
      priority: route.priority || this.getDefaultPriority(route.path),
      alternates
    };
  }

  // Generate dynamic route entries
  private generateDynamicRouteEntries(
    dynamicRoute: { path: string; params: Record<string, string[]> },
    baseRoute: RouteConfig
  ): SitemapEntry[] {
    const entries: SitemapEntry[] = [];
    const paramKeys = Object.keys(dynamicRoute.params);

    if (paramKeys.length === 0) return entries;

    // Generate all possible combinations of parameters
    const combinations = this.generateParameterCombinations(dynamicRoute.params);

    combinations.forEach(params => {
      let routePath = dynamicRoute.path;
      
      // Replace parameters in path
      Object.entries(params).forEach(([key, value]) => {
        routePath = routePath.replace(`[${key}]`, value);
      });

      this.config.supportedLanguages.forEach(lang => {
        const url = this.buildUrl(routePath, lang);
        const alternates = this.generateAlternates(routePath);

        entries.push({
          url,
          lastmod: new Date().toISOString().split('T')[0],
          changefreq: baseRoute.changefreq || 'weekly',
          priority: baseRoute.priority || 0.6,
          alternates
        });
      });
    });

    return entries;
  }

  // Generate all parameter combinations
  private generateParameterCombinations(params: Record<string, string[]>): Record<string, string>[] {
    const keys = Object.keys(params);
    if (keys.length === 0) return [{}];

    const [firstKey, ...restKeys] = keys;
    const firstValues = params[firstKey];
    const restParams = Object.fromEntries(restKeys.map(key => [key, params[key]]));
    const restCombinations = this.generateParameterCombinations(restParams);

    const combinations: Record<string, string>[] = [];
    firstValues.forEach(value => {
      restCombinations.forEach(restCombination => {
        combinations.push({ [firstKey]: value, ...restCombination });
      });
    });

    return combinations;
  }

  // Build URL for specific route and language
  private buildUrl(path: string, lang: string): string {
    const { baseUrl, defaultLanguage } = this.config;
    const cleanPath = path.startsWith('/') ? path.slice(1) : path;
    
    if (lang === defaultLanguage) {
      return cleanPath ? `${baseUrl}/${cleanPath}` : baseUrl;
    }
    
    return cleanPath ? `${baseUrl}/${lang}/${cleanPath}` : `${baseUrl}/${lang}`;
  }

  // Generate alternate language URLs
  private generateAlternates(path: string): { lang: string; url: string }[] {
    return this.config.supportedLanguages.map(lang => ({
      lang,
      url: this.buildUrl(path, lang)
    }));
  }

  // Get default priority based on route depth and type
  private getDefaultPriority(path: string): number {
    if (path === '' || path === '/') return 1.0;
    
    const segments = path.split('/').filter(Boolean);
    
    // Main pages get higher priority
    if (segments.length === 1) {
      const mainPages = ['services', 'about', 'gallery', 'blog', 'contact'];
      return mainPages.includes(segments[0]) ? 0.8 : 0.6;
    }
    
    // Deeper pages get lower priority
    if (segments.length === 2) return 0.6;
    return 0.4;
  }

  // Convert entries to XML format
  private entriesToXML(entries: SitemapEntry[]): string {
    const xmlHeader = '<?xml version="1.0" encoding="UTF-8"?>';
    const urlsetOpen = '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">';
    const urlsetClose = '</urlset>';

    const urls = entries.map(entry => this.entryToXML(entry)).join('\n');

    return [xmlHeader, urlsetOpen, urls, urlsetClose].join('\n');
  }

  // Convert single entry to XML
  private entryToXML(entry: SitemapEntry): string {
    const { url, lastmod, changefreq, priority, alternates } = entry;
    
    let xml = '  <url>\n';
    xml += `    <loc>${this.escapeXML(url)}</loc>\n`;
    
    if (lastmod) {
      xml += `    <lastmod>${lastmod}</lastmod>\n`;
    }
    
    if (changefreq) {
      xml += `    <changefreq>${changefreq}</changefreq>\n`;
    }
    
    if (priority !== undefined) {
      xml += `    <priority>${priority.toFixed(1)}</priority>\n`;
    }

    // Add alternate language links
    if (alternates && alternates.length > 0) {
      alternates.forEach(alternate => {
        xml += `    <xhtml:link rel="alternate" hreflang="${alternate.lang}" href="${this.escapeXML(alternate.url)}" />\n`;
      });
    }
    
    xml += '  </url>';
    
    return xml;
  }

  // Escape XML special characters
  private escapeXML(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;');
  }

  // Generate robots.txt content
  public generateRobotsTxt(): string {
    const { baseUrl } = this.config;
    
    return [
      'User-agent: *',
      'Allow: /',
      '',
      '# Sitemaps',
      `Sitemap: ${baseUrl}/sitemap.xml`,
      '',
      '# Crawl-delay for medical website',
      'Crawl-delay: 1',
      '',
      '# Disallow sensitive paths',
      'Disallow: /admin/',
      'Disallow: /api/',
      'Disallow: /private/',
      'Disallow: /*.json$',
      'Disallow: /*?*debug*',
      '',
      '# Allow medical content',
      'Allow: /services/',
      'Allow: /treatments/',
      'Allow: /doctors/',
      'Allow: /blog/',
      ''
    ].join('\n');
  }
}

// Default configuration for Lullaby Clinic
export const createLullabySitemapConfig = (): SitemapConfig => ({
  baseUrl: 'https://lullabyclinic.com',
  defaultLanguage: 'en',
  supportedLanguages: ['en', 'th', 'zh'],
  routes: [
    {
      path: '',
      priority: 1.0,
      changefreq: 'weekly'
    },
    {
      path: 'services',
      priority: 0.9,
      changefreq: 'weekly'
    },
    {
      path: 'about',
      priority: 0.8,
      changefreq: 'monthly'
    },
    {
      path: 'gallery',
      priority: 0.7,
      changefreq: 'weekly'
    },
    {
      path: 'blog',
      priority: 0.8,
      changefreq: 'daily',
      dynamicRoutes: [
        {
          path: 'blog/[slug]',
          params: {
            slug: [
              'botox-treatment-guide',
              'dermal-fillers-explained',
              'laser-treatments',
              'skincare-routine',
              'post-treatment-care',
              'choosing-right-treatment',
              'medical-aesthetics-trends',
              'safety-considerations'
            ]
          }
        }
      ]
    },
    {
      path: 'contact',
      priority: 0.9,
      changefreq: 'monthly'
    },
    {
      path: 'booking',
      priority: 0.9,
      changefreq: 'monthly'
    },
    {
      path: 'services/botox',
      priority: 0.8,
      changefreq: 'monthly'
    },
    {
      path: 'services/fillers',
      priority: 0.8,
      changefreq: 'monthly'
    },
    {
      path: 'services/laser',
      priority: 0.8,
      changefreq: 'monthly'
    },
    {
      path: 'services/skincare',
      priority: 0.8,
      changefreq: 'monthly'
    },
    {
      path: 'privacy-policy',
      priority: 0.3,
      changefreq: 'yearly'
    },
    {
      path: 'terms-of-service',
      priority: 0.3,
      changefreq: 'yearly'
    }
  ]
});

// Utility function to generate and download sitemap
export const generateAndDownloadSitemap = (config?: SitemapConfig) => {
  const sitemapConfig = config || createLullabySitemapConfig();
  const generator = new SitemapGenerator(sitemapConfig);
  
  const sitemap = generator.generateSitemap();
  const robots = generator.generateRobotsTxt();
  
  // Download sitemap.xml
  const sitemapBlob = new Blob([sitemap], { type: 'application/xml' });
  const sitemapUrl = URL.createObjectURL(sitemapBlob);
  const sitemapLink = document.createElement('a');
  sitemapLink.href = sitemapUrl;
  sitemapLink.download = 'sitemap.xml';
  sitemapLink.click();
  URL.revokeObjectURL(sitemapUrl);
  
  // Download robots.txt
  const robotsBlob = new Blob([robots], { type: 'text/plain' });
  const robotsUrl = URL.createObjectURL(robotsBlob);
  const robotsLink = document.createElement('a');
  robotsLink.href = robotsUrl;
  robotsLink.download = 'robots.txt';
  robotsLink.click();
  URL.revokeObjectURL(robotsUrl);
  
  return { sitemap, robots };
};

// Generate sitemap for medical practice schema
export const generateMedicalSitemap = () => {
  const config = createLullabySitemapConfig();
  
  // Add medical-specific routes
  config.routes.push(
    {
      path: 'treatments',
      priority: 0.9,
      changefreq: 'weekly',
      dynamicRoutes: [
        {
          path: 'treatments/[category]/[treatment]',
          params: {
            category: ['facial', 'body', 'laser', 'injectable'],
            treatment: [
              'anti-aging-facial',
              'hydrating-treatment',
              'body-contouring',
              'cellulite-treatment',
              'laser-hair-removal',
              'skin-resurfacing',
              'botox-injections',
              'dermal-fillers'
            ]
          }
        }
      ]
    },
    {
      path: 'doctors',
      priority: 0.8,
      changefreq: 'monthly',
      dynamicRoutes: [
        {
          path: 'doctors/[doctor]',
          params: {
            doctor: ['dr-smith', 'dr-johnson', 'dr-thai', 'dr-wang']
          }
        }
      ]
    },
    {
      path: 'conditions',
      priority: 0.7,
      changefreq: 'monthly',
      dynamicRoutes: [
        {
          path: 'conditions/[condition]',
          params: {
            condition: [
              'acne-treatment',
              'wrinkle-reduction',
              'skin-pigmentation',
              'hair-loss',
              'body-sculpting'
            ]
          }
        }
      ]
    }
  );
  
  const generator = new SitemapGenerator(config);
  return generator.generateSitemap();
};

export default SitemapGenerator;