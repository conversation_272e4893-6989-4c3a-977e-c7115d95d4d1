
import { useState, useEffect } from 'react';
import { servicesApi, Service, ServiceFilters } from '@/lib/supabase/services';

export const useServices = (filters: ServiceFilters = {}) => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchServices = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await servicesApi.getServices(filters);
      setServices(data);
    } catch (err) {
      console.error('Error fetching services:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch services');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchServices();
  }, [JSON.stringify(filters)]);

  const refetch = () => {
    fetchServices();
  };

  return {
    services,
    loading,
    error,
    refetch
  };
};

export const useService = (serviceId: string) => {
  const [service, setService] = useState<Service | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchService = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await servicesApi.getServiceById(serviceId);
        setService(data);
      } catch (err) {
        console.error('Error fetching service:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch service');
      } finally {
        setLoading(false);
      }
    };

    if (serviceId) {
      fetchService();
    }
  }, [serviceId]);

  return {
    service,
    loading,
    error
  };
};

export const useServicesByCategory = (category: string) => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await servicesApi.getServicesByCategory(category);
        setServices(data);
      } catch (err) {
        console.error('Error fetching services by category:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch services');
      } finally {
        setLoading(false);
      }
    };

    if (category) {
      fetchServices();
    }
  }, [category]);

  return {
    services,
    loading,
    error
  };
};

export const useFeaturedServices = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await servicesApi.getFeaturedServices();
        setServices(data);
      } catch (err) {
        console.error('Error fetching featured services:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch featured services');
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  return {
    services,
    loading,
    error
  };
};

export const usePopularServices = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await servicesApi.getPopularServices();
        setServices(data);
      } catch (err) {
        console.error('Error fetching popular services:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch popular services');
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  return {
    services,
    loading,
    error
  };
};
