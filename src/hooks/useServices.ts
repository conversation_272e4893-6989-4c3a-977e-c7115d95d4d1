
/**
 * Lullaby Clinic - Service Hooks
 * Custom hooks for service operations
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import { useQuery } from '@tanstack/react-query';
import { serviceService } from '@/lib/supabase-services';

interface ServiceSearchFilters {
  category?: string;
  difficulty?: string;
  priceRange?: [number, number];
}

export const useServiceCategories = () => {
  return useQuery({
    queryKey: ['services', 'categories'],
    queryFn: () => serviceService.getCategories(),
    staleTime: 10 * 60 * 1000 // 10 minutes
  });
};

export const useServices = (categoryId?: string) => {
  return useQuery({
    queryKey: ['services', categoryId],
    queryFn: () => serviceService.getServices(categoryId),
    staleTime: 10 * 60 * 1000 // 10 minutes
  });
};

export const useService = (slug: string) => {
  return useQuery({
    queryKey: ['service', slug],
    queryFn: () => serviceService.getServiceBySlug(slug),
    enabled: !!slug,
    staleTime: 10 * 60 * 1000 // 10 minutes
  });
};

export const useSearchServices = (
  query: string,
  filters?: ServiceSearchFilters
) => {
  return useQuery({
    queryKey: ['services', 'search', query, filters],
    queryFn: () => serviceService.searchServices(query, filters),
    enabled: !!query,
    staleTime: 5 * 60 * 1000 // 5 minutes
  });
};
