
/**
 * Lullaby Clinic - Payment Hooks
 * Custom hooks for payment operations
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { paymentService } from '@/lib/supabase-services';

export const useCreatePaymentIntent = () => {
  return useMutation({
    mutationFn: ({ appointmentId, amount }: { appointmentId: string; amount: number }) =>
      paymentService.createPaymentIntent(appointmentId, amount)
  });
};

export const useUpdatePaymentStatus = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ 
      paymentId, 
      status, 
      transactionId 
    }: { 
      paymentId: string; 
      status: string; 
      transactionId?: string 
    }) => paymentService.updatePaymentStatus(paymentId, status, transactionId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payments'] });
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
    }
  });
};
