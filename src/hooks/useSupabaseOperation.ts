
import { useState, useCallback } from 'react';
import { toast } from 'sonner';

interface OperationResult<T> {
  data: T | null;
  error: string | null;
  isLoading: boolean;
}

interface UseSupabaseOperationOptions<T> {
  onSuccess?: (data: T) => void;
  onError?: (error: string) => void;
  showToast?: boolean;
}

export function useSupabaseOperation<T>(
  operation: () => Promise<{ data: T; error: Error | null }>,
  options: UseSupabaseOperationOptions<T> = {}
) {
  const [result, setResult] = useState<OperationResult<T>>({
    data: null,
    error: null,
    isLoading: false
  });

  const execute = useCallback(async () => {
    setResult(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const { data, error } = await operation();

      if (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        setResult({ data: null, error: errorMessage, isLoading: false });
        
        if (options.showToast !== false) {
          toast.error(errorMessage);
        }
        
        options.onError?.(errorMessage);
      } else {
        setResult({ data, error: null, isLoading: false });
        
        if (options.showToast !== false) {
          toast.success('Operation completed successfully');
        }
        
        options.onSuccess?.(data);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setResult({ data: null, error: errorMessage, isLoading: false });
      
      if (options.showToast !== false) {
        toast.error(errorMessage);
      }
      
      options.onError?.(errorMessage);
    }
  }, [operation, options]);

  return {
    ...result,
    execute
  };
}
