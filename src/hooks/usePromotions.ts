
/**
 * Lullaby Clinic - Promotion Hooks
 * Custom hooks for promotion operations
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import { useQuery, useMutation } from '@tanstack/react-query';
import { promotionService } from '@/lib/supabase-services';

export const useActivePromotions = () => {
  return useQuery({
    queryKey: ['promotions', 'active'],
    queryFn: () => promotionService.getActivePromotions(),
    staleTime: 5 * 60 * 1000 // 5 minutes
  });
};

export const useValidatePromoCode = () => {
  return useMutation({
    mutationFn: ({ promoCode, serviceIds }: { promoCode: string; serviceIds?: string[] }) =>
      promotionService.validatePromoCode(promoCode, serviceIds)
  });
};
