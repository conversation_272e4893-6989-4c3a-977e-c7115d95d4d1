/**
 * Lullaby Clinic - Backend Content Hooks
 * Simplified hooks for backend integration via Supabase
 * 
 * @version 1.0.0
 * @created 2024-06-24
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getContent, uploadMedia, getBackendStatus } from '@/lib/backend-integration';

// Query Keys
export const CONTENT_QUERY_KEYS = {
  doctors: ['content', 'doctors'] as const,
  services: ['content', 'services'] as const,
  blogPosts: ['content', 'blog-posts'] as const,
  backendStatus: ['backend', 'status'] as const,
} as const;

/**
 * Hook to get doctors from Supabase
 */
export const useDoctors = (filters?: {
  specialty?: string;
  is_active?: boolean;
}) => {
  return useQuery({
    queryKey: [...CONTENT_QUERY_KEYS.doctors, filters],
    queryFn: () => getContent('doctors', filters),
    staleTime: 10 * 60 * 1000, // 10 minutes
    select: (data) => ({
      doctors: data.success ? data.data : [],
      error: data.error,
      source: data.source,
    }),
  });
};

/**
 * Hook to get services from Supabase
 */
export const useServices = (filters?: {
  category?: string;
  is_active?: boolean;
}) => {
  return useQuery({
    queryKey: [...CONTENT_QUERY_KEYS.services, filters],
    queryFn: () => getContent('services', filters),
    staleTime: 10 * 60 * 1000, // 10 minutes
    select: (data) => ({
      services: data.success ? data.data : [],
      error: data.error,
      source: data.source,
    }),
  });
};

/**
 * Hook to get blog posts from Supabase
 */
export const useBlogPosts = (filters?: {
  category?: string;
  language?: string;
  is_featured?: boolean;
}) => {
  return useQuery({
    queryKey: [...CONTENT_QUERY_KEYS.blogPosts, filters],
    queryFn: () => getContent('blog-posts', filters),
    staleTime: 15 * 60 * 1000, // 15 minutes
    select: (data) => ({
      posts: data.success ? data.data : [],
      error: data.error,
      source: data.source,
    }),
  });
};

/**
 * Hook to upload media files
 */
export const useMediaUpload = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ file, bucket }: { file: File; bucket?: string }) =>
      uploadMedia(file, bucket),
    onSuccess: () => {
      // Invalidate any media-related queries if needed
      console.log('Media uploaded successfully');
    },
    onError: (error) => {
      console.error('Media upload failed:', error);
    },
  });
};

/**
 * Hook to check backend status
 */
export const useBackendStatus = () => {
  return useQuery({
    queryKey: CONTENT_QUERY_KEYS.backendStatus,
    queryFn: () => getBackendStatus(),
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Check every minute
    retry: 1,
  });
};

/**
 * Hook to get system information
 */
export const useSystemInfo = () => {
  const { data: backendStatus } = useBackendStatus();
  
  return {
    backend: {
      available: backendStatus?.available || false,
      url: backendStatus?.url || 'Not configured',
    },
    database: {
      provider: 'Supabase',
      status: 'Connected', // Assume connected if hooks are working
    },
    integration: {
      mode: backendStatus?.available ? 'Full Integration' : 'Supabase Only',
      description: backendStatus?.available 
        ? 'Frontend connected to both Supabase and backend'
        : 'Frontend using Supabase as primary database',
    },
  };
};

// Export all hooks
export {
  getContent,
  uploadMedia,
  getBackendStatus,
};
