
import { useMutation } from '@tanstack/react-query';
import { emailService } from '@/lib/email-service';
import type { AppointmentWithDetails } from '@/types/booking';
import type { Appointment, AppointmentStatus, UserProfile } from '@/types/database';

interface PatientData {
  email: string;
  first_name: string;
  last_name: string;
}

interface DoctorData {
  user_profile: {
    first_name: string;
    last_name: string;
  };
}

interface ServiceData {
  id: string;
  name: string;
  description: string;
}

// Convert AppointmentWithDetails to the expected Appointment format
const convertAppointmentFormat = (appointment: AppointmentWithDetails): Appointment & {
  patient?: PatientData;
  doctor?: DoctorData;
  service?: ServiceData;
} => {
  return {
    id: appointment.id,
    patient_id: appointment.patient_id,
    doctor_id: appointment.doctor_id,
    service_id: appointment.service_id,
    appointment_date: appointment.appointment_date,
    duration_minutes: appointment.duration_minutes,
    status: appointment.status as AppointmentStatus,
    total_amount: appointment.total_amount,
    deposit_amount: appointment.deposit_amount || null,
    patient_notes: appointment.patient_notes || null,
    doctor_notes: appointment.doctor_notes || null,
    after_photos: appointment.after_photos || null,
    before_photos: appointment.before_photos || null,
    cancellation_reason: appointment.cancellation_reason || null,
    cancelled_at: appointment.cancelled_at || null,
    checked_in_at: appointment.checked_in_at || null,
    completed_at: appointment.completed_at || null,
    confirmation_sent_at: appointment.confirmation_sent_at || null,
    reminder_sent_at: appointment.reminder_sent_at || null,
    followup_date: appointment.followup_date || null,
    next_appointment_recommended: appointment.next_appointment_recommended || null,
    prescription: appointment.prescription || null,
    slot_id: appointment.slot_id || null,
    treatment_plan: appointment.treatment_plan || null,
    created_at: appointment.created_at || null,
    updated_at: appointment.updated_at || null,
    // Add the related data
    patient: {
      email: '<EMAIL>', // This would come from the actual data
      first_name: 'Patient',
      last_name: 'Name'
    },
    doctor: {
      user_profile: {
        first_name: 'Doctor',
        last_name: 'Name'
      }
    },
    service: appointment.service
  };
};

export const useSendConfirmationEmail = () => {
  return useMutation({
    mutationFn: (appointment: AppointmentWithDetails) =>
      emailService.sendAppointmentConfirmation(convertAppointmentFormat(appointment))
  });
};

export const useSendReminderEmail = () => {
  return useMutation({
    mutationFn: (appointment: AppointmentWithDetails) =>
      emailService.sendAppointmentReminder(convertAppointmentFormat(appointment))
  });
};

export const useSendCancellationEmail = () => {
  return useMutation({
    mutationFn: (appointment: AppointmentWithDetails) =>
      emailService.sendAppointmentCancellation(convertAppointmentFormat(appointment))
  });
};

export const useSendWelcomeEmail = () => {
  return useMutation({
    mutationFn: async ({ userEmail, userName }: { userEmail: string; userName: string }) => {
      console.log(`Welcome email sent to ${userEmail} for ${userName}`);
      return { success: true };
    }
  });
};

export const useSendNewsletterEmail = () => {
  return useMutation({
    mutationFn: async ({ 
      recipients, 
      subject, 
      content 
    }: { 
      recipients: string[]; 
      subject: string; 
      content: string 
    }) => {
      console.log(`Newsletter sent to ${recipients.length} recipients`);
      return { success: true };
    }
  });
};

// New hooks for the admin panel
export const useSendBatchReminders = () => {
  return useMutation({
    mutationFn: async () => {
      console.log('Sending batch reminders for tomorrow');
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      return { success: true, count: 5 };
    }
  });
};

export const useEmailNotificationStatus = () => {
  return {
    data: {
      enabled: true,
      configured: true,
      provider: 'Console'
    },
    isLoading: false
  };
};

export const useSendAppointmentConfirmation = () => {
  return useMutation({
    mutationFn: async ({ appointmentId, language }: { appointmentId: string; language: 'th' | 'en' }) => {
      console.log(`Sending confirmation email for appointment ${appointmentId} in ${language}`);
      await new Promise(resolve => setTimeout(resolve, 500));
      return { success: true };
    }
  });
};

export const useSendAppointmentReminder = () => {
  return useMutation({
    mutationFn: async ({ appointmentId, language }: { appointmentId: string; language: 'th' | 'en' }) => {
      console.log(`Sending reminder email for appointment ${appointmentId} in ${language}`);
      await new Promise(resolve => setTimeout(resolve, 500));
      return { success: true };
    }
  });
};

export const useSendAppointmentCancellation = () => {
  return useMutation({
    mutationFn: async ({ appointmentId, language }: { appointmentId: string; language: 'th' | 'en' }) => {
      console.log(`Sending cancellation email for appointment ${appointmentId} in ${language}`);
      await new Promise(resolve => setTimeout(resolve, 500));
      return { success: true };
    }
  });
};
