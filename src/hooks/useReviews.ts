
/**
 * Lullaby Clinic - Review Hooks
 * Custom hooks for review operations
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { reviewService } from '@/lib/supabase-services';

export const useCreateReview = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (reviewData: {
      appointment_id: string;
      doctor_id: string;
      service_id: string;
      rating: number;
      title?: string;
      comment?: string;
      is_anonymous?: boolean;
    }) => reviewService.createReview(reviewData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reviews'] });
      queryClient.invalidateQueries({ queryKey: ['services'] });
      queryClient.invalidateQueries({ queryKey: ['doctors'] });
    }
  });
};

export const useServiceReviews = (serviceId: string, limit?: number) => {
  return useQuery({
    queryKey: ['reviews', 'service', serviceId, limit],
    queryFn: () => reviewService.getServiceReviews(serviceId, limit),
    enabled: !!serviceId,
    staleTime: 5 * 60 * 1000 // 5 minutes
  });
};
