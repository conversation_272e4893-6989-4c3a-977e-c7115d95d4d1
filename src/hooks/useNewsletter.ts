
/**
 * Lullaby Clinic - Newsletter Hooks
 * Custom hooks for newsletter operations
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import { useMutation } from '@tanstack/react-query';
import { newsletterService } from '@/lib/supabase-services';
import type { NewsletterSubscriptionInput } from '@/types';

export const useNewsletterSubscribe = () => {
  return useMutation({
    mutationFn: (subscriptionData: NewsletterSubscriptionInput) =>
      newsletterService.subscribe(subscriptionData)
  });
};

export const useNewsletterUnsubscribe = () => {
  return useMutation({
    mutationFn: (email: string) => newsletterService.unsubscribe(email)
  });
};
