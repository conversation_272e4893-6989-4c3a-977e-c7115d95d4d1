
/**
 * Lullaby Clinic - Authentication Hooks
 * Custom hooks for authentication operations
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import { useState, useEffect } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { authService } from '@/lib/supabase-services';
import { auth } from '@/lib/supabase';
import type { UserProfile } from '@/types';

export const useAuth = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const queryClient = useQueryClient();

  useEffect(() => {
    // Get initial session
    auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null);
      setLoading(false);
    });

    // Listen for auth changes
    const { data: { subscription } } = auth.onAuthStateChange(
      async (event, session) => {
        setUser(session?.user ?? null);
        setLoading(false);
        
        // Clear queries on sign out
        if (event === 'SIGNED_OUT') {
          queryClient.clear();
        }
      }
    );

    return () => subscription.unsubscribe();
  }, [queryClient]);

  const signUp = useMutation({
    mutationFn: ({ email, password, userData }: { 
      email: string; 
      password: string; 
      userData: Partial<UserProfile> 
    }) => authService.signUp(email, password, userData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user'] });
    }
  });

  const signIn = useMutation({
    mutationFn: ({ email, password }: { email: string; password: string }) => 
      authService.signIn(email, password),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user'] });
    }
  });

  const signOut = useMutation({
    mutationFn: () => authService.signOut(),
    onSuccess: () => {
      queryClient.clear();
    }
  });

  const resetPassword = useMutation({
    mutationFn: (email: string) => authService.resetPassword(email)
  });

  return {
    user,
    loading,
    signUp,
    signIn,
    signOut,
    resetPassword,
    isAuthenticated: !!user
  };
};
