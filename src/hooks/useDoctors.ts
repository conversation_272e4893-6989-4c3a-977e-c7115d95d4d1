
/**
 * Lullaby Clinic - Doctor Hooks
 * Custom hooks for doctor operations
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import { useQuery } from '@tanstack/react-query';
import { doctorService } from '@/lib/supabase-services';

export const useDoctors = () => {
  return useQuery({
    queryKey: ['doctors'],
    queryFn: () => doctorService.getDoctors(),
    staleTime: 10 * 60 * 1000 // 10 minutes
  });
};

export const useDoctorAvailability = (
  doctorId: string, 
  startDate: string, 
  endDate: string
) => {
  return useQuery({
    queryKey: ['doctor', 'availability', doctorId, startDate, endDate],
    queryFn: () => doctorService.getDoctorAvailability(doctorId, startDate, endDate),
    enabled: !!doctorId && !!startDate && !!endDate,
    staleTime: 2 * 60 * 1000 // 2 minutes
  });
};
