
/**
 * Lullaby Clinic - User Profile Hooks
 * Custom hooks for user profile operations
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { userService } from '@/lib/supabase-services';
import type { UserProfile } from '@/types';

export const useUserProfile = (userId?: string) => {
  return useQuery({
    queryKey: ['user', 'profile', userId],
    queryFn: () => userService.getProfile(userId!),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000 // 5 minutes
  });
};

export const useUpdateProfile = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ userId, updates }: { userId: string; updates: Partial<UserProfile> }) =>
      userService.updateProfile(userId, updates),
    onSuccess: (data, { userId }) => {
      queryClient.invalidateQueries({ queryKey: ['user', 'profile', userId] });
    }
  });
};

export const useUserAppointments = (userId?: string, status?: string) => {
  return useQuery({
    queryKey: ['user', 'appointments', userId, status],
    queryFn: () => userService.getUserAppointments(userId!, status),
    enabled: !!userId
  });
};
