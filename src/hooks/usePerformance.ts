import { useEffect, useState, useCallback, useMemo } from 'react';

interface PerformanceEventTiming extends PerformanceEntry {
  processingStart: number;
}

interface LayoutShift extends PerformanceEntry {
  value: number;
  hadRecentInput: boolean;
}

interface PerformanceMetrics {
  fcp: number | null; // First Contentful Paint
  lcp: number | null; // Largest Contentful Paint
  fid: number | null; // First Input Delay
  cls: number | null; // Cumulative Layout Shift
  ttfb: number | null; // Time to First Byte
  domContentLoaded: number | null;
  loadComplete: number | null;
  memoryUsage: number | null;
  connectionType: string | null;
}

interface PerformanceThresholds {
  fcp: { good: 1800, poor: 3000 };
  lcp: { good: 2500, poor: 4000 };
  fid: { good: 100, poor: 300 };
  cls: { good: 0.1, poor: 0.25 };
  ttfb: { good: 800, poor: 1800 };
}

interface PerformanceScore {
  overall: 'good' | 'needs-improvement' | 'poor';
  scores: {
    [K in keyof PerformanceThresholds]: 'good' | 'needs-improvement' | 'poor' | 'unknown';
  };
}

export const usePerformance = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fcp: null,
    lcp: null,
    fid: null,
    cls: null,
    ttfb: null,
    domContentLoaded: null,
    loadComplete: null,
    memoryUsage: null,
    connectionType: null
  });

  const [isLoading, setIsLoading] = useState(true);
  const [score, setScore] = useState<PerformanceScore | null>(null);

  const thresholds = useMemo<PerformanceThresholds>(() => ({
    fcp: { good: 1800, poor: 3000 },
    lcp: { good: 2500, poor: 4000 },
    fid: { good: 100, poor: 300 },
    cls: { good: 0.1, poor: 0.25 },
    ttfb: { good: 800, poor: 1800 }
  }), []);

  // Calculate performance score
  const calculateScore = useCallback((currentMetrics: PerformanceMetrics): PerformanceScore => {
    const scores = {
      fcp: getMetricScore(currentMetrics.fcp, thresholds.fcp),
      lcp: getMetricScore(currentMetrics.lcp, thresholds.lcp),
      fid: getMetricScore(currentMetrics.fid, thresholds.fid),
      cls: getMetricScore(currentMetrics.cls, thresholds.cls),
      ttfb: getMetricScore(currentMetrics.ttfb, thresholds.ttfb)
    };

    const goodCount = Object.values(scores).filter(s => s === 'good').length;
    const poorCount = Object.values(scores).filter(s => s === 'poor').length;
    
    let overall: 'good' | 'needs-improvement' | 'poor';
    if (goodCount >= 4) overall = 'good';
    else if (poorCount >= 2) overall = 'poor';
    else overall = 'needs-improvement';

    return { overall, scores };
  }, [thresholds]);

  const getMetricScore = (
    value: number | null, 
    threshold: { good: number; poor: number }
  ): 'good' | 'needs-improvement' | 'poor' | 'unknown' => {
    if (value === null) return 'unknown';
    if (value <= threshold.good) return 'good';
    if (value <= threshold.poor) return 'needs-improvement';
    return 'poor';
  };

  // Collect Core Web Vitals
  const collectWebVitals = useCallback(() => {
    // First Contentful Paint
    const fcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
      if (fcpEntry) {
        setMetrics(prev => ({ ...prev, fcp: fcpEntry.startTime }));
      }
    });

    // Largest Contentful Paint
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      setMetrics(prev => ({ ...prev, lcp: lastEntry.startTime }));
    });

    // First Input Delay
    const fidObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        const fidEntry = entry as PerformanceEventTiming;
        if (fidEntry.processingStart && fidEntry.startTime) {
          const fid = fidEntry.processingStart - fidEntry.startTime;
          setMetrics(prev => ({ ...prev, fid }));
        }
      });
    });

    // Cumulative Layout Shift
    let clsValue = 0;
    const clsObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        const clsEntry = entry as LayoutShift;
        if (!clsEntry.hadRecentInput) {
          clsValue += clsEntry.value;
          setMetrics(prev => ({ ...prev, cls: clsValue }));
        }
      }
    });

    try {
      fcpObserver.observe({ entryTypes: ['paint'] });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      fidObserver.observe({ entryTypes: ['first-input'] });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
    } catch (error) {
      console.warn('Performance Observer not supported:', error);
    }

    return () => {
      fcpObserver.disconnect();
      lcpObserver.disconnect();
      fidObserver.disconnect();
      clsObserver.disconnect();
    };
  }, []);

  // Collect Navigation Timing
  const collectNavigationTiming = useCallback(() => {
    if ('performance' in window && 'timing' in window.performance) {
      const timing = window.performance.timing;
      const navigationStart = timing.navigationStart;

      const ttfb = timing.responseStart - navigationStart;
      const domContentLoaded = timing.domContentLoadedEventEnd - navigationStart;
      const loadComplete = timing.loadEventEnd - navigationStart;

      setMetrics(prev => ({
        ...prev,
        ttfb,
        domContentLoaded,
        loadComplete
      }));
    }
  }, []);

  // Collect Memory Usage
  const collectMemoryUsage = useCallback(() => {
    if ('memory' in performance) {
      const memory = (performance as unknown as { memory: { usedJSHeapSize: number } }).memory;
      const memoryUsage = memory.usedJSHeapSize / 1024 / 1024; // MB
      setMetrics(prev => ({ ...prev, memoryUsage }));
    }
  }, []);

  // Collect Connection Information
  const collectConnectionInfo = useCallback(() => {
    if ('connection' in navigator) {
      const connection = (navigator as unknown as { connection: { effectiveType?: string; type?: string } }).connection;
      setMetrics(prev => ({ 
        ...prev, 
        connectionType: connection.effectiveType || connection.type || 'unknown'
      }));
    }
  }, []);

  // Performance optimization tips
  const getOptimizationTips = useCallback((currentScore: PerformanceScore): string[] => {
    const tips: string[] = [];

    if (currentScore.scores.fcp !== 'good') {
      tips.push('Optimize First Contentful Paint by reducing server response time and eliminating render-blocking resources');
    }

    if (currentScore.scores.lcp !== 'good') {
      tips.push('Improve Largest Contentful Paint by optimizing images, preloading key resources, and using efficient font loading');
    }

    if (currentScore.scores.fid !== 'good') {
      tips.push('Reduce First Input Delay by breaking up long tasks and optimizing JavaScript execution');
    }

    if (currentScore.scores.cls !== 'good') {
      tips.push('Minimize Cumulative Layout Shift by setting dimensions for images and ads, and avoiding dynamic content insertion');
    }

    if (currentScore.scores.ttfb !== 'good') {
      tips.push('Improve Time to First Byte by optimizing server performance, using CDN, and implementing caching strategies');
    }

    return tips;
  }, []);

  // Resource timing analysis
  const getResourceTiming = useCallback(() => {
    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    
    const resourceAnalysis = {
      totalResources: resources.length,
      slowResources: resources.filter(r => r.duration > 1000),
      largeResources: resources.filter(r => r.transferSize > 500000), // > 500KB
      cachedResources: resources.filter(r => r.transferSize === 0),
      resourceTypes: {
        scripts: resources.filter(r => r.initiatorType === 'script').length,
        images: resources.filter(r => r.initiatorType === 'img').length,
        css: resources.filter(r => r.initiatorType === 'css').length,
        fonts: resources.filter(r => r.initiatorType === 'font').length
      }
    };

    return resourceAnalysis;
  }, []);

  // Bundle size analysis
  const getBundleAnalysis = useCallback(() => {
    const entries = performance.getEntriesByType('navigation') as PerformanceNavigationTiming[];
    if (entries.length === 0) return null;

    const navigation = entries[0];
    return {
      transferSize: navigation.transferSize,
      encodedBodySize: navigation.encodedBodySize,
      decodedBodySize: navigation.decodedBodySize,
      compressionRatio: navigation.encodedBodySize / navigation.decodedBodySize,
      loadTime: navigation.loadEventEnd - navigation.fetchStart
    };
  }, []);

  // Initialize performance monitoring
  useEffect(() => {
    const cleanup = collectWebVitals();
    
    // Collect initial metrics
    collectNavigationTiming();
    collectMemoryUsage();
    collectConnectionInfo();

    // Set up periodic updates
    const interval = setInterval(() => {
      collectMemoryUsage();
      collectConnectionInfo();
    }, 5000);

    // Mark as loaded after initial collection
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => {
      cleanup?.();
      clearInterval(interval);
    };
  }, [collectWebVitals, collectNavigationTiming, collectMemoryUsage, collectConnectionInfo]);

  // Update score when metrics change
  useEffect(() => {
    const newScore = calculateScore(metrics);
    setScore(newScore);
  }, [metrics, calculateScore]);

  // Export performance data
  const exportPerformanceData = useCallback(() => {
    const data = {
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      metrics,
      score,
      resourceTiming: getResourceTiming(),
      bundleAnalysis: getBundleAnalysis()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `performance-report-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }, [metrics, score, getResourceTiming, getBundleAnalysis]);

  // Performance monitoring report
  const getPerformanceReport = useCallback(() => {
    if (!score) return null;

    return {
      metrics,
      score,
      optimizationTips: getOptimizationTips(score),
      resourceTiming: getResourceTiming(),
      bundleAnalysis: getBundleAnalysis(),
      isGoodPerformance: score.overall === 'good',
      needsImprovement: score.overall !== 'good'
    };
  }, [metrics, score, getOptimizationTips, getResourceTiming, getBundleAnalysis]);

  return {
    metrics,
    score,
    isLoading,
    thresholds,
    getOptimizationTips: () => score ? getOptimizationTips(score) : [],
    getResourceTiming,
    getBundleAnalysis,
    exportPerformanceData,
    getPerformanceReport
  };
};

export default usePerformance;