
/**
 * Lullaby Clinic - Appointment Hooks
 * Custom hooks for appointment operations
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { appointmentService } from '@/lib/supabase-services';
import type { CreateAppointmentInput } from '@/types';

export const useCreateAppointment = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (appointmentData: CreateAppointmentInput) =>
      appointmentService.createAppointment(appointmentData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
      queryClient.invalidateQueries({ queryKey: ['user', 'appointments'] });
      queryClient.invalidateQueries({ queryKey: ['doctor', 'availability'] });
    }
  });
};

export const useAppointmentDetails = (appointmentId: string) => {
  return useQuery({
    queryKey: ['appointment', appointmentId],
    queryFn: () => appointmentService.getAppointmentDetails(appointmentId),
    enabled: !!appointmentId
  });
};

export const useUpdateAppointmentStatus = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ 
      appointmentId, 
      status, 
      notes 
    }: { 
      appointmentId: string; 
      status: string; 
      notes?: string 
    }) => appointmentService.updateAppointmentStatus(appointmentId, status, notes),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
      queryClient.invalidateQueries({ queryKey: ['user', 'appointments'] });
    }
  });
};
