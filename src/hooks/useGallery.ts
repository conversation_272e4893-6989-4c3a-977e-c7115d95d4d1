import { useState, useEffect, useCallback } from 'react';
import {
  fetchGalleryItems,
  fetchGalleryItemById,
  incrementViewCount,
  incrementLikeCount,
  getGalleryCategories,
  getGalleryTags,
  getGalleryStats,
  BeforeAfterGalleryItem,
  GalleryFilters,
  GallerySortOptions
} from '@/lib/supabase/gallery';

export interface UseGalleryOptions {
  initialFilters?: GalleryFilters;
  initialSort?: GallerySortOptions;
  limit?: number;
  autoFetch?: boolean;
}

export interface UseGalleryReturn {
  items: BeforeAfterGalleryItem[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  hasMore: boolean;
  filters: GalleryFilters;
  sort: GallerySortOptions;
  categories: string[];
  tags: string[];
  stats: {
    totalItems: number;
    totalViews: number;
    totalLikes: number;
    averageRating: number;
    categoryCounts: Record<string, number>;
  } | null;
  
  // Actions
  setFilters: (filters: GalleryFilters) => void;
  setSort: (sort: GallerySortOptions) => void;
  loadMore: () => Promise<void>;
  refresh: () => Promise<void>;
  viewItem: (id: string) => Promise<void>;
  likeItem: (id: string) => Promise<void>;
}

export function useGallery(options: UseGalleryOptions = {}): UseGalleryReturn {
  const {
    initialFilters = {},
    initialSort = { field: 'created_at', direction: 'desc' },
    limit = 12,
    autoFetch = true
  } = options;

  const [items, setItems] = useState<BeforeAfterGalleryItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [offset, setOffset] = useState(0);
  const [filters, setFilters] = useState<GalleryFilters>(initialFilters);
  const [sort, setSort] = useState<GallerySortOptions>(initialSort);
  const [categories, setCategories] = useState<string[]>([]);
  const [tags, setTags] = useState<string[]>([]);
  const [stats, setStats] = useState<UseGalleryReturn['stats']>(null);

  const hasMore = offset + items.length < totalCount;

  const fetchItems = useCallback(async (reset = false) => {
    try {
      setLoading(true);
      setError(null);

      const currentOffset = reset ? 0 : offset;
      const { data, count } = await fetchGalleryItems(
        filters,
        sort,
        limit,
        currentOffset
      );

      if (reset) {
        setItems(data);
        setOffset(data.length);
      } else {
        setItems(prev => [...prev, ...data]);
        setOffset(prev => prev + data.length);
      }

      setTotalCount(count);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch gallery items');
    } finally {
      setLoading(false);
    }
  }, [filters, sort, limit, offset]);

  const fetchMetadata = useCallback(async () => {
    try {
      const [categoriesData, tagsData, statsData] = await Promise.all([
        getGalleryCategories(),
        getGalleryTags(),
        getGalleryStats()
      ]);

      setCategories(categoriesData);
      setTags(tagsData);
      setStats(statsData);
    } catch (err) {
      console.error('Failed to fetch gallery metadata:', err);
    }
  }, []);

  const handleSetFilters = useCallback((newFilters: GalleryFilters) => {
    setFilters(newFilters);
    setOffset(0);
  }, []);

  const handleSetSort = useCallback((newSort: GallerySortOptions) => {
    setSort(newSort);
    setOffset(0);
  }, []);

  const loadMore = useCallback(async () => {
    if (!hasMore || loading) return;
    await fetchItems(false);
  }, [hasMore, loading, fetchItems]);

  const refresh = useCallback(async () => {
    setOffset(0);
    await fetchItems(true);
  }, [fetchItems]);

  const viewItem = useCallback(async (id: string) => {
    try {
      await incrementViewCount(id);
      // Update local state
      setItems(prev => prev.map(item => 
        item.id === id 
          ? { ...item, view_count: item.view_count + 1 }
          : item
      ));
    } catch (err) {
      console.error('Failed to increment view count:', err);
    }
  }, []);

  const likeItem = useCallback(async (id: string) => {
    try {
      await incrementLikeCount(id);
      // Update local state
      setItems(prev => prev.map(item => 
        item.id === id 
          ? { ...item, like_count: item.like_count + 1 }
          : item
      ));
    } catch (err) {
      console.error('Failed to increment like count:', err);
      throw err;
    }
  }, []);

  // Initial fetch
  useEffect(() => {
    if (autoFetch) {
      fetchItems(true);
      fetchMetadata();
    }
  }, []);

  // Refetch when filters or sort change
  useEffect(() => {
    if (autoFetch) {
      fetchItems(true);
    }
  }, [filters, sort]);

  return {
    items,
    loading,
    error,
    totalCount,
    hasMore,
    filters,
    sort,
    categories,
    tags,
    stats,
    setFilters: handleSetFilters,
    setSort: handleSetSort,
    loadMore,
    refresh,
    viewItem,
    likeItem
  };
}

export interface UseGalleryItemOptions {
  id: string;
  autoFetch?: boolean;
}

export interface UseGalleryItemReturn {
  item: BeforeAfterGalleryItem | null;
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
  viewItem: () => Promise<void>;
  likeItem: () => Promise<void>;
}

export function useGalleryItem(options: UseGalleryItemOptions): UseGalleryItemReturn {
  const { id, autoFetch = true } = options;
  
  const [item, setItem] = useState<BeforeAfterGalleryItem | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchItem = useCallback(async () => {
    if (!id) return;

    try {
      setLoading(true);
      setError(null);
      const data = await fetchGalleryItemById(id);
      setItem(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch gallery item');
    } finally {
      setLoading(false);
    }
  }, [id]);

  const viewItem = useCallback(async () => {
    if (!item) return;

    try {
      await incrementViewCount(item.id);
      setItem(prev => prev ? { ...prev, view_count: prev.view_count + 1 } : null);
    } catch (err) {
      console.error('Failed to increment view count:', err);
    }
  }, [item]);

  const likeItem = useCallback(async () => {
    if (!item) return;

    try {
      await incrementLikeCount(item.id);
      setItem(prev => prev ? { ...prev, like_count: prev.like_count + 1 } : null);
    } catch (err) {
      console.error('Failed to increment like count:', err);
      throw err;
    }
  }, [item]);

  useEffect(() => {
    if (autoFetch && id) {
      fetchItem();
    }
  }, [id, autoFetch, fetchItem]);

  return {
    item,
    loading,
    error,
    refresh: fetchItem,
    viewItem,
    likeItem
  };
}
