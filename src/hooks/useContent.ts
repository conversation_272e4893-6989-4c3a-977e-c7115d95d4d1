
/**
 * Lullaby Clinic - Content Hooks
 * Custom hooks for content operations
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import { useQuery } from '@tanstack/react-query';
import { contentService } from '@/lib/supabase-services';

interface BlogPostFilters {
  category?: string;
  difficulty?: string;
}

export const useBlogPosts = (filters?: {
  category?: string;
  difficulty?: string;
  language?: string;
  featured?: boolean;
  limit?: number;
}) => {
  return useQuery({
    queryKey: ['blog', 'posts', filters],
    queryFn: () => contentService.getBlogPosts(filters),
    staleTime: 10 * 60 * 1000 // 10 minutes
  });
};

export const useBlogPost = (slug: string) => {
  return useQuery({
    queryKey: ['blog', 'post', slug],
    queryFn: () => contentService.getBlogPostBySlug(slug),
    enabled: !!slug,
    staleTime: 10 * 60 * 1000 // 10 minutes
  });
};

export const useSearchBlogPosts = (query: string, filters?: BlogPostFilters) => {
  return useQuery({
    queryKey: ['blog', 'search', query, filters],
    queryFn: () => contentService.searchBlogPosts(query, filters),
    enabled: !!query,
    staleTime: 5 * 60 * 1000 // 5 minutes
  });
};
