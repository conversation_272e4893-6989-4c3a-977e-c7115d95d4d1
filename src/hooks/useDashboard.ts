
/**
 * Lullaby Clinic - Dashboard Hooks
 * Custom hooks for dashboard operations
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import { useQuery } from '@tanstack/react-query';
import { dashboardService } from '@/lib/supabase-services';

export const useDashboardStats = () => {
  return useQuery({
    queryKey: ['dashboard', 'stats'],
    queryFn: () => dashboardService.getStats(),
    staleTime: 2 * 60 * 1000 // 2 minutes
  });
};
