
/**
 * Lullaby Clinic - Realtime Hooks
 * Custom hooks for real-time updates
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import { useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import type { RealtimePostgresChangesPayload } from '@supabase/realtime-js';

export const useRealtimeAppointments = (
  callback?: (payload: RealtimePostgresChangesPayload<Record<string, unknown>>) => void
) => {
  useEffect(() => {
    const channel = supabase
      .channel('appointments-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'appointments'
        },
        (payload) => {
          console.log('Appointment updated:', payload);
          callback?.(payload);
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [callback]);
};

export const useRealtimePayments = (
  callback?: (payload: RealtimePostgresChangesPayload<Record<string, unknown>>) => void
) => {
  useEffect(() => {
    const channel = supabase
      .channel('payments-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'payments'
        },
        (payload) => {
          console.log('Payment updated:', payload);
          callback?.(payload);
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [callback]);
};
