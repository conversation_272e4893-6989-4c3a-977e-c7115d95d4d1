
/**
 * Lullaby Clinic - Supabase Hooks Index
 * Re-exports all Supabase-related hooks for convenience
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

// Authentication hooks
export { useAuth } from './useAuth';

// User profile hooks
export { 
  useUserProfile, 
  useUpdateProfile, 
  useUserAppointments 
} from './useUserProfile';

// Service hooks
export { 
  useServiceCategories, 
  useServices, 
  useService, 
  useSearchServices 
} from './useServices';

// Doctor hooks
export { 
  useDoctors, 
  useDoctorAvailability 
} from './useDoctors';

// Appointment hooks
export { 
  useCreateAppointment, 
  useAppointmentDetails, 
  useUpdateAppointmentStatus 
} from './useAppointments';

// Payment hooks
export { 
  useCreatePaymentIntent, 
  useUpdatePaymentStatus 
} from './usePayments';

// Review hooks
export { 
  useCreateReview, 
  useServiceReviews 
} from './useReviews';

// Newsletter hooks
export { 
  useNewsletterSubscribe, 
  useNewsletterUnsubscribe 
} from './useNewsletter';

// Content hooks
export { 
  useBlogPosts, 
  useBlogPost, 
  useSearchBlogPosts 
} from './useContent';

// Promotion hooks
export { 
  useActivePromotions, 
  useValidatePromoCode 
} from './usePromotions';

// Dashboard hooks
export { useDashboardStats } from './useDashboard';

// Real-time hooks
export { 
  useRealtimeAppointments, 
  useRealtimePayments 
} from './useRealtime';

// Utility hooks
export { useSupabaseOperation } from './useSupabaseOperation';

// Re-export commonly used types for convenience
export type { 
  UserProfile, 
  Service, 
  Doctor, 
  Appointment, 
  BlogPost, 
  DashboardStats 
} from '@/types';
