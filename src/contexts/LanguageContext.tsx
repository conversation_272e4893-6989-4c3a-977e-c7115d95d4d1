
import React, { createContext, useContext, useEffect, useState } from 'react';
import { translations } from '@/utils/translations';

export type Language = 'th' | 'en' | 'zh';

interface LanguageContextType {
  currentLanguage: Language;
  changeLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

interface LanguageProviderProps {
  children: React.ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState<Language>('en');

  useEffect(() => {
    // Check URL for language parameter
    const urlParams = new URLSearchParams(window.location.search);
    const langParam = urlParams.get('lang') as Language;
    
    // Check localStorage for saved language preference
    const savedLang = localStorage.getItem('language') as Language;
    
    // Check browser language
    const browserLang = navigator.language.split('-')[0] as Language;
    
    // Priority: URL param > localStorage > browser > default (en)
    const detectedLanguage = langParam || savedLang || 
      (['th', 'en', 'zh'].includes(browserLang) ? browserLang : 'en');
    
    setCurrentLanguage(detectedLanguage);
    
    // Save to localStorage
    localStorage.setItem('language', detectedLanguage);
    
    // Apply font class to body
    applyLanguageFont(detectedLanguage);
  }, []);

  const changeLanguage = (lang: Language) => {
    setCurrentLanguage(lang);
    localStorage.setItem('language', lang);
    applyLanguageFont(lang);
    
    // Update URL parameter
    const url = new URL(window.location.href);
    url.searchParams.set('lang', lang);
    window.history.replaceState({}, '', url.toString());
  };

  const applyLanguageFont = (lang: Language) => {
    // Remove existing font classes
    document.body.classList.remove('font-kanit', 'font-poppins');
    
    // Apply appropriate font class
    if (lang === 'th') {
      document.body.classList.add('font-kanit');
    } else {
      document.body.classList.add('font-poppins');
    }
  };

  // Translation function - uses imported translations
  const t = (key: string): string => {
    try {
      const keys = key.split('.');
      let value: unknown = translations[currentLanguage];
      
      for (const k of keys) {
        if (typeof value === 'object' && value !== null && k in value) {
          value = (value as Record<string, unknown>)[k];
        } else {
          return key;
        }
      }
      
      return typeof value === 'string' ? value : key;
    } catch (error) {
      console.warn(`Translation not found for key: ${key}`);
      return key;
    }
  };

  return (
    <LanguageContext.Provider value={{ currentLanguage, changeLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
