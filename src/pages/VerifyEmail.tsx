/**
 * Lullaby Clinic - Email Verification Page
 * Page for users to verify their email after signup
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import React, { useEffect, useState } from 'react';
import { CheckCircle, Mail, RefreshCw } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useLanguage } from '@/contexts/LanguageContext';
import { auth } from '@/lib/supabase';
import { Link, useSearchParams, useNavigate } from 'react-router-dom';

const VerifyEmail: React.FC = () => {
  const { t } = useLanguage();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [isResending, setIsResending] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  
  const email = searchParams.get('email') || '';

  useEffect(() => {
    // Check if user is already verified
    auth.getUser().then(({ data: { user } }) => {
      if (user && user.email_confirmed_at) {
        setIsVerified(true);
        setTimeout(() => {
          navigate('/dashboard');
        }, 2000);
      }
    });

    // Listen for auth state changes (email confirmation)
    const { data: { subscription } } = auth.onAuthStateChange((event, session) => {
      if (event === 'SIGNED_IN' && session?.user?.email_confirmed_at) {
        setIsVerified(true);
        toast({
          title: t('auth.emailVerified'),
          description: t('auth.welcomeToLullaby')
        });
        setTimeout(() => {
          navigate('/dashboard');
        }, 2000);
      }
    });

    return () => subscription.unsubscribe();
  }, [navigate, t, toast]);

  const handleResendEmail = async () => {
    if (!email) {
      toast({
        title: t('auth.error'),
        description: t('auth.emailRequired'),
        variant: 'destructive'
      });
      return;
    }

    setIsResending(true);
    
    try {
      const { error } = await auth.resend({
        type: 'signup',
        email: email
      });

      if (error) {
        toast({
          title: t('auth.error'),
          description: error.message,
          variant: 'destructive'
        });
      } else {
        toast({
          title: t('auth.emailResent'),
          description: t('auth.checkEmailAgain')
        });
      }
    } catch (error) {
      toast({
        title: t('auth.error'),
        description: t('auth.resendFailed'),
        variant: 'destructive'
      });
    } finally {
      setIsResending(false);
    }
  };

  if (isVerified) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6 text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <h2 className="text-2xl font-bold text-green-600 mb-2">
              {t('auth.emailVerified')}
            </h2>
            <p className="text-gray-600 mb-4">
              {t('auth.redirectingToDashboard')}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Mail className="w-8 h-8 text-blue-600" />
          </div>
          <CardTitle className="text-2xl font-bold">
            {t('auth.verifyEmail')}
          </CardTitle>
          <CardDescription>
            {t('auth.verificationEmailSent')}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {email && (
            <Alert>
              <AlertDescription>
                {t('auth.checkEmailAt')} <strong>{email}</strong>
              </AlertDescription>
            </Alert>
          )}

          <div className="text-sm text-gray-600 space-y-2">
            <p>{t('auth.verificationInstructions')}</p>
            <ul className="list-disc list-inside space-y-1 text-xs">
              <li>{t('auth.checkSpamFolder')}</li>
              <li>{t('auth.emailMayTakeMinutes')}</li>
              <li>{t('auth.clickLinkInEmail')}</li>
            </ul>
          </div>

          <div className="space-y-3">
            <Button
              onClick={handleResendEmail}
              disabled={isResending || !email}
              variant="outline"
              className="w-full"
            >
              {isResending ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  {t('auth.resending')}
                </>
              ) : (
                <>
                  <Mail className="w-4 h-4 mr-2" />
                  {t('auth.resendEmail')}
                </>
              )}
            </Button>

            <div className="text-center">
              <Link 
                to="/auth/login" 
                className="text-sm text-primary hover:underline"
              >
                {t('auth.backToLogin')}
              </Link>
            </div>
          </div>

          <div className="text-xs text-gray-500 text-center">
            {t('auth.emailVerificationHelp')}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default VerifyEmail;