
import React from 'react';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import SEOHead from '@/components/SEOHead';
import ErrorBoundary from '@/components/ErrorBoundary';
import { useLanguage } from '@/contexts/LanguageContext';
import { MapPin, Phone, Mail, Clock, Award, Users, Heart, Star } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

const About = () => {
  const { t, currentLanguage } = useLanguage();

  const services = [
    {
      id: 'facial',
      title: currentLanguage === 'th' ? 'ฟื้นฟูผิวหน้า' : currentLanguage === 'zh' ? '面部护理' : 'Facial Treatment',
      description: currentLanguage === 'th' ? 'ดูแลผิวหน้าด้วยเทคโนโลยีล่าสุด' : currentLanguage === 'zh' ? '先进的面部护理技术' : 'Advanced facial care with latest technology',
      icon: '✨'
    },
    {
      id: 'botox',
      title: currentLanguage === 'th' ? 'โบท็อกซ์' : currentLanguage === 'zh' ? '肉毒杆菌' : 'Botox',
      description: currentLanguage === 'th' ? 'ลดริ้วรอย ยกกระชับหน้า' : currentLanguage === 'zh' ? '减少皱纹，紧致面部' : 'Reduce wrinkles and lift facial contours',
      icon: '💉'
    },
    {
      id: 'laser',
      title: currentLanguage === 'th' ? 'เลเซอร์ผิว' : currentLanguage === 'zh' ? '激光治疗' : 'Laser Treatment',
      description: currentLanguage === 'th' ? 'กำจัดจุดด่างดำ ฝ้า กระ' : currentLanguage === 'zh' ? '去除色斑和黑斑' : 'Remove dark spots and pigmentation',
      icon: '⚡'
    },
    {
      id: 'filler',
      title: currentLanguage === 'th' ? 'ฟิลเลอร์' : currentLanguage === 'zh' ? '玻尿酸填充' : 'Dermal Fillers',
      description: currentLanguage === 'th' ? 'ฟิลเลอร์อิตาลี Menarini' : currentLanguage === 'zh' ? '意大利Menarini玻尿酸' : 'Italian Menarini Fillers',
      icon: '💧'
    },
    {
      id: 'hydrobooster',
      title: currentLanguage === 'th' ? 'ไฮโดรบูสเตอร์' : currentLanguage === 'zh' ? '水光针' : 'Hydrobooster',
      description: currentLanguage === 'th' ? 'เติมความชุ่มชื้นให้ผิว' : currentLanguage === 'zh' ? '深层补水护理' : 'Deep hydration treatment',
      icon: '💎'
    },
    {
      id: 'threads',
      title: currentLanguage === 'th' ? 'เธรดลิฟท์' : currentLanguage === 'zh' ? '线雕提升' : 'Thread Lift',
      description: currentLanguage === 'th' ? 'เธรดอิตาลี ยกกระชับ' : currentLanguage === 'zh' ? '意大利线雕提升' : 'Italian threads for lifting',
      icon: '🧵'
    }
  ];

  const operatingHours = [
    { day: currentLanguage === 'th' ? 'จันทร์' : currentLanguage === 'zh' ? '周一' : 'Monday', hours: currentLanguage === 'th' ? 'ปิด' : currentLanguage === 'zh' ? '休息' : 'Closed' },
    { day: currentLanguage === 'th' ? 'อังคาร' : currentLanguage === 'zh' ? '周二' : 'Tuesday', hours: '11:00 - 20:30' },
    { day: currentLanguage === 'th' ? 'พุธ' : currentLanguage === 'zh' ? '周三' : 'Wednesday', hours: '11:00 - 20:30' },
    { day: currentLanguage === 'th' ? 'พฤหัสบดี' : currentLanguage === 'zh' ? '周四' : 'Thursday', hours: '11:00 - 20:30' },
    { day: currentLanguage === 'th' ? 'ศุกร์' : currentLanguage === 'zh' ? '周五' : 'Friday', hours: '11:00 - 20:30' },
    { day: currentLanguage === 'th' ? 'เสาร์' : currentLanguage === 'zh' ? '周六' : 'Saturday', hours: '11:00 - 20:30' },
    { day: currentLanguage === 'th' ? 'อาทิตย์' : currentLanguage === 'zh' ? '周日' : 'Sunday', hours: '11:00 - 20:30' }
  ];

  return (
    <ErrorBoundary level="page" showDetails={import.meta.env.DEV}>
      <SEOHead 
        lang={currentLanguage}
        title={`${t('about.hero.title')} | Lullaby Clinic`}
        description={t('about.hero.subtitle')}
        keywords="about lullaby clinic, medical aesthetics, beauty clinic, professional doctors, chonburi"
      />
      
      <div className="min-h-screen bg-background">
        <Navigation />

        <main className="px-4 pt-32 pb-12">
          {/* Hero Section */}
          <section className="relative bg-gradient-soft py-16 mb-16">
            <div className="container mx-auto px-4 text-center">
              <div className="inline-flex items-center bg-primary-100 text-primary-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
                <Heart className="w-4 h-4 mr-2" />
                {currentLanguage === 'th' ? 'เกี่ยวกับเรา' : currentLanguage === 'zh' ? '关于我们' : 'About Us'}
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
                {currentLanguage === 'th' ? 'ลัลลาบายคลินิก' : currentLanguage === 'zh' ? '摇篮诊所' : 'Lullaby Clinic'}
              </h1>
              <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                {currentLanguage === 'th' 
                  ? 'ให้บริการด้านความงามและผิวพรรณครบวงจร ด้วยผลิตภัณฑ์คุณภาพสูงจากอิตาลี และทีมแพทย์ผู้เชี่ยวชาญ' 
                  : currentLanguage === 'zh' 
                  ? '提供全面的美容和护肤服务，使用来自意大利的高品质产品和专业医疗团队'
                  : 'Comprehensive beauty and skincare services with high-quality Italian products and expert medical team'
                }
              </p>
            </div>
          </section>

          <div className="container mx-auto space-y-16">
            {/* Services Grid */}
            <section>
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-foreground mb-4">
                  {currentLanguage === 'th' ? 'บริการของเรา' : currentLanguage === 'zh' ? '我们的服务' : 'Our Services'}
                </h2>
                <p className="text-muted-foreground">
                  {currentLanguage === 'th' ? 'บริการความงามครบวงจร ด้วยเทคโนโลยีล่าสุด' : currentLanguage === 'zh' ? '全面的美容服务，采用最新技术' : 'Comprehensive beauty services with latest technology'}
                </p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {services.map((service) => (
                  <Card key={service.id} className="bg-white border-gray-200 shadow-lg hover:shadow-xl transition-shadow">
                    <CardHeader className="text-center">
                      <div className="text-4xl mb-4">{service.icon}</div>
                      <CardTitle className="text-foreground">{service.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted-foreground text-center">{service.description}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </section>

            {/* Certifications */}
            <section className="bg-gradient-soft py-16 rounded-2xl">
              <div className="container mx-auto px-4 text-center">
                <div className="inline-flex items-center bg-primary-100 text-primary-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
                  <Award className="w-4 h-4 mr-2" />
                  {currentLanguage === 'th' ? 'การรับรอง' : currentLanguage === 'zh' ? '认证资质' : 'Certifications'}
                </div>
                <h2 className="text-3xl font-bold text-foreground mb-6">
                  {currentLanguage === 'th' ? 'ผลิตภัณฑ์ Menarini แท้' : currentLanguage === 'zh' ? '正品Menarini产品' : 'Authentic Menarini Products'}
                </h2>
                <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
                  {currentLanguage === 'th' 
                    ? 'เราใช้ผลิตภัณฑ์ Menarini จากอิตาลี พร้อมใบรับรองความแท้ เพื่อความปลอดภัยและประสิทธิภาพสูงสุด'
                    : currentLanguage === 'zh'
                    ? '我们使用来自意大利的Menarini产品，附有真品认证，确保最高的安全性和效果'
                    : 'We use authentic Menarini products from Italy with certificates of authenticity for maximum safety and effectiveness'
                  }
                </p>
                <div className="flex justify-center items-center space-x-8">
                  <div className="bg-white p-6 rounded-lg shadow-lg">
                    <Award className="w-12 h-12 text-primary-600 mx-auto mb-2" />
                    <p className="text-sm font-medium text-foreground">
                      {currentLanguage === 'th' ? 'ใบรับรองแท้' : currentLanguage === 'zh' ? '真品认证' : 'Certificate of Authenticity'}
                    </p>
                  </div>
                </div>
              </div>
            </section>

            {/* Location & Hours */}
            <section className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Contact Info */}
              <Card className="bg-white border-gray-200 shadow-lg">
                <CardHeader>
                  <CardTitle className="text-foreground flex items-center">
                    <MapPin className="w-5 h-5 mr-2" />
                    {currentLanguage === 'th' ? 'ที่อยู่และติดต่อ' : currentLanguage === 'zh' ? '地址和联系方式' : 'Location & Contact'}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <MapPin className="w-5 h-5 text-primary-600 mt-1" />
                    <div>
                      <p className="font-medium text-foreground">
                        {currentLanguage === 'th' ? 'ที่อยู่' : currentLanguage === 'zh' ? '地址' : 'Address'}
                      </p>
                      <p className="text-muted-foreground">
                        170/25 หมู่ 3 ต.เสม็ด<br />
                        ถ.พระยาสัจจา อ.เมือง<br />
                        จ.ชลบุรี 20000
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Phone className="w-5 h-5 text-primary-600" />
                    <div>
                      <p className="font-medium text-foreground">
                        {currentLanguage === 'th' ? 'โทรศัพท์' : currentLanguage === 'zh' ? '电话' : 'Phone'}
                      </p>
                      <p className="text-muted-foreground">+66 64 646 8656</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Mail className="w-5 h-5 text-primary-600" />
                    <div>
                      <p className="font-medium text-foreground">
                        {currentLanguage === 'th' ? 'อีเมล' : currentLanguage === 'zh' ? '邮箱' : 'Email'}
                      </p>
                      <p className="text-muted-foreground"><EMAIL></p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Operating Hours */}
              <Card className="bg-white border-gray-200 shadow-lg">
                <CardHeader>
                  <CardTitle className="text-foreground flex items-center">
                    <Clock className="w-5 h-5 mr-2" />
                    {currentLanguage === 'th' ? 'เวลาทำการ' : currentLanguage === 'zh' ? '营业时间' : 'Operating Hours'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {operatingHours.map((schedule, index) => (
                      <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                        <span className="font-medium text-foreground">{schedule.day}</span>
                        <span className={`${schedule.hours === 'ปิด' || schedule.hours === '休息' || schedule.hours === 'Closed' ? 'text-red-500' : 'text-muted-foreground'}`}>
                          {schedule.hours}
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </section>

            {/* Google Maps */}
            <section>
              <Card className="bg-white border-gray-200 shadow-lg">
                <CardHeader>
                  <CardTitle className="text-foreground">
                    {currentLanguage === 'th' ? 'แผนที่' : currentLanguage === 'zh' ? '地图' : 'Map'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="w-full h-64 bg-gray-200 rounded-lg flex items-center justify-center">
                    <p className="text-muted-foreground">
                      {currentLanguage === 'th' ? 'แผนที่ Google Maps จะแสดงที่นี่' : currentLanguage === 'zh' ? 'Google地图将在此显示' : 'Google Maps will be displayed here'}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </section>

            {/* Social Media & Contact */}
            <section className="bg-gradient-soft py-16 rounded-2xl text-center">
              <div className="container mx-auto px-4">
                <h2 className="text-3xl font-bold text-foreground mb-6">
                  {currentLanguage === 'th' ? 'ติดต่อเรา' : currentLanguage === 'zh' ? '联系我们' : 'Connect With Us'}
                </h2>
                <p className="text-muted-foreground mb-8">
                  {currentLanguage === 'th' ? 'ติดตามเราบนโซเชียลมีเดีย หรือติดต่อเพื่อนัดหมาย' : currentLanguage === 'zh' ? '在社交媒体上关注我们，或联系我们预约' : 'Follow us on social media or contact us for appointments'}
                </p>
                <div className="flex justify-center space-x-4 mb-8">
                  <Button variant="outline" className="bg-white">
                    Facebook
                  </Button>
                  <Button variant="outline" className="bg-white">
                    TikTok
                  </Button>
                  <Button variant="outline" className="bg-white">
                    Line
                  </Button>
                </div>
                <Button size="lg" className="bg-primary-600 hover:bg-primary-700">
                  {currentLanguage === 'th' ? 'จองนัดหมาย' : currentLanguage === 'zh' ? '预约挂号' : 'Book Appointment'}
                </Button>
              </div>
            </section>
          </div>
        </main>

        <Footer translations={{
          footer: {
            partners: t('footer.partners'),
            description: t('footer.description'),
            address: '170/25 หมู่ 3 ต.เสม็ด ถ.พระยาสัจจา อ.เมือง จ.ชลบุรี 20000',
            phone: '+66 64 646 8656',
            email: '<EMAIL>',
            quickLinks: t('footer.quickLinks'),
            services: t('footer.services'),
            rights: t('footer.rights'),
            socialMedia: {
              facebook: 'Facebook',
              line: 'Line',
              googleMaps: 'Google Maps'
            }
          },
          nav: {
            home: t('nav.home'),
            services: t('nav.services'),
            about: t('nav.about'),
            gallery: t('nav.gallery'),
            blog: t('nav.blog')
          },
          services: {
            facial: {
              title: t('services.facial.title')
            },
            botox: {
              title: t('services.botox.title')
            },
            laser: {
              title: t('services.laser.title')
            }
          }
        }} />
      </div>
    </ErrorBoundary>
  );
};

export default About;
