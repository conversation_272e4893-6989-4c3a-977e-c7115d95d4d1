import React, { memo } from 'react';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import SEOHead from '@/components/SEOHead';
import ErrorBoundary from '@/components/ErrorBoundary';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, Award, GraduationCap, Stethoscope, ArrowRight } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

// Doctor data
const doctorsData = [
  {
    id: 'dr_smith',
    initials: 'DS',
    gradient: 'from-pink-400 to-purple-600'
  },
  {
    id: 'dr_lee',
    initials: 'DL',
    gradient: 'from-blue-400 to-indigo-600'
  },
  {
    id: 'dr_chen',
    initials: 'DC',
    gradient: 'from-green-400 to-teal-600'
  }
];

// Modern card component matching home page design
const ModernCard = memo(({ 
  children, 
  className = '', 
  hover = false 
}: { 
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
}) => (
  <Card 
    className={`
      bg-white border border-gray-200 shadow-lg rounded-lg
      ${hover ? 'hover:shadow-xl hover:scale-105 transition-all duration-300' : ''}
      ${className}
    `}
  >
    <CardContent className="p-8">
      {children}
    </CardContent>
  </Card>
));

ModernCard.displayName = 'ModernCard';

// Doctor Card Component
const DoctorCard = memo(({ doctor }: { doctor: typeof doctorsData[0] }) => {
  const { t } = useLanguage();
  
  return (
    <ModernCard hover className="h-full">
      <div className="text-center space-y-6">
        <Avatar className={`w-32 h-32 mx-auto bg-gradient-to-br ${doctor.gradient} text-foreground`}>
          <AvatarFallback className="text-3xl font-bold">
            {doctor.initials}
          </AvatarFallback>
        </Avatar>
        
        <div>
          <h3 className="text-2xl font-bold text-foreground mb-2">
            {t(`doctorsPage.team.${doctor.id}.name`)}
          </h3>
          <p className="text-muted-foreground text-lg font-medium mb-4">
            {t(`doctorsPage.team.${doctor.id}.role`)}
          </p>
        </div>

        <div className="space-y-4 text-left">
          {/* Experience */}
          <div className="flex items-start gap-3">
            <Calendar className="w-5 h-5 text-pink-300 mt-1 flex-shrink-0" />
            <div>
              <p className="text-foreground/70 text-sm">Experience</p>
              <p className="text-foreground font-medium">
                {t(`doctorsPage.team.${doctor.id}.experience`)}
              </p>
            </div>
          </div>

          {/* Specialties */}
          <div className="flex items-start gap-3">
            <Stethoscope className="w-5 h-5 text-green-300 mt-1 flex-shrink-0" />
            <div>
              <p className="text-foreground/70 text-sm mb-2">Specialties</p>
              <div className="flex flex-wrap gap-2">
                {['Dermatology', 'Aesthetic Medicine', 'Anti-Aging'].map((specialty: string, idx: number) => (
                  <Badge key={idx} variant="secondary" className="bg-white/20 text-foreground text-xs">
                    {specialty}
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          {/* Education */}
          <div className="flex items-start gap-3">
            <GraduationCap className="w-5 h-5 text-blue-300 mt-1 flex-shrink-0" />
            <div>
              <p className="text-foreground/70 text-sm mb-2">Education</p>
              <ul className="space-y-1">
                {['MD, Harvard Medical School', 'Dermatology Residency, Johns Hopkins', 'Fellowship in Aesthetic Medicine'].map((edu: string, idx: number) => (
                  <li key={idx} className="text-foreground text-sm">
                    • {edu}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        <div className="pt-4">
          <p className="text-muted-foreground text-sm leading-relaxed mb-4">
            {t(`doctorsPage.team.${doctor.id}.description`)}
          </p>
          
          <Button 
            className="w-full bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-foreground border-0"
          >
            Book Consultation <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
    </ModernCard>
  );
});

DoctorCard.displayName = 'DoctorCard';

const Doctors = () => {
  const { t, currentLanguage } = useLanguage();

  return (
    <ErrorBoundary level="page" showDetails={import.meta.env.DEV}>
      <SEOHead 
        lang={currentLanguage}
        title={`${t('doctorsPage.hero.title')} | Lullaby Clinic`}
        description={t('doctorsPage.hero.subtitle')}
        keywords="lullaby clinic doctors, aesthetic medicine specialists, dermatology experts, beauty doctors"
      />
      
      <div className="min-h-screen bg-background">

        {/* Navigation */}
        <Navigation />

        <main className="container mx-auto px-4 pt-32 pb-12 space-y-16">
          {/* Hero Section */}
          <section className="relative bg-gradient-soft min-h-[400px] flex items-center rounded-lg">
            <div className="container mx-auto px-4 py-16 text-center">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-6">
                {t('doctorsPage.hero.title')}
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                {t('doctorsPage.hero.subtitle')}
              </p>
            </div>
          </section>

          {/* Doctors Grid */}
          <section className="space-y-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {doctorsData.map((doctor) => (
                <DoctorCard key={doctor.id} doctor={doctor} />
              ))}
            </div>
          </section>

          {/* Why Our Doctors Section */}
          <section className="space-y-8">
            <h2 className="text-4xl font-bold text-foreground text-center">
              Why Choose Our Medical Team?
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <ModernCard hover>
                <div className="text-center space-y-4">
                  <div className="text-4xl">🏆</div>
                  <h3 className="text-lg font-bold text-foreground">Board Certified</h3>
                  <p className="text-muted-foreground text-sm">
                    All our doctors are board-certified specialists
                  </p>
                </div>
              </ModernCard>
              
              <ModernCard hover>
                <div className="text-center space-y-4">
                  <div className="text-4xl">📚</div>
                  <h3 className="text-lg font-bold text-foreground">Continuous Learning</h3>
                  <p className="text-muted-foreground text-sm">
                    Regular training in latest techniques and technologies
                  </p>
                </div>
              </ModernCard>
              
              <ModernCard hover>
                <div className="text-center space-y-4">
                  <div className="text-4xl">🌟</div>
                  <h3 className="text-lg font-bold text-foreground">Award Winners</h3>
                  <p className="text-muted-foreground text-sm">
                    Recognized for excellence in aesthetic medicine
                  </p>
                </div>
              </ModernCard>
              
              <ModernCard hover>
                <div className="text-center space-y-4">
                  <div className="text-4xl">❤️</div>
                  <h3 className="text-lg font-bold text-foreground">Patient-Centered</h3>
                  <p className="text-muted-foreground text-sm">
                    Personalized care for every patient's unique needs
                  </p>
                </div>
              </ModernCard>
            </div>
          </section>

          {/* Appointment CTA Section */}
          <section className="text-center">
            <ModernCard>
              <div className="max-w-2xl mx-auto space-y-6">
                <h2 className="text-3xl font-bold text-foreground">
                  {t('doctorsPage.appointment.title')}
                </h2>
                <p className="text-xl text-muted-foreground">
                  Schedule a consultation with our expert medical team to discuss your beauty goals
                </p>
                <Button 
                  size="lg"
                  className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-foreground border-0 px-8 py-4 text-lg"
                >
                  {t('doctorsPage.appointment.button')} <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </div>
            </ModernCard>
          </section>

          {/* Clinic Standards Section */}
          <section className="space-y-8">
            <h2 className="text-4xl font-bold text-foreground text-center">
              Our Medical Standards
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <ModernCard hover>
                <div className="text-center space-y-4">
                  <div className="text-4xl">🔬</div>
                  <h3 className="text-xl font-bold text-foreground">Evidence-Based Practice</h3>
                  <p className="text-muted-foreground">
                    All treatments are based on scientific research and proven methodologies
                  </p>
                </div>
              </ModernCard>
              
              <ModernCard hover>
                <div className="text-center space-y-4">
                  <div className="text-4xl">🛡️</div>
                  <h3 className="text-xl font-bold text-foreground">Safety First</h3>
                  <p className="text-muted-foreground">
                    Comprehensive safety protocols and sterile environment for all procedures
                  </p>
                </div>
              </ModernCard>
              
              <ModernCard hover>
                <div className="text-center space-y-4">
                  <div className="text-4xl">✨</div>
                  <h3 className="text-xl font-bold text-foreground">Natural Results</h3>
                  <p className="text-muted-foreground">
                    Focus on enhancing your natural beauty with subtle, long-lasting results
                  </p>
                </div>
              </ModernCard>
            </div>
          </section>
        </main>

        {/* Footer */}
        <Footer translations={{
          footer: {
            partners: t('footer.partners'),
            description: t('footer.description'),
            address: t('footer.address'),
            phone: t('footer.phone'),
            email: t('footer.email'),
            quickLinks: t('footer.quickLinks'),
            services: t('footer.services'),
            rights: t('footer.rights'),
            socialMedia: {
              facebook: t('footer.socialMedia.facebook'),
              line: t('footer.socialMedia.line'),
              googleMaps: t('footer.socialMedia.googleMaps')
            }
          },
          nav: {
            home: t('nav.home'),
            services: t('nav.services'),
            about: t('nav.about'),
            gallery: t('nav.gallery'),
            blog: t('nav.blog')
          },
          services: {
            facial: {
              title: t('services.facial.title')
            },
            botox: {
              title: t('services.botox.title')
            },
            laser: {
              title: t('services.laser.title')
            }
          }
        }} />
      </div>
    </ErrorBoundary>
  );
};

export default Doctors;
