
import React, { memo, useState } from 'react';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import SEOHead from '@/components/SEOHead';
import ErrorBoundary from '@/components/ErrorBoundary';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Check, Star, Crown } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

// Pricing data
const pricingPlans = [
  {
    id: 'basic',
    icon: '✨',
    popular: false,
    services: ['facial_basic', 'consultation', 'aftercare']
  },
  {
    id: 'premium',
    icon: '💎',
    popular: true,
    services: ['facial_premium', 'botox_basic', 'consultation', 'aftercare', 'followup']
  },
  {
    id: 'luxury',
    icon: '👑',
    popular: false,
    services: ['facial_luxury', 'botox_premium', 'filler', 'laser', 'consultation', 'aftercare', 'followup', 'priority']
  }
];

// Individual services
const individualServices = [
  { id: 'facial_hydra', category: 'facial', duration: '60', price: '2500' },
  { id: 'facial_diamond', category: 'facial', duration: '90', price: '3500' },
  { id: 'botox_forehead', category: 'botox', duration: '30', price: '8000' },
  { id: 'botox_crow_feet', category: 'botox', duration: '30', price: '6000' },
  { id: 'filler_lips', category: 'filler', duration: '45', price: '15000' },
  { id: 'filler_cheeks', category: 'filler', duration: '60', price: '20000' },
  { id: 'laser_hair', category: 'laser', duration: '30', price: '3000' },
  { id: 'laser_skin', category: 'laser', duration: '45', price: '5000' }
];

// Glass effect card component
const ModernCard = memo(({ 
  children, 
  className = '', 
  hover = false 
}: { 
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
}) => (
  <Card 
    className={`
      bg-white border border-gray-200 shadow-lg rounded-lg
      ${hover ? 'hover:shadow-xl hover:scale-105 transition-all duration-300' : ''}
      ${className}
    `}
    style={{
    }}
  >
    <CardContent className="p-8">
      {children}
    </CardContent>
  </Card>
));

ModernCard.displayName = "ModernCard";;

// Pricing Plan Card
const PricingPlanCard = memo(({ plan }: { plan: typeof pricingPlans[0] }) => {
  const { t } = useLanguage();
  
  return (
    <ModernCard hover className={`relative ${plan.popular ? 'ring-2 ring-yellow-400' : ''}`}>
      {plan.popular && (
        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
          <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black font-bold px-4 py-1">
            <Star className="w-4 h-4 mr-1" />
            {t('pricing.popular')}
          </Badge>
        </div>
      )}
      
      <div className="text-center space-y-6">
        <div className="text-6xl">{plan.icon}</div>
        
        <div>
          <h3 className="text-2xl font-bold text-foreground mb-2">
            {t(`pricing.plans.${plan.id}.name`)}
          </h3>
          <p className="text-muted-foreground">
            {t(`pricing.plans.${plan.id}.description`)}
          </p>
        </div>
        
        <div className="text-center">
          <div className="text-4xl font-bold text-foreground mb-1">
            {t(`pricing.plans.${plan.id}.price`)}
          </div>
          <div className="text-foreground/60">
            {t(`pricing.plans.${plan.id}.period`)}
          </div>
        </div>
        
        <div className="space-y-3">
          {plan.services.map((service, idx) => (
            <div key={idx} className="flex items-center gap-3 text-muted-foreground">
              <Check className="w-5 h-5 text-green-400 flex-shrink-0" />
              <span>{t(`pricing.services.${service}`)}</span>
            </div>
          ))}
        </div>
        
        <Button 
          className={`w-full ${
            plan.popular 
              ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-black hover:from-yellow-500 hover:to-orange-600' 
              : 'bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-foreground'
          } border-0`}
        >
          {t('pricing.selectPlan')}
        </Button>
      </div>
    </ModernCard>
  );
});

PricingPlanCard.displayName = 'PricingPlanCard';

// Individual Service Card
const ServiceCard = memo(({ service }: { service: typeof individualServices[0] }) => {
  const { t } = useLanguage();
  
  return (
    <ModernCard hover>
      <div className="flex justify-between items-start mb-4">
        <div>
          <h4 className="text-lg font-semibold text-foreground mb-1">
            {t(`pricing.individual.${service.id}.name`)}
          </h4>
          <p className="text-foreground/70 text-sm">
            {t(`pricing.individual.${service.id}.description`)}
          </p>
        </div>
        <Badge variant="secondary" className="bg-white/20 text-foreground">
          {service.duration} min
        </Badge>
      </div>
      
      <div className="flex justify-between items-center">
        <div className="text-2xl font-bold text-foreground">
          ฿{service.price}
        </div>
        <Button size="sm" className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-foreground border-0">
          {t('pricing.book')}
        </Button>
      </div>
    </ModernCard>
  );
});

ServiceCard.displayName = 'ServiceCard';

const Pricing = () => {
  const { t, currentLanguage } = useLanguage();
  const [showPackages, setShowPackages] = useState(true);

  return (
    <ErrorBoundary level="page" showDetails={import.meta.env.DEV}>
      <SEOHead 
        lang={currentLanguage}
        title={`${t('pricing.hero.title')} | Lullaby Clinic`}
        description={t('pricing.hero.subtitle')}
        keywords="lullaby clinic pricing, facial prices, botox cost, beauty treatment prices"
      />
      
      <div
        className="min-h-screen bg-background"
        style={{
          background: 'bg-background',
        }}
      >
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-72 h-72 bg-pink-300/20 rounded-full blur-3xl animate-pulse"></div>
          <div
            className="absolute top-40 right-20 w-96 h-96 bg-purple-300/15 rounded-full blur-3xl animate-pulse"
            style={{ animationDelay: '2s' }}
          ></div>
          <div
            className="absolute bottom-20 left-1/3 w-80 h-80 bg-blue-300/10 rounded-full blur-3xl animate-pulse"
            style={{ animationDelay: '4s' }}
          ></div>
        </div>

        {/* Navigation */}
        <Navigation />

        <main className="container mx-auto px-4 pt-32 pb-12 space-y-16 relative z-10">
          {/* Hero Section */}
          <section className="text-center">
            <ModernCard>
              <h1 className="text-5xl md:text-6xl font-extrabold text-foreground mb-6">
                {t('pricing.hero.title')}
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                {t('pricing.hero.subtitle')}
              </p>
            </ModernCard>
          </section>

          {/* Toggle Switch */}
          <section className="text-center">
            <ModernCard>
              <div className="flex items-center justify-center gap-4">
                <span className={`text-foreground ${!showPackages ? 'opacity-50' : ''}`}>
                  {t('pricing.packages')}
                </span>
                <Switch 
                  checked={!showPackages} 
                  onCheckedChange={(checked) => setShowPackages(!checked)}
                />
                <span className={`text-foreground ${showPackages ? 'opacity-50' : ''}`}>
                  {t('pricing.individual')}
                </span>
              </div>
            </ModernCard>
          </section>

          {/* Pricing Plans */}
          {showPackages ? (
            <section className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {pricingPlans.map((plan) => (
                <PricingPlanCard key={plan.id} plan={plan} />
              ))}
            </section>
          ) : (
            <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {individualServices.map((service) => (
                <ServiceCard key={service.id} service={service} />
              ))}
            </section>
          )}

          {/* FAQ Section */}
          <section className="space-y-8">
            <h2 className="text-4xl font-bold text-foreground text-center">
              {t('pricing.faq.title')}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {[1, 2, 3, 4].map((num) => (
                <ModernCard key={num}>
                  <h3 className="text-lg font-semibold text-foreground mb-3">
                    {t(`pricing.faq.q${num}.question`)}
                  </h3>
                  <p className="text-muted-foreground">
                    {t(`pricing.faq.q${num}.answer`)}
                  </p>
                </ModernCard>
              ))}
            </div>
          </section>
        </main>

        {/* Footer */}
        <Footer translations={{
          footer: {
            partners: t('footer.partners'),
            description: t('footer.description'),
            address: t('footer.address'),
            phone: t('footer.phone'),
            email: t('footer.email'),
            quickLinks: t('footer.quickLinks'),
            services: t('footer.services'),
            rights: t('footer.rights'),
            socialMedia: {
              facebook: t('footer.socialMedia.facebook'),
              line: t('footer.socialMedia.line'),
              googleMaps: t('footer.socialMedia.googleMaps')
            }
          },
          nav: {
            home: t('nav.home'),
            services: t('nav.services'),
            about: t('nav.about'),
            gallery: t('nav.gallery'),
            blog: t('nav.blog')
          },
          services: {
            facial: {
              title: t('services.facial.title')
            },
            botox: {
              title: t('services.botox.title')
            },
            laser: {
              title: t('services.laser.title')
            }
          }
        }} />
      </div>
    </ErrorBoundary>
  );
};

export default Pricing;
