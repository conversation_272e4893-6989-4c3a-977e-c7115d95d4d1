
import React, { memo, useState } from 'react';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import SEOHead from '@/components/SEOHead';
import ErrorBoundary from '@/components/ErrorBoundary';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { useLanguage } from '@/contexts/LanguageContext';
import { Eye, Heart, Calendar, MapPin } from 'lucide-react';

// Real gallery data with treatment images
const galleryData = [
  {
    id: 'facial_acne_treatment',
    category: 'facial',
    title: 'Acne Treatment Results',
    beforeImage: 'https://images.unsplash.com/photo-1616683693504-3ea7e9ad6fec?w=400&h=300&fit=crop&crop=face',
    afterImage: 'https://images.unsplash.com/photo-1594824804732-5f8fcaf4365b?w=400&h=300&fit=crop&crop=face',
    description: 'Complete acne treatment with visible improvement in skin texture and clarity',
    duration: '3 months',
    featured: true,
    views: 1250,
    likes: 89
  },
  {
    id: 'botox_forehead_lines',
    category: 'botox',
    title: 'Forehead Lines Reduction',
    beforeImage: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=300&fit=crop&crop=face',
    afterImage: 'https://images.unsplash.com/photo-1580618672591-eb180b1a973f?w=400&h=300&fit=crop&crop=face',
    description: 'Botox treatment for forehead lines with natural-looking results',
    duration: '2 weeks',
    featured: true,
    views: 980,
    likes: 67
  },
  {
    id: 'laser_pigmentation',
    category: 'laser',
    title: 'Pigmentation Removal',
    beforeImage: 'https://images.unsplash.com/photo-1616683693504-3ea7e9ad6fec?w=400&h=300&fit=crop&crop=face',
    afterImage: 'https://images.unsplash.com/photo-1594824804732-5f8fcaf4365b?w=400&h=300&fit=crop&crop=face',
    description: 'Laser treatment for dark spots and pigmentation removal',
    duration: '6 sessions',
    featured: false,
    views: 756,
    likes: 45
  },
  {
    id: 'filler_lip_enhancement',
    category: 'filler',
    title: 'Lip Enhancement',
    beforeImage: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=300&fit=crop&crop=face',
    afterImage: 'https://images.unsplash.com/photo-1580618672591-eb180b1a973f?w=400&h=300&fit=crop&crop=face',
    description: 'Natural lip enhancement with hyaluronic acid fillers',
    duration: '1 session',
    featured: false,
    views: 892,
    likes: 78
  },
  {
    id: 'threads_face_lift',
    category: 'threads',
    title: 'Thread Lift Results',
    beforeImage: 'https://images.unsplash.com/photo-1616683693504-3ea7e9ad6fec?w=400&h=300&fit=crop&crop=face',
    afterImage: 'https://images.unsplash.com/photo-1594824804732-5f8fcaf4365b?w=400&h=300&fit=crop&crop=face',
    description: 'Non-surgical face lift using PDO threads for skin tightening',
    duration: '1 session',
    featured: true,
    views: 1100,
    likes: 95
  },
  {
    id: 'facial_hydrafacial',
    category: 'facial',
    title: 'HydraFacial Treatment',
    beforeImage: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=300&fit=crop&crop=face',
    afterImage: 'https://images.unsplash.com/photo-1580618672591-eb180b1a973f?w=400&h=300&fit=crop&crop=face',
    description: 'Deep cleansing and hydrating facial treatment',
    duration: '1 session',
    featured: false,
    views: 634,
    likes: 42
  },
  {
    id: 'botox_crows_feet',
    category: 'botox',
    title: 'Crow\'s Feet Treatment',
    beforeImage: 'https://images.unsplash.com/photo-1616683693504-3ea7e9ad6fec?w=400&h=300&fit=crop&crop=face',
    afterImage: 'https://images.unsplash.com/photo-1594824804732-5f8fcaf4365b?w=400&h=300&fit=crop&crop=face',
    description: 'Botox treatment for eye area wrinkles and fine lines',
    duration: '2 weeks',
    featured: false,
    views: 723,
    likes: 56
  },
  {
    id: 'laser_skin_resurfacing',
    category: 'laser',
    title: 'Skin Resurfacing',
    beforeImage: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=300&fit=crop&crop=face',
    afterImage: 'https://images.unsplash.com/photo-1580618672591-eb180b1a973f?w=400&h=300&fit=crop&crop=face',
    description: 'Fractional laser treatment for skin texture improvement',
    duration: '4 sessions',
    featured: true,
    views: 1050,
    likes: 83
  }
];

const Gallery = () => {
  const { t, currentLanguage } = useLanguage();
  const [activeTab, setActiveTab] = useState('all');
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const filteredItems = galleryData.filter(item => 
    activeTab === 'all' || item.category === activeTab
  );

  const GalleryCard = ({ item }: { item: typeof galleryData[0] }) => (
    <Card className="bg-white border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
      <CardContent className="p-0">
        <div className="relative">
          {item.featured && (
            <Badge className="absolute top-3 left-3 z-10 bg-pink-500 text-white">
              Featured
            </Badge>
          )}
          
          {/* Before/After Images */}
          <div className="grid grid-cols-2 gap-0">
            <Dialog>
              <DialogTrigger asChild>
                <div className="relative cursor-pointer group">
                  <picture>
                    <source 
                      srcSet={`${item.beforeImage}&w=200 200w, ${item.beforeImage}&w=400 400w`}
                      sizes="(max-width: 640px) 200px, 400px"
                    />
                    <img
                      src={item.beforeImage}
                      alt={`Before ${item.title}`}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                      loading="lazy"
                    />
                  </picture>
                  <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300" />
                  <div className="absolute bottom-2 left-2 bg-white/90 px-2 py-1 rounded text-xs font-medium text-foreground">
                    {t('galleryPage.beforeAfter').split('-')[0] || 'Before'}
                  </div>
                </div>
              </DialogTrigger>
              <DialogContent className="max-w-4xl">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground mb-2">Before</h4>
                    <img src={item.beforeImage} alt={`Before ${item.title}`} className="w-full rounded-lg" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground mb-2">After</h4>
                    <img src={item.afterImage} alt={`After ${item.title}`} className="w-full rounded-lg" />
                  </div>
                </div>
                <div className="mt-4">
                  <h3 className="text-lg font-semibold text-foreground mb-2">{item.title}</h3>
                  <p className="text-muted-foreground mb-3">{item.description}</p>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      <span>{item.duration}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Eye className="w-4 h-4" />
                      <span>{item.views.toLocaleString()}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Heart className="w-4 h-4" />
                      <span>{item.likes}</span>
                    </div>
                  </div>
                </div>
              </DialogContent>
            </Dialog>

            <Dialog>
              <DialogTrigger asChild>
                <div className="relative cursor-pointer group">
                  <picture>
                    <source 
                      srcSet={`${item.afterImage}&w=200 200w, ${item.afterImage}&w=400 400w`}
                      sizes="(max-width: 640px) 200px, 400px"
                    />
                    <img
                      src={item.afterImage}
                      alt={`After ${item.title}`}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                      loading="lazy"
                    />
                  </picture>
                  <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300" />
                  <div className="absolute bottom-2 right-2 bg-white/90 px-2 py-1 rounded text-xs font-medium text-foreground">
                    {t('galleryPage.beforeAfter').split('-')[1] || 'After'}
                  </div>
                </div>
              </DialogTrigger>
              <DialogContent className="max-w-4xl">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground mb-2">Before</h4>
                    <img src={item.beforeImage} alt={`Before ${item.title}`} className="w-full rounded-lg" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground mb-2">After</h4>
                    <img src={item.afterImage} alt={`After ${item.title}`} className="w-full rounded-lg" />
                  </div>
                </div>
                <div className="mt-4">
                  <h3 className="text-lg font-semibold text-foreground mb-2">{item.title}</h3>
                  <p className="text-muted-foreground mb-3">{item.description}</p>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      <span>{item.duration}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Eye className="w-4 h-4" />
                      <span>{item.views.toLocaleString()}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Heart className="w-4 h-4" />
                      <span>{item.likes}</span>
                    </div>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          {/* Card Content */}
          <div className="p-4">
            <h3 className="font-semibold text-foreground mb-2 line-clamp-1">{item.title}</h3>
            <p className="text-sm text-muted-foreground mb-3 line-clamp-2">{item.description}</p>
            
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                <span>{item.duration}</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-1">
                  <Eye className="w-3 h-3" />
                  <span>{item.views.toLocaleString()}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Heart className="w-3 h-3" />
                  <span>{item.likes}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <ErrorBoundary level="page" showDetails={import.meta.env.DEV}>
      <SEOHead 
        lang={currentLanguage}
        title={`${t('galleryPage.hero.title')} | Lullaby Clinic`}
        description={t('galleryPage.hero.subtitle')}
        keywords="lullaby clinic gallery, before after results, facial results, botox results, laser treatment, thread lift, dermal fillers"
      />
      
      <div className="min-h-screen bg-background">
        {/* Navigation */}
        <Navigation />

        <main className="container mx-auto px-4 pt-32 pb-12 space-y-16">
          {/* Hero Section */}
          <section className="text-center bg-gradient-soft py-16 rounded-2xl">
            <Card className="bg-white border-gray-200 shadow-lg max-w-4xl mx-auto">
              <CardContent className="p-8">
                <h1 className="text-4xl md:text-5xl font-extrabold text-foreground mb-6">
                  {t('galleryPage.hero.title')}
                </h1>
                <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                  {t('galleryPage.hero.subtitle')}
                </p>
              </CardContent>
            </Card>
          </section>

          {/* Filter Tabs */}
          <section className="text-center">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full max-w-2xl mx-auto grid-cols-3 sm:grid-cols-6 bg-white border-gray-200 shadow-lg">
                <TabsTrigger value="all" className="data-[state=active]:bg-pink-500 data-[state=active]:text-white text-foreground">
                  {t('galleryPage.categories.all')}
                </TabsTrigger>
                <TabsTrigger value="facial" className="data-[state=active]:bg-pink-500 data-[state=active]:text-white text-foreground">
                  {t('galleryPage.categories.facial')}
                </TabsTrigger>
                <TabsTrigger value="botox" className="data-[state=active]:bg-pink-500 data-[state=active]:text-white text-foreground">
                  {t('galleryPage.categories.botox')}
                </TabsTrigger>
                <TabsTrigger value="laser" className="data-[state=active]:bg-pink-500 data-[state=active]:text-white text-foreground">
                  {t('galleryPage.categories.laser')}
                </TabsTrigger>
                <TabsTrigger value="filler" className="data-[state=active]:bg-pink-500 data-[state=active]:text-white text-foreground">
                  {t('galleryPage.categories.filler')}
                </TabsTrigger>
                <TabsTrigger value="threads" className="data-[state=active]:bg-pink-500 data-[state=active]:text-white text-foreground">
                  {t('galleryPage.categories.threads')}
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </section>

          {/* Gallery Grid */}
          <section className="grid grid-cols-1 max-[375px]:grid-cols-1 max-[640px]:grid-cols-2 max-[1024px]:grid-cols-3 min-[1024px]:grid-cols-4 gap-6">
            {filteredItems.map((item) => (
              <GalleryCard key={item.id} item={item} />
            ))}
          </section>

          {/* Results Summary */}
          <section className="text-center">
            <Card className="bg-gradient-soft border-gray-200 shadow-lg max-w-2xl mx-auto">
              <CardContent className="p-8">
                <h2 className="text-2xl font-bold text-foreground mb-4">
                  {filteredItems.length} {t('galleryPage.viewDetails')}
                </h2>
                <p className="text-muted-foreground">
                  Showing {activeTab === 'all' ? 'all' : activeTab} treatment results from our satisfied clients
                </p>
              </CardContent>
            </Card>
          </section>
        </main>

        {/* Footer */}
        <Footer translations={{
          footer: {
            partners: t('footer.partners'),
            description: t('footer.description'),
            address: t('footer.address'),
            phone: t('footer.phone'),
            email: t('footer.email'),
            quickLinks: t('footer.quickLinks'),
            services: t('footer.services'),
            rights: t('footer.rights'),
            socialMedia: {
              facebook: t('footer.socialMedia.facebook'),
              line: t('footer.socialMedia.line'),
              googleMaps: t('footer.socialMedia.googleMaps')
            }
          },
          nav: {
            home: t('nav.home'),
            services: t('nav.services'),
            about: t('nav.about'),
            gallery: t('nav.gallery'),
            blog: t('nav.blog')
          },
          services: {
            facial: {
              title: t('services.facial.title')
            },
            botox: {
              title: t('services.botox.title')
            },
            laser: {
              title: t('services.laser.title')
            }
          }
        }} />
      </div>
    </ErrorBoundary>
  );
};

export default Gallery;
