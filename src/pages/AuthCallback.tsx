/**
 * Lullaby Clinic - OAuth Callback Handler
 * Handles OAuth redirects from social providers
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useLanguage } from '@/contexts/LanguageContext';
import { useToast } from '@/hooks/use-toast';
import { SocialAuthService } from '@/lib/social-auth';

const AuthCallback: React.FC = () => {
  const { t } = useLanguage();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState<string>('');

  useEffect(() => {
    const handleCallback = async () => {
      try {
        setStatus('loading');
        
        const { data, error } = await SocialAuthService.handleOAuthCallback();
        
        if (error) {
          console.error('OAuth callback error:', error);
          setErrorMessage(error.message || t('auth.socialAuthError'));
          setStatus('error');
          return;
        }

        if (data) {
          setStatus('success');
          
          // Show success toast
          toast({
            title: t('auth.loginSuccess'),
            description: t('auth.socialLoginSuccess'),
          });

          // Redirect after a brief delay
          setTimeout(() => {
            const redirect = searchParams.get('redirect') || '/dashboard';
            const isLinking = searchParams.get('linking') === 'true';
            
            if (isLinking) {
              // If linking, redirect to profile settings
              navigate('/profile/settings?linked=true');
            } else {
              // Normal login redirect
              navigate(redirect);
            }
          }, 1500);
        } else {
          setErrorMessage(t('auth.noSessionError'));
          setStatus('error');
        }
      } catch (error) {
        console.error('Callback handling error:', error);
        setErrorMessage(
          error instanceof Error ? error.message : t('auth.unexpectedError')
        );
        setStatus('error');
      }
    };

    // Handle the callback when component mounts
    handleCallback();
  }, [navigate, searchParams, t, toast]);

  const handleRetry = () => {
    navigate('/auth/login');
  };

  const handleGoHome = () => {
    navigate('/');
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold text-center">
              {t('auth.processingLogin')}
            </CardTitle>
            <CardDescription className="text-center">
              {t('auth.pleaseWait')}
            </CardDescription>
          </CardHeader>
          
          <CardContent className="flex flex-col items-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-sm text-gray-600 text-center">
              {t('auth.verifyingCredentials')}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (status === 'success') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold text-center text-green-600">
              {t('auth.loginSuccess')}
            </CardTitle>
            <CardDescription className="text-center">
              {t('auth.redirectingToDashboard')}
            </CardDescription>
          </CardHeader>
          
          <CardContent className="flex flex-col items-center space-y-4">
            <CheckCircle className="h-8 w-8 text-green-500" />
            <p className="text-sm text-gray-600 text-center">
              {t('auth.loginSuccessMessage')}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (status === 'error') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold text-center text-red-600">
              {t('auth.loginFailed')}
            </CardTitle>
            <CardDescription className="text-center">
              {t('auth.loginErrorOccurred')}
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{errorMessage}</AlertDescription>
            </Alert>
            
            <div className="flex flex-col space-y-2">
              <Button onClick={handleRetry} className="w-full">
                {t('auth.tryAgain')}
              </Button>
              <Button onClick={handleGoHome} variant="outline" className="w-full">
                {t('nav.home')}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return null;
};

export default AuthCallback;