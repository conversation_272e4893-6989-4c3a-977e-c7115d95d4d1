import React, { memo, useState } from 'react';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import SEOHead from '@/components/SEOHead';
import ErrorBoundary from '@/components/ErrorBoundary';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar, Clock, User, Search, ArrowRight, Tag } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

// Blog posts data
const blogPosts = [
    {
        id: 'skincare_routine',
        category: 'skincare',
        readTime: '5 min',
        author: 'Dr. <PERSON>',
        date: '2024-01-15',
        image: '✨',
        featured: true
    },
    {
        id: 'botox_guide',
        category: 'treatments',
        readTime: '8 min',
        author: '<PERSON><PERSON> <PERSON>',
        date: '2024-01-10',
        image: '💉',
        featured: true
    },
    {
        id: 'laser_technology',
        category: 'technology',
        readTime: '6 min',
        author: 'Dr. <PERSON>',
        date: '2024-01-05',
        image: '⚡',
        featured: false
    },
    {
        id: 'beauty_trends',
        category: 'trends',
        readTime: '4 min',
        author: 'Dr. Smith',
        date: '2024-01-01',
        image: '🌟',
        featured: false
    }
];

// Glass effect card component
const ModernCard = memo(({ 
  children, 
  className = '', 
  hover = false 
}: { 
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
}) => (
  <Card 
    className={`
      bg-white border border-gray-200 shadow-lg rounded-lg
      ${hover ? 'hover:shadow-xl hover:scale-105 transition-all duration-300' : ''}
      ${className}
    `}
    style={{
    }}
  >
    <CardContent className="p-8">
      {children}
    </CardContent>
  </Card>
));

ModernCard.displayName = "ModernCard";;

// Blog Post Card Component
const BlogPostCard = memo(({ post, featured = false }: { post: typeof blogPosts[0]; featured?: boolean }) => {
  const { t } = useLanguage();
  
  return (
    <ModernCard hover className={`h-full ${featured ? 'md:col-span-2' : ''}`}>
      <div className="space-y-6">
        <div className="text-center">
          <div className="text-6xl mb-4">{post.image}</div>
          <Badge variant="secondary" className="bg-white/20 text-foreground mb-4">
            {t(`blogPage.categories.${post.category}`)}
          </Badge>
        </div>
        
        <div>
          <h3 className={`font-bold text-foreground mb-3 ${featured ? 'text-2xl' : 'text-xl'}`}>
            {t(`blogPage.posts.${post.id}.title`)}
          </h3>
          <p className="text-muted-foreground leading-relaxed">
            {t(`blogPage.posts.${post.id}.excerpt`)}
          </p>
        </div>
        
        <div className="flex items-center justify-between text-foreground/70 text-sm">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <User className="w-4 h-4" />
              <span>{post.author}</span>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="w-4 h-4" />
              <span>{post.date}</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="w-4 h-4" />
              <span>{post.readTime}</span>
            </div>
          </div>
        </div>
        
        <Button 
          className="w-full bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-foreground border-0"
        >
          {t('blogPage.readMore')} <ArrowRight className="w-4 h-4 ml-2" />
        </Button>
      </div>
    </ModernCard>
  );
});

BlogPostCard.displayName = 'BlogPostCard';

const Blog = () => {
  const { t, currentLanguage } = useLanguage();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');

  const filteredPosts = blogPosts.filter(post => {
    const matchesCategory = activeCategory === 'all' || post.category === activeCategory;
    const matchesSearch = searchQuery === '' || 
      t(`blogPage.posts.${post.id}.title`).toLowerCase().includes(searchQuery.toLowerCase()) ||
      t(`blogPage.posts.${post.id}.excerpt`).toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const featuredPosts = filteredPosts.filter(post => post.featured);
  const regularPosts = filteredPosts.filter(post => !post.featured);

  return (
    <ErrorBoundary level="page" showDetails={import.meta.env.DEV}>
      <SEOHead 
        lang={currentLanguage}
        title={`${t('blogPage.hero.title')} | Lullaby Clinic`}
        description={t('blogPage.hero.subtitle')}
        keywords="lullaby clinic blog, beauty tips, skincare advice, aesthetic medicine, wellness"
      />
      
      <div
        className="min-h-screen bg-background"
        style={{
          background: 'bg-background',
        }}
      >
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-72 h-72 bg-pink-300/20 rounded-full blur-3xl animate-pulse"></div>
          <div
            className="absolute top-40 right-20 w-96 h-96 bg-purple-300/15 rounded-full blur-3xl animate-pulse"
            style={{ animationDelay: '2s' }}
          ></div>
          <div
            className="absolute bottom-20 left-1/3 w-80 h-80 bg-blue-300/10 rounded-full blur-3xl animate-pulse"
            style={{ animationDelay: '4s' }}
          ></div>
        </div>

        {/* Navigation */}
        <Navigation />

        <main className="container mx-auto px-4 pt-32 pb-12 space-y-16 relative z-10">
          {/* Hero Section */}
          <section className="text-center">
            <ModernCard>
              <h1 className="text-5xl md:text-6xl font-extrabold text-foreground mb-6">
                {t('blogPage.hero.title')}
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                {t('blogPage.hero.subtitle')}
              </p>
            </ModernCard>
          </section>

          {/* Search and Filter Section */}
          <section className="space-y-8">
            <ModernCard>
              <div className="max-w-2xl mx-auto space-y-6">
                {/* Search Bar */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-foreground/50" />
                  <Input
                    placeholder={t('blogPage.search.placeholder')}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 bg-white/10 border-white/20 text-foreground placeholder:text-foreground/50"
                  />
                </div>
                
                {/* Category Tabs */}
                <Tabs value={activeCategory} onValueChange={setActiveCategory}>
                  <TabsList className="grid w-full grid-cols-5 bg-white/10 backdrop-blur-md">
                    <TabsTrigger value="all" className="data-[state=active]:bg-white/20 text-foreground">
                      {t('blogPage.categories.all')}
                    </TabsTrigger>
                    <TabsTrigger value="skincare" className="data-[state=active]:bg-white/20 text-foreground">
                      {t('blogPage.categories.skincare')}
                    </TabsTrigger>
                    <TabsTrigger value="treatments" className="data-[state=active]:bg-white/20 text-foreground">
                      {t('blogPage.categories.treatments')}
                    </TabsTrigger>
                    <TabsTrigger value="technology" className="data-[state=active]:bg-white/20 text-foreground">
                      {t('blogPage.categories.technology')}
                    </TabsTrigger>
                    <TabsTrigger value="trends" className="data-[state=active]:bg-white/20 text-foreground">
                      {t('blogPage.categories.trends')}
                    </TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
            </ModernCard>
          </section>

          {/* Featured Posts */}
          {featuredPosts.length > 0 && (
            <section className="space-y-8">
              <h2 className="text-4xl font-bold text-foreground text-center">
                {t('blogPage.featured.title')}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {featuredPosts.map((post) => (
                  <BlogPostCard key={post.id} post={post} featured />
                ))}
              </div>
            </section>
          )}

          {/* Regular Posts */}
          <section className="space-y-8">
            <h2 className="text-4xl font-bold text-foreground text-center">
              {t('blogPage.recent.title')}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {regularPosts.map((post) => (
                <BlogPostCard key={post.id} post={post} />
              ))}
            </div>
          </section>

          {/* Newsletter CTA */}
          <section className="text-center">
            <ModernCard>
              <div className="max-w-2xl mx-auto space-y-6">
                <h2 className="text-3xl font-bold text-foreground">
                  {t('blogPage.newsletter.title')}
                </h2>
                <p className="text-xl text-muted-foreground">
                  {t('blogPage.newsletter.subtitle')}
                </p>
                <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                  <Input
                    placeholder={t('blogPage.newsletter.placeholder')}
                    className="bg-white/10 border-white/20 text-foreground placeholder:text-foreground/50 flex-1"
                  />
                  <Button 
                    className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-foreground border-0"
                  >
                    {t('blogPage.newsletter.subscribe')}
                  </Button>
                </div>
              </div>
            </ModernCard>
          </section>
        </main>

        {/* Footer */}
        <Footer translations={{
          footer: {
            partners: t('footer.partners'),
            description: t('footer.description'),
            address: t('footer.address'),
            phone: t('footer.phone'),
            email: t('footer.email'),
            quickLinks: t('footer.quickLinks'),
            services: t('footer.services'),
            rights: t('footer.rights'),
            socialMedia: {
              facebook: t('footer.socialMedia.facebook'),
              line: t('footer.socialMedia.line'),
              googleMaps: t('footer.socialMedia.googleMaps')
            }
          },
          nav: {
            home: t('nav.home'),
            services: t('nav.services'),
            about: t('nav.about'),
            gallery: t('nav.gallery'),
            blog: t('nav.blog')
          },
          services: {
            facial: {
              title: t('services.facial.title')
            },
            botox: {
              title: t('services.botox.title')
            },
            laser: {
              title: t('services.laser.title')
            }
          }
        }} />
      </div>
    </ErrorBoundary>
  );
};

export default Blog;
