import React, { memo, useState } from 'react';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import SEOHead from '@/components/SEOHead';
import ErrorBoundary from '@/components/ErrorBoundary';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Syringe, Sparkles, Star, ArrowRight, Calendar, Loader2 } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useServices } from '@/hooks/useServices';
import { Service } from '@/lib/supabase/services';

// Mock image component for displaying AI-generated service images
const MockServiceImage = memo(({ prompt, alt, className }: { prompt: string; alt: string; className?: string }) => (
  <div className={`bg-gradient-to-br from-pink-50 to-purple-50 rounded-lg flex items-center justify-center p-4 ${className}`}>
    <div className="text-center">
      <Sparkles className="w-8 h-8 text-pink-400 mx-auto mb-2" />
      <p className="text-xs text-muted-foreground leading-tight">{prompt}</p>
    </div>
  </div>
));

// Format price in THB
const formatPrice = (price: number) => `฿${price.toLocaleString()}`;

// Service Card Component
const ServiceCard = memo(({ service, icon, onBookNow }: { 
  service: Service;
  icon: React.ReactNode;
  onBookNow: () => void;
}) => (
  <Card className="bg-white border-gray-200 shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300">
    <CardContent className="p-6 space-y-4">
      {/* Mock Image */}
      <MockServiceImage 
        prompt={service.mock_image_prompt} 
        alt={service.name}
        className="h-32"
      />
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-3">
          {icon}
          <h3 className="font-semibold text-foreground">{service.name}</h3>
        </div>
      </div>
      <div className="text-2xl font-bold text-foreground">
        {formatPrice(service.price)}
      </div>
      <Button 
        onClick={onBookNow}
        className="w-full"
      >
        Book Now <ArrowRight className="w-4 h-4 ml-2" />
      </Button>
    </CardContent>
  </Card>
));

ServiceCard.displayName = 'ServiceCard';

// Brand Package Card Component
const BrandPackageCard = memo(({ brand, onBookNow }: { 
  brand: Service;
  onBookNow: () => void;
}) => (
  <Card className="bg-white border-gray-200 shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300">
    <CardHeader className="text-center">
      <div className="flex items-center justify-center gap-2 mb-2">
        <Star className="w-6 h-6 text-yellow-500" />
        <CardTitle className="text-xl text-foreground">{brand.name}</CardTitle>
      </div>
      {brand.units && (
        <Badge variant="secondary" className="mx-auto">
          {brand.units} units
        </Badge>
      )}
    </CardHeader>
    <CardContent className="text-center space-y-4">
      {/* Mock Image */}
      <MockServiceImage 
        prompt={brand.mock_image_prompt} 
        alt={brand.name}
        className="h-32"
      />
      <div className="text-3xl font-bold text-foreground">
        {formatPrice(brand.price)}
      </div>
      <Button 
        onClick={onBookNow}
        className="w-full"
      >
        Select Package <ArrowRight className="w-4 h-4 ml-2" />
      </Button>
    </CardContent>
  </Card>
));

BrandPackageCard.displayName = 'BrandPackageCard';

// Filler Product Card Component
const FillerProductCard = memo(({ product, onBookNow }: { 
  product: Service;
  onBookNow: () => void;
}) => (
  <Card className="bg-white border-gray-200 shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300">
    <CardHeader>
      <div className="flex items-center justify-between">
        <CardTitle className="text-lg text-foreground">{product.name}</CardTitle>
        {product.country && <Badge variant="outline">{product.country}</Badge>}
      </div>
      <CardDescription className="text-muted-foreground">{product.brand}</CardDescription>
    </CardHeader>
    <CardContent className="space-y-4">
      {/* Mock Image */}
      <MockServiceImage 
        prompt={product.mock_image_prompt} 
        alt={product.name}
        className="h-32"
      />
      <div className="text-2xl font-bold text-foreground">
        {formatPrice(product.price)}
      </div>
      <Button 
        onClick={onBookNow}
        variant="outline" 
        className="w-full"
      >
        View Details <ArrowRight className="w-4 h-4 ml-2" />
      </Button>
    </CardContent>
  </Card>
));

FillerProductCard.displayName = 'FillerProductCard';

// Injection Zone Card Component
const InjectionZoneCard = memo(({ zone, onBookNow }: { 
  zone: Service;
  onBookNow: () => void;
}) => (
  <Card className="bg-white border-gray-200 shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300">
    <CardHeader>
      <CardTitle className="text-lg text-foreground">{zone.zone || zone.name}</CardTitle>
      <CardDescription className="text-muted-foreground">Recommended products for this area</CardDescription>
    </CardHeader>
    <CardContent className="space-y-4">
      {/* Mock Image */}
      <MockServiceImage 
        prompt={zone.mock_image_prompt} 
        alt={zone.zone || zone.name}
        className="h-32"
      />
      <div className="space-y-2">
        {zone.products && zone.products.map((product, idx) => (
          <div key={idx} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
            <span className="font-medium text-muted-foreground">{product.name}</span>
            <span className="font-bold text-foreground">{formatPrice(product.price)}</span>
          </div>
        ))}
      </div>
      <Button 
        onClick={onBookNow}
        className="w-full"
      >
        Book Consultation <Calendar className="w-4 h-4 ml-2" />
      </Button>
    </CardContent>
  </Card>
));

InjectionZoneCard.displayName = 'InjectionZoneCard';

const Services = () => {
  const { t, currentLanguage } = useLanguage();
  const [activeFilter, setActiveFilter] = useState('all');
  
  // Fetch services from Supabase
  const { services, loading, error } = useServices({ 
    category: activeFilter === 'all' ? undefined : activeFilter 
  });

  // Handle booking action
  const handleBookNow = () => {
    window.location.href = '/appointments';
  };

  // Get filtered services
  const getFilteredServices = () => {
    if (activeFilter === 'all') return services;
    if (activeFilter === 'botox-all') {
      return services.filter(service => 
        service.category === 'botox' || service.category === 'botox-package'
      );
    }
    return services.filter(service => service.category === activeFilter);
  };

  // Loading state
  if (loading) {
    return (
      <ErrorBoundary level="page" showDetails={import.meta.env.DEV}>
        <div className="min-h-screen bg-background">
          <Navigation />
          <div className="pt-32 pb-12 px-4">
            <div className="max-w-7xl mx-auto">
              <div className="text-center">
                <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-primary" />
                <p className="text-muted-foreground">Loading services...</p>
              </div>
            </div>
          </div>
          <Footer />
        </div>
      </ErrorBoundary>
    );
  }

  // Error state
  if (error) {
    return (
      <ErrorBoundary level="page" showDetails={import.meta.env.DEV}>
        <div className="min-h-screen bg-background">
          <Navigation />
          <div className="pt-32 pb-12 px-4">
            <div className="max-w-7xl mx-auto">
              <div className="text-center">
                <p className="text-red-600 mb-4">Error loading services: {error}</p>
                <Button onClick={() => window.location.reload()}>
                  Try Again
                </Button>
              </div>
            </div>
          </div>
          <Footer />
        </div>
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary level="page" showDetails={import.meta.env.DEV}>
      <SEOHead
        lang={currentLanguage}
        title="Our Services | Lullaby Clinic"
        description="Premium Botox and Dermal Filler treatments with high-quality imported products at affordable prices"
        keywords="botox, filler, dermal filler, juvederm, restylane, neuramis, lullaby clinic, beauty treatment"
      />

      <div className="min-h-screen bg-background">
        {/* Navigation */}
        <Navigation />

        <main className="container mx-auto px-4 pt-32 pb-12 space-y-16">
          {/* Hero Section */}
          <section className="relative bg-gradient-soft min-h-[400px] flex items-center rounded-lg">
            <div className="container mx-auto px-4 py-16 text-center">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-6">
                Our Services
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                Premium Botox and Dermal Filler treatments with high-quality imported products 
                at affordable prices for everyone
              </p>
            </div>
          </section>

          {/* Filter Controls */}
          <section className="space-y-8">
            <div className="text-center">
              <h2 className="text-4xl font-bold text-foreground mb-6">
                Choose Your Service
              </h2>
              
              {/* Filter Tabs */}
              <Tabs value={activeFilter} onValueChange={setActiveFilter} className="mb-12">
                <TabsList className="grid w-full max-w-4xl mx-auto grid-cols-5">
                  <TabsTrigger value="all">All Services</TabsTrigger>
                  <TabsTrigger value="botox">Botox</TabsTrigger>
                  <TabsTrigger value="filler">Fillers</TabsTrigger>
                  <TabsTrigger value="zone">Top Zones</TabsTrigger>
                  <TabsTrigger value="botox-package">Packages</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>

            {/* Services Grid */}
            <div className="space-y-12">
              {/* Botox Services */}
              {(activeFilter === 'all' || activeFilter === 'botox') && (
                <div className="space-y-6">
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-foreground mb-4">Botox Injection Options</h3>
                    <p className="text-muted-foreground">Choose your desired injection area, starting from ฿1,990</p>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {getFilteredServices()
                      .filter(service => service.category === 'botox')
                      .map((service) => (
                        <ServiceCard 
                          key={service.id} 
                          service={service} 
                          icon={<Syringe className="w-5 h-5 text-blue-600" />}
                          onBookNow={handleBookNow}
                        />
                      ))}
                  </div>
                </div>
              )}

              {/* Botox Brand Packages */}
              {(activeFilter === 'all' || activeFilter === 'botox-package') && (
                <div className="space-y-6">
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-foreground mb-4">Botox Brand Packages</h3>
                    <p className="text-muted-foreground">Choose your preferred Botox brand with appropriate units</p>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {getFilteredServices()
                      .filter(service => service.category === 'botox-package')
                      .map((service) => (
                        <BrandPackageCard 
                          key={service.id} 
                          brand={service}
                          onBookNow={handleBookNow}
                        />
                      ))}
                  </div>
                </div>
              )}

              {/* Dermal Fillers */}
              {(activeFilter === 'all' || activeFilter === 'filler') && (
                <div className="space-y-6">
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-foreground mb-4">Premium Dermal Fillers</h3>
                    <p className="text-muted-foreground">Imported filler products with world-class quality</p>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {getFilteredServices()
                      .filter(service => service.category === 'filler')
                      .map((service) => (
                        <FillerProductCard 
                          key={service.id} 
                          product={service}
                          onBookNow={handleBookNow}
                        />
                      ))}
                  </div>
                </div>
              )}

              {/* Top 10 Injection Zones */}
              {(activeFilter === 'all' || activeFilter === 'zone') && (
                <div className="space-y-6">
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-foreground mb-4">Top 10 Popular Injection Zones</h3>
                    <p className="text-muted-foreground">Most popular filler injection areas with recommended products</p>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    {getFilteredServices()
                      .filter(service => service.category === 'zone')
                      .map((service) => (
                        <InjectionZoneCard 
                          key={service.id} 
                          zone={service}
                          onBookNow={handleBookNow}
                        />
                      ))}
                  </div>
                </div>
              )}
            </div>
          </section>

          {/* Booking CTA Section */}
          <section className="relative bg-gradient-soft rounded-lg py-16">
            <div className="container mx-auto px-4 text-center">
              <div className="max-w-2xl mx-auto space-y-6">
                <h2 className="text-3xl font-bold text-foreground">
                  Ready to Start Your Transformation?
                </h2>
                <p className="text-xl text-muted-foreground">
                  Book a free consultation with our experts to find the perfect treatment for you
                </p>
                <Button
                  size="lg"
                  className="px-8 py-4 text-lg"
                  onClick={handleBookNow}
                >
                  Book Free Consultation <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </div>
            </div>
          </section>

          {/* Why Choose Us Section */}
          <section className="space-y-8">
            <h2 className="text-4xl font-bold text-foreground text-center">
              Why Choose Lullaby Clinic?
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <Card className="bg-white border-gray-200 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardContent className="p-8 text-center space-y-4">
                  <div className="text-4xl">👨‍⚕️</div>
                  <h3 className="text-xl font-bold text-foreground">Expert Doctors</h3>
                  <p className="text-muted-foreground">
                    Certified medical team with years of experience in aesthetic medicine
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-white border-gray-200 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardContent className="p-8 text-center space-y-4">
                  <div className="text-4xl">🔬</div>
                  <h3 className="text-xl font-bold text-foreground">Advanced Technology</h3>
                  <p className="text-muted-foreground">
                    State-of-the-art equipment and latest techniques for safe and effective treatments
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-white border-gray-200 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardContent className="p-8 text-center space-y-4">
                  <div className="text-4xl">💝</div>
                  <h3 className="text-xl font-bold text-foreground">Personalized Care</h3>
                  <p className="text-muted-foreground">
                    Customized treatment plans tailored to your unique beauty goals and needs
                  </p>
                </CardContent>
              </Card>
            </div>
          </section>
        </main>

        {/* Footer */}
        <Footer translations={{
          footer: {
            partners: t('footer.partners'),
            description: t('footer.description'),
            address: t('footer.address'),
            phone: t('footer.phone'),
            email: t('footer.email'),
            quickLinks: t('footer.quickLinks'),
            services: t('footer.services'),
            rights: t('footer.rights'),
            socialMedia: {
              facebook: t('footer.socialMedia.facebook'),
              line: t('footer.socialMedia.line'),
              googleMaps: t('footer.socialMedia.googleMaps')
            }
          },
          nav: {
            home: t('nav.home'),
            services: t('nav.services'),
            about: t('nav.about'),
            gallery: t('nav.gallery'),
            blog: t('nav.blog')
          },
          services: {
            facial: {
              title: t('services.facial.title')
            },
            botox: {
              title: t('services.botox.title')
            },
            laser: {
              title: t('services.laser.title')
            }
          }
        }} />
      </div>
    </ErrorBoundary>
  );
};

export default Services;
