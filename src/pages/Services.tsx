import React, { memo, useState } from 'react';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import SEOHead from '@/components/SEOHead';
import ErrorBoundary from '@/components/ErrorBoundary';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Clock, DollarSign, CheckCircle, ArrowRight } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

// Treatment data
const treatmentsData = [
  {
    id: 'facial',
    icon: '✨',
    category: 'skincare'
  },
  {
    id: 'botox',
    icon: '💉',
    category: 'anti-aging'
  },
  {
    id: 'laser',
    icon: '⚡',
    category: 'technology'
  },
  {
    id: 'filler',
    icon: '💎',
    category: 'enhancement'
  }
];

// Modern card component matching home page design
const ModernCard = memo(({ 
  children, 
  className = '', 
  hover = false 
}: { 
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
}) => (
  <Card 
    className={`
      bg-white border border-gray-200 shadow-lg rounded-lg
      ${hover ? 'hover:shadow-xl hover:scale-105 transition-all duration-300' : ''}
      ${className}
    `}
  >
    <CardContent className="p-8">
      {children}
    </CardContent>
  </Card>
));

ModernCard.displayName = 'ModernCard';

// Treatment Card Component
const TreatmentCard = memo(({ treatment }: { treatment: typeof treatmentsData[0] }) => {
  const { t } = useLanguage();
  
  return (
    <ModernCard hover className="h-full">
      <div className="text-center space-y-6">
        <div className="text-6xl">{treatment.icon}</div>
        <div>
          <h3 className="text-2xl font-bold text-foreground mb-3">
            {t(`servicesPage.treatments.${treatment.id}.title`)}
          </h3>
          <p className="text-muted-foreground leading-relaxed mb-4">
            {t(`servicesPage.treatments.${treatment.id}.description`)}
          </p>
        </div>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between text-muted-foreground">
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4" />
              <span>{t(`servicesPage.treatments.${treatment.id}.duration`)}</span>
            </div>
            <div className="flex items-center gap-2">
              <DollarSign className="w-4 h-4" />
              <span className="font-semibold text-foreground">
                {t(`servicesPage.treatments.${treatment.id}.price`)}
              </span>
            </div>
          </div>
          
          <div className="space-y-2">
            {['Smooth, glowing skin', 'Hydration boost', 'Pore refinement', 'Anti-aging benefits'].map((benefit: string, idx: number) => (
              <div key={idx} className="flex items-center gap-2 text-muted-foreground">
                <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                <span className="text-sm">{benefit}</span>
              </div>
            ))}
          </div>
          
          <Button 
            className="w-full"
          >
            {t('common.bookNow')} <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
    </ModernCard>
  );
});

TreatmentCard.displayName = 'TreatmentCard';

const Services = () => {
  const { t, currentLanguage } = useLanguage();
  const [activeTab, setActiveTab] = useState('all');

  const filteredTreatments = treatmentsData.filter(treatment => 
    activeTab === 'all' || treatment.category === activeTab
  );

  return (
    <ErrorBoundary level="page" showDetails={import.meta.env.DEV}>
      <SEOHead 
        lang={currentLanguage}
        title={`${t('servicesPage.hero.title')} | Lullaby Clinic`}
        description={t('servicesPage.hero.subtitle')}
        keywords="lullaby clinic services, facial treatment, botox, laser, dermal fillers, beauty treatments"
      />
      
      <div className="min-h-screen bg-background">


        {/* Navigation */}
        <Navigation />

        <main className="container mx-auto px-4 pt-32 pb-12 space-y-16">
          {/* Hero Section */}
          <section className="relative bg-gradient-soft min-h-[400px] flex items-center rounded-lg">
            <div className="container mx-auto px-4 py-16 text-center">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-6">
                {t('servicesPage.hero.title')}
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                {t('servicesPage.hero.subtitle')}
              </p>
            </div>
          </section>

          {/* Treatments Section */}
          <section className="space-y-8">
            <div className="text-center">
              <h2 className="text-4xl font-bold text-foreground mb-6">
                {t('servicesPage.treatments.title')}
              </h2>
              
              {/* Category Tabs */}
              <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-12">
                <TabsList className="grid w-full max-w-md mx-auto grid-cols-5">
                  <TabsTrigger value="all">
                    All
                  </TabsTrigger>
                  <TabsTrigger value="skincare">
                    Skincare
                  </TabsTrigger>
                  <TabsTrigger value="anti-aging">
                    Anti-Aging
                  </TabsTrigger>
                  <TabsTrigger value="technology">
                    Technology
                  </TabsTrigger>
                  <TabsTrigger value="enhancement">
                    Enhancement
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>

            {/* Treatments Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8">
              {filteredTreatments.map((treatment) => (
                <TreatmentCard key={treatment.id} treatment={treatment} />
              ))}
            </div>
          </section>

          {/* Booking CTA Section */}
          <section className="relative bg-gradient-soft rounded-lg py-16">
            <div className="container mx-auto px-4 text-center">
              <div className="max-w-2xl mx-auto space-y-6">
                <h2 className="text-3xl font-bold text-foreground">
                  {t('servicesPage.booking.title')}
                </h2>
                <p className="text-xl text-muted-foreground">
                  {t('servicesPage.booking.subtitle')}
                </p>
                <Button 
                  size="lg"
                  className="px-8 py-4 text-lg"
                >
                  {t('servicesPage.booking.button')} <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </div>
            </div>
          </section>

          {/* Why Choose Us Section */}
          <section className="space-y-8">
            <h2 className="text-4xl font-bold text-foreground text-center">
              Why Choose Lullaby Clinic?
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <ModernCard hover>
                <div className="text-center space-y-4">
                  <div className="text-4xl">👨‍⚕️</div>
                  <h3 className="text-xl font-bold text-foreground">Expert Doctors</h3>
                  <p className="text-muted-foreground">
                    Our team of certified medical professionals with years of experience in aesthetic medicine
                  </p>
                </div>
              </ModernCard>
              
              <ModernCard hover>
                <div className="text-center space-y-4">
                  <div className="text-4xl">🔬</div>
                  <h3 className="text-xl font-bold text-foreground">Advanced Technology</h3>
                  <p className="text-muted-foreground">
                    State-of-the-art equipment and latest techniques for safe and effective treatments
                  </p>
                </div>
              </ModernCard>
              
              <ModernCard hover>
                <div className="text-center space-y-4">
                  <div className="text-4xl">💝</div>
                  <h3 className="text-xl font-bold text-foreground">Personalized Care</h3>
                  <p className="text-muted-foreground">
                    Customized treatment plans tailored to your unique beauty goals and needs
                  </p>
                </div>
              </ModernCard>
            </div>
          </section>
        </main>

        {/* Footer */}
        <Footer translations={{
          footer: {
            partners: t('footer.partners'),
            description: t('footer.description'),
            address: t('footer.address'),
            phone: t('footer.phone'),
            email: t('footer.email'),
            quickLinks: t('footer.quickLinks'),
            services: t('footer.services'),
            rights: t('footer.rights'),
            socialMedia: {
              facebook: t('footer.socialMedia.facebook'),
              line: t('footer.socialMedia.line'),
              googleMaps: t('footer.socialMedia.googleMaps')
            }
          },
          nav: {
            home: t('nav.home'),
            services: t('nav.services'),
            about: t('nav.about'),
            gallery: t('nav.gallery'),
            blog: t('nav.blog')
          },
          services: {
            facial: {
              title: t('services.facial.title')
            },
            botox: {
              title: t('services.botox.title')
            },
            laser: {
              title: t('services.laser.title')
            }
          }
        }} />
      </div>
    </ErrorBoundary>
  );
};

export default Services;
