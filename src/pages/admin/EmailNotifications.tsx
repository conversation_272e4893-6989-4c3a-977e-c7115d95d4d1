
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useLanguage } from '@/contexts/LanguageContext';
import EmailStatusCard from '@/components/admin/email/EmailStatusCard';
import EmailStatsCards from '@/components/admin/email/EmailStatsCards';
import EmailTestPanel from '@/components/admin/email/EmailTestPanel';
import {
  useEmailNotificationStatus,
  useSendAppointmentConfirmation,
  useSendAppointmentReminder,
  useSendAppointmentCancellation,
  useSendBatchReminders
} from '@/hooks/useEmailNotifications';

// Mock data for recent appointments
const mockRecentAppointments = [
  {
    id: '1',
    patient: { first_name: 'สมชาย', last_name: 'ใจดี' },
    service: { name: 'การทำความสะอาดผิวหน้า' }
  },
  {
    id: '2',
    patient: { first_name: 'มาลี', last_name: 'สวยงาม' },
    service: { name: 'โบท็อกซ์' }
  }
];

const EmailNotifications: React.FC = () => {
  const { t } = useLanguage();
  const [selectedAppointmentId, setSelectedAppointmentId] = useState('');
  const [emailLanguage, setEmailLanguage] = useState<'th' | 'en'>('th');

  // Email notification hooks
  const { data: emailStatus, isLoading: statusLoading } = useEmailNotificationStatus();
  const sendConfirmation = useSendAppointmentConfirmation();
  const sendReminder = useSendAppointmentReminder();
  const sendCancellation = useSendAppointmentCancellation();
  const sendBatchReminders = useSendBatchReminders();

  const handleSendTestEmail = (type: 'confirmation' | 'reminder' | 'cancellation') => {
    if (!selectedAppointmentId) return;

    const mutationMap = {
      confirmation: sendConfirmation,
      reminder: sendReminder,
      cancellation: sendCancellation
    };

    mutationMap[type].mutate({
      appointmentId: selectedAppointmentId,
      language: emailLanguage
    });
  };

  const handleSendBatchReminders = () => {
    sendBatchReminders.mutate();
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">{t('notifications.title')}</h1>
        <p className="text-gray-600 mt-2">{t('notifications.description')}</p>
      </div>

      <Tabs defaultValue="status" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="status">{t('notifications.status')}</TabsTrigger>
          <TabsTrigger value="test">{t('notifications.testEmails')}</TabsTrigger>
          <TabsTrigger value="stats">{t('notifications.statistics')}</TabsTrigger>
        </TabsList>

        <TabsContent value="status" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <EmailStatusCard
              status={emailStatus}
              isLoading={statusLoading}
              onSendBatchReminders={handleSendBatchReminders}
              sendBatchReminders={sendBatchReminders}
            />
          </div>
        </TabsContent>

        <TabsContent value="test" className="space-y-4">
          <EmailTestPanel
            selectedAppointmentId={selectedAppointmentId}
            setSelectedAppointmentId={setSelectedAppointmentId}
            emailLanguage={emailLanguage}
            setEmailLanguage={setEmailLanguage}
            recentAppointments={mockRecentAppointments}
            appointmentsLoading={false}
            onSendTestEmail={handleSendTestEmail}
            sendConfirmation={sendConfirmation}
            sendReminder={sendReminder}
            sendCancellation={sendCancellation}
          />
        </TabsContent>

        <TabsContent value="stats" className="space-y-4">
          <EmailStatsCards />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default EmailNotifications;
