import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { vi } from 'vitest';
import Services from '../Services';
import { useLanguage } from '@/contexts/LanguageContext';

vi.mock('@/contexts/LanguageContext');
vi.mock('@/components/Navigation', () => ({
  default: () => <nav data-testid="navigation">Navigation</nav>
}));
vi.mock('@/components/Footer', () => ({
  default: () => <footer data-testid="footer">Footer</footer>
}));
vi.mock('@/components/SEOHead', () => ({
  default: () => <head data-testid="seo-head">SEO Head</head>
}));
vi.mock('@/components/ErrorBoundary', () => ({
  default: ({ children }: { children: React.ReactNode }) => <div>{children}</div>
}));

const mockUseLanguage = useLanguage as jest.MockedFunction<typeof useLanguage>;

const mockTranslations = {
  'servicesPage.hero.title': 'Our Services',
  'servicesPage.hero.subtitle': 'Professional beauty treatments',
  'servicesPage.treatments.title': 'Available Treatments',
  'servicesPage.treatments.facial.title': 'Facial Treatment',
  'servicesPage.treatments.facial.description': 'Rejuvenating facial treatment',
  'servicesPage.treatments.facial.duration': '60 minutes',
  'servicesPage.treatments.facial.price': '$150',
  'servicesPage.treatments.botox.title': 'Botox Treatment',
  'servicesPage.treatments.botox.description': 'Anti-aging botox treatment',
  'servicesPage.treatments.botox.duration': '30 minutes',
  'servicesPage.treatments.botox.price': '$300',
  'servicesPage.treatments.laser.title': 'Laser Treatment',
  'servicesPage.treatments.laser.description': 'Advanced laser therapy',
  'servicesPage.treatments.laser.duration': '45 minutes',
  'servicesPage.treatments.laser.price': '$250',
  'servicesPage.treatments.filler.title': 'Dermal Fillers',
  'servicesPage.treatments.filler.description': 'Volume enhancement treatment',
  'servicesPage.treatments.filler.duration': '30 minutes',
  'servicesPage.treatments.filler.price': '$400',
  'servicesPage.booking.title': 'Book Your Appointment',
  'servicesPage.booking.subtitle': 'Schedule your consultation today',
  'servicesPage.booking.button': 'Book Now',
  'common.bookNow': 'Book Now',
};

const renderServices = () => {
  mockUseLanguage.mockReturnValue({
    t: (key: string) => mockTranslations[key] || key,
    currentLanguage: 'en',
    setLanguage: vi.fn(),
  });

  return render(
    <BrowserRouter>
      <Services />
    </BrowserRouter>
  );
};

describe('Services Page', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders without glass effects', () => {
    renderServices();
    
    const container = screen.getByText('Our Services').closest('div');
    expect(container).not.toHaveClass('backdrop-blur-md');
    expect(container).not.toHaveClass('bg-white/10');
  });

  it('displays hero section with modern design', () => {
    renderServices();
    
    expect(screen.getByText('Our Services')).toBeInTheDocument();
    expect(screen.getByText('Professional beauty treatments')).toBeInTheDocument();
    
    const heroSection = screen.getByText('Our Services').closest('section');
    expect(heroSection).toHaveClass('bg-gradient-soft');
  });

  it('shows all treatment categories in tabs', () => {
    renderServices();
    
    expect(screen.getByText('All')).toBeInTheDocument();
    expect(screen.getByText('Skincare')).toBeInTheDocument();
    expect(screen.getByText('Anti-Aging')).toBeInTheDocument();
    expect(screen.getByText('Technology')).toBeInTheDocument();
    expect(screen.getByText('Enhancement')).toBeInTheDocument();
  });

  it('displays treatment cards with modern styling', () => {
    renderServices();
    
    expect(screen.getByText('Facial Treatment')).toBeInTheDocument();
    expect(screen.getByText('Botox Treatment')).toBeInTheDocument();
    expect(screen.getByText('Laser Treatment')).toBeInTheDocument();
    expect(screen.getByText('Dermal Fillers')).toBeInTheDocument();
    
    const treatmentCard = screen.getByText('Facial Treatment').closest('div');
    expect(treatmentCard).toHaveClass('bg-white');
    expect(treatmentCard).not.toHaveClass('backdrop-blur-md');
  });

  it('filters treatments by category', () => {
    renderServices();
    
    const skincareTab = screen.getByText('Skincare');
    fireEvent.click(skincareTab);
    
    expect(screen.getByText('Facial Treatment')).toBeInTheDocument();
  });

  it('shows booking CTA section with modern design', () => {
    renderServices();
    
    expect(screen.getByText('Book Your Appointment')).toBeInTheDocument();
    expect(screen.getByText('Schedule your consultation today')).toBeInTheDocument();
    
    const ctaSection = screen.getByText('Book Your Appointment').closest('section');
    expect(ctaSection).toHaveClass('bg-gradient-soft');
  });

  it('displays why choose us section without glass effects', () => {
    renderServices();
    
    expect(screen.getByText('Why Choose Lullaby Clinic?')).toBeInTheDocument();
    expect(screen.getByText('Expert Doctors')).toBeInTheDocument();
    expect(screen.getByText('Advanced Technology')).toBeInTheDocument();
    expect(screen.getByText('Personalized Care')).toBeInTheDocument();
    
    const expertCard = screen.getByText('Expert Doctors').closest('div');
    expect(expertCard).toHaveClass('bg-white');
    expect(expertCard).not.toHaveClass('backdrop-blur-md');
  });

  it('includes navigation and footer', () => {
    renderServices();
    
    expect(screen.getByTestId('navigation')).toBeInTheDocument();
    expect(screen.getByTestId('footer')).toBeInTheDocument();
  });

  it('is mobile responsive', () => {
    renderServices();
    
    const treatmentsGrid = screen.getByText('Facial Treatment').closest('div').parentElement;
    expect(treatmentsGrid).toHaveClass('grid-cols-1');
    expect(treatmentsGrid).toHaveClass('md:grid-cols-2');
  });

  it('has proper text colors for readability', () => {
    renderServices();
    
    const title = screen.getByText('Our Services');
    expect(title).toHaveClass('text-foreground');
    
    const subtitle = screen.getByText('Professional beauty treatments');
    expect(subtitle).toHaveClass('text-muted-foreground');
  });
});
