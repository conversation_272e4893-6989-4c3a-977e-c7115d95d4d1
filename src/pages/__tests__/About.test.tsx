import React from 'react';
import { render, screen } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { vi } from 'vitest';
import About from '../About';
import { useLanguage } from '@/contexts/LanguageContext';

vi.mock('@/contexts/LanguageContext');
vi.mock('@/components/Navigation', () => ({
  default: () => <nav data-testid="navigation">Navigation</nav>
}));
vi.mock('@/components/Footer', () => ({
  default: () => <footer data-testid="footer">Footer</footer>
}));
vi.mock('@/components/SEOHead', () => ({
  default: () => <head data-testid="seo-head">SEO Head</head>
}));
vi.mock('@/components/ErrorBoundary', () => ({
  default: ({ children }: { children: React.ReactNode }) => <div>{children}</div>
}));

const mockUseLanguage = useLanguage as jest.MockedFunction<typeof useLanguage>;

const mockTranslations = {
  'about.hero.title': 'About Lullaby Clinic',
  'about.hero.subtitle': 'Your trusted beauty partner',
  'about.mission.title': 'Our Mission',
  'about.mission.description': 'To provide exceptional beauty treatments',
  'about.vision.title': 'Our Vision',
  'about.vision.description': 'To be the leading beauty clinic',
  'about.approach.title': 'Our Approach',
  'about.approach.description': 'We believe in personalized care',
};

const renderAbout = () => {
  mockUseLanguage.mockReturnValue({
    t: (key: string) => mockTranslations[key] || key,
    currentLanguage: 'en',
    setLanguage: vi.fn(),
  });

  return render(
    <BrowserRouter>
      <About />
    </BrowserRouter>
  );
};

describe('About Page', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders without glass effects', () => {
    renderAbout();
    
    const container = document.querySelector('.backdrop-blur-md');
    expect(container).toBeNull();
    
    const glassContainer = document.querySelector('.bg-white\\/10');
    expect(glassContainer).toBeNull();
  });

  it('displays hero section with modern design', () => {
    renderAbout();
    
    expect(screen.getByText('About Lullaby Clinic')).toBeInTheDocument();
    expect(screen.getByText('Your trusted beauty partner')).toBeInTheDocument();
    
    const heroSection = screen.getByText('About Lullaby Clinic').closest('section');
    expect(heroSection).toHaveClass('bg-gradient-soft');
  });

  it('shows mission and vision cards with modern styling', () => {
    renderAbout();
    
    expect(screen.getByText('Our Mission')).toBeInTheDocument();
    expect(screen.getByText('To provide exceptional beauty treatments')).toBeInTheDocument();
    expect(screen.getByText('Our Vision')).toBeInTheDocument();
    expect(screen.getByText('To be the leading beauty clinic')).toBeInTheDocument();
    
    const missionCard = screen.getByText('Our Mission').closest('div');
    expect(missionCard).toHaveClass('bg-white');
    expect(missionCard).not.toHaveClass('backdrop-blur-md');
  });

  it('displays approach section', () => {
    renderAbout();
    
    expect(screen.getByText('Our Approach')).toBeInTheDocument();
    expect(screen.getByText('We believe in personalized care')).toBeInTheDocument();
  });

  it('includes navigation and footer', () => {
    renderAbout();
    
    expect(screen.getByTestId('navigation')).toBeInTheDocument();
    expect(screen.getByTestId('footer')).toBeInTheDocument();
  });

  it('has proper background styling', () => {
    renderAbout();
    
    const mainContainer = document.querySelector('.min-h-screen');
    expect(mainContainer).toHaveClass('bg-background');
    expect(mainContainer).not.toHaveStyle('background: linear-gradient(135deg, #667eea 0%, #764ba2 100%)');
  });

  it('uses proper text colors for readability', () => {
    renderAbout();
    
    const title = screen.getByText('About Lullaby Clinic');
    expect(title).toHaveClass('text-foreground');
    
    const subtitle = screen.getByText('Your trusted beauty partner');
    expect(subtitle).toHaveClass('text-muted-foreground');
  });

  it('is mobile responsive', () => {
    renderAbout();
    
    const missionVisionGrid = screen.getByText('Our Mission').closest('section');
    expect(missionVisionGrid).toHaveClass('grid-cols-1');
    expect(missionVisionGrid).toHaveClass('md:grid-cols-2');
  });

  it('has consistent spacing and layout', () => {
    renderAbout();
    
    const mainContent = screen.getByText('About Lullaby Clinic').closest('main');
    expect(mainContent).toHaveClass('container');
    expect(mainContent).toHaveClass('mx-auto');
    expect(mainContent).toHaveClass('px-4');
    expect(mainContent).toHaveClass('pt-32');
    expect(mainContent).toHaveClass('pb-12');
  });
});
