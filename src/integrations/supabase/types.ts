export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      admin_activity_logs: {
        Row: {
          action: string
          admin_user_id: string | null
          created_at: string
          details: Json | null
          id: string
          ip_address: string | null
          performed_by: string | null
          target_id: string | null
          target_type: string
          user_agent: string | null
        }
        Insert: {
          action: string
          admin_user_id?: string | null
          created_at?: string
          details?: Json | null
          id?: string
          ip_address?: string | null
          performed_by?: string | null
          target_id?: string | null
          target_type: string
          user_agent?: string | null
        }
        Update: {
          action?: string
          admin_user_id?: string | null
          created_at?: string
          details?: Json | null
          id?: string
          ip_address?: string | null
          performed_by?: string | null
          target_id?: string | null
          target_type?: string
          user_agent?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "admin_activity_logs_admin_user_id_fkey"
            columns: ["admin_user_id"]
            isOneToOne: false
            referencedRelation: "admin_users"
            referencedColumns: ["id"]
          },
        ]
      }
      admin_profiles: {
        Row: {
          avatar_url: string | null
          bio: string | null
          created_at: string | null
          email: string
          first_name: string
          id: string
          last_name: string
          phone: string | null
          preferences: Json | null
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          bio?: string | null
          created_at?: string | null
          email: string
          first_name: string
          id: string
          last_name: string
          phone?: string | null
          preferences?: Json | null
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          bio?: string | null
          created_at?: string | null
          email?: string
          first_name?: string
          id?: string
          last_name?: string
          phone?: string | null
          preferences?: Json | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "admin_profiles_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "admin_users"
            referencedColumns: ["id"]
          },
        ]
      }
      admin_users: {
        Row: {
          created_at: string | null
          department: string | null
          employee_id: string | null
          hire_date: string | null
          id: string
          is_active: boolean | null
          last_login_at: string | null
          permissions: Json | null
          role: Database["public"]["Enums"]["admin_role"]
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          department?: string | null
          employee_id?: string | null
          hire_date?: string | null
          id: string
          is_active?: boolean | null
          last_login_at?: string | null
          permissions?: Json | null
          role?: Database["public"]["Enums"]["admin_role"]
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          department?: string | null
          employee_id?: string | null
          hire_date?: string | null
          id?: string
          is_active?: boolean | null
          last_login_at?: string | null
          permissions?: Json | null
          role?: Database["public"]["Enums"]["admin_role"]
          updated_at?: string | null
        }
        Relationships: []
      }
      analytics_events: {
        Row: {
          browser: string | null
          city: string | null
          country: string | null
          created_at: string | null
          device_type: string | null
          event_category: string
          event_data: Json
          event_type: string
          id: string
          ip_address: unknown | null
          os: string | null
          page_url: string | null
          patient_id: string | null
          referrer_url: string | null
          session_id: string | null
          user_agent: string | null
        }
        Insert: {
          browser?: string | null
          city?: string | null
          country?: string | null
          created_at?: string | null
          device_type?: string | null
          event_category: string
          event_data: Json
          event_type: string
          id?: string
          ip_address?: unknown | null
          os?: string | null
          page_url?: string | null
          patient_id?: string | null
          referrer_url?: string | null
          session_id?: string | null
          user_agent?: string | null
        }
        Update: {
          browser?: string | null
          city?: string | null
          country?: string | null
          created_at?: string | null
          device_type?: string | null
          event_category?: string
          event_data?: Json
          event_type?: string
          id?: string
          ip_address?: unknown | null
          os?: string | null
          page_url?: string | null
          patient_id?: string | null
          referrer_url?: string | null
          session_id?: string | null
          user_agent?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "analytics_events_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      appointment_slots: {
        Row: {
          created_at: string | null
          doctor_id: string | null
          end_time: string
          id: string
          is_available: boolean | null
          recurring_rule: Json | null
          start_time: string
        }
        Insert: {
          created_at?: string | null
          doctor_id?: string | null
          end_time: string
          id?: string
          is_available?: boolean | null
          recurring_rule?: Json | null
          start_time: string
        }
        Update: {
          created_at?: string | null
          doctor_id?: string | null
          end_time?: string
          id?: string
          is_available?: boolean | null
          recurring_rule?: Json | null
          start_time?: string
        }
        Relationships: [
          {
            foreignKeyName: "appointment_slots_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "doctors"
            referencedColumns: ["id"]
          },
        ]
      }
      appointment_types: {
        Row: {
          color: string | null
          created_at: string | null
          description: string | null
          duration_minutes: number
          id: string
          is_active: boolean | null
          name: string
          price: number | null
          updated_at: string | null
        }
        Insert: {
          color?: string | null
          created_at?: string | null
          description?: string | null
          duration_minutes?: number
          id?: string
          is_active?: boolean | null
          name: string
          price?: number | null
          updated_at?: string | null
        }
        Update: {
          color?: string | null
          created_at?: string | null
          description?: string | null
          duration_minutes?: number
          id?: string
          is_active?: boolean | null
          name?: string
          price?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      appointments: {
        Row: {
          after_photos: string[] | null
          appointment_date: string
          appointment_type_id: string | null
          before_photos: string[] | null
          cancellation_reason: string | null
          cancelled_at: string | null
          checked_in_at: string | null
          completed_at: string | null
          confirmation_sent_at: string | null
          created_at: string | null
          deposit_amount: number | null
          doctor_id: string | null
          doctor_notes: string | null
          duration_minutes: number
          followup_date: string | null
          id: string
          next_appointment_recommended: boolean | null
          patient_email: string | null
          patient_id: string | null
          patient_notes: string | null
          patient_phone: string | null
          prescription: string | null
          reminder_sent_at: string | null
          service_id: string | null
          slot_id: string | null
          status: Database["public"]["Enums"]["appointment_status"] | null
          total_amount: number
          treatment_plan: string | null
          updated_at: string | null
        }
        Insert: {
          after_photos?: string[] | null
          appointment_date: string
          appointment_type_id?: string | null
          before_photos?: string[] | null
          cancellation_reason?: string | null
          cancelled_at?: string | null
          checked_in_at?: string | null
          completed_at?: string | null
          confirmation_sent_at?: string | null
          created_at?: string | null
          deposit_amount?: number | null
          doctor_id?: string | null
          doctor_notes?: string | null
          duration_minutes?: number
          followup_date?: string | null
          id?: string
          next_appointment_recommended?: boolean | null
          patient_email?: string | null
          patient_id?: string | null
          patient_notes?: string | null
          patient_phone?: string | null
          prescription?: string | null
          reminder_sent_at?: string | null
          service_id?: string | null
          slot_id?: string | null
          status?: Database["public"]["Enums"]["appointment_status"] | null
          total_amount: number
          treatment_plan?: string | null
          updated_at?: string | null
        }
        Update: {
          after_photos?: string[] | null
          appointment_date?: string
          appointment_type_id?: string | null
          before_photos?: string[] | null
          cancellation_reason?: string | null
          cancelled_at?: string | null
          checked_in_at?: string | null
          completed_at?: string | null
          confirmation_sent_at?: string | null
          created_at?: string | null
          deposit_amount?: number | null
          doctor_id?: string | null
          doctor_notes?: string | null
          duration_minutes?: number
          followup_date?: string | null
          id?: string
          next_appointment_recommended?: boolean | null
          patient_email?: string | null
          patient_id?: string | null
          patient_notes?: string | null
          patient_phone?: string | null
          prescription?: string | null
          reminder_sent_at?: string | null
          service_id?: string | null
          slot_id?: string | null
          status?: Database["public"]["Enums"]["appointment_status"] | null
          total_amount?: number
          treatment_plan?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "appointments_appointment_type_id_fkey"
            columns: ["appointment_type_id"]
            isOneToOne: false
            referencedRelation: "appointment_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "doctors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_service_id_fkey"
            columns: ["service_id"]
            isOneToOne: false
            referencedRelation: "services"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_slot_id_fkey"
            columns: ["slot_id"]
            isOneToOne: false
            referencedRelation: "appointment_slots"
            referencedColumns: ["id"]
          },
        ]
      }
      blog_categories: {
        Row: {
          color: string | null
          created_at: string | null
          description: Json | null
          icon: string | null
          id: string
          is_active: boolean | null
          name: Json
          slug: string
          sort_order: number | null
        }
        Insert: {
          color?: string | null
          created_at?: string | null
          description?: Json | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          name: Json
          slug: string
          sort_order?: number | null
        }
        Update: {
          color?: string | null
          created_at?: string | null
          description?: Json | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          name?: Json
          slug?: string
          sort_order?: number | null
        }
        Relationships: []
      }
      blog_posts: {
        Row: {
          author_id: string | null
          category: string | null
          content: string
          created_at: string | null
          difficulty: Database["public"]["Enums"]["treatment_difficulty"] | null
          excerpt: string | null
          featured_image: string | null
          gallery_images: string[] | null
          id: string
          is_featured: boolean | null
          is_published: boolean | null
          language: string | null
          like_count: number | null
          published_at: string | null
          read_time_minutes: number | null
          seo_description: string | null
          seo_keywords: string[] | null
          seo_title: string | null
          slug: string
          tags: string[] | null
          title: string
          updated_at: string | null
          view_count: number | null
        }
        Insert: {
          author_id?: string | null
          category?: string | null
          content: string
          created_at?: string | null
          difficulty?:
            | Database["public"]["Enums"]["treatment_difficulty"]
            | null
          excerpt?: string | null
          featured_image?: string | null
          gallery_images?: string[] | null
          id?: string
          is_featured?: boolean | null
          is_published?: boolean | null
          language?: string | null
          like_count?: number | null
          published_at?: string | null
          read_time_minutes?: number | null
          seo_description?: string | null
          seo_keywords?: string[] | null
          seo_title?: string | null
          slug: string
          tags?: string[] | null
          title: string
          updated_at?: string | null
          view_count?: number | null
        }
        Update: {
          author_id?: string | null
          category?: string | null
          content?: string
          created_at?: string | null
          difficulty?:
            | Database["public"]["Enums"]["treatment_difficulty"]
            | null
          excerpt?: string | null
          featured_image?: string | null
          gallery_images?: string[] | null
          id?: string
          is_featured?: boolean | null
          is_published?: boolean | null
          language?: string | null
          like_count?: number | null
          published_at?: string | null
          read_time_minutes?: number | null
          seo_description?: string | null
          seo_keywords?: string[] | null
          seo_title?: string | null
          slug?: string
          tags?: string[] | null
          title?: string
          updated_at?: string | null
          view_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "blog_posts_author_id_fkey"
            columns: ["author_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      blog_tags: {
        Row: {
          color: string | null
          created_at: string | null
          id: string
          name: Json
          slug: string
          usage_count: number | null
        }
        Insert: {
          color?: string | null
          created_at?: string | null
          id?: string
          name: Json
          slug: string
          usage_count?: number | null
        }
        Update: {
          color?: string | null
          created_at?: string | null
          id?: string
          name?: Json
          slug?: string
          usage_count?: number | null
        }
        Relationships: []
      }
      campaign_recipients: {
        Row: {
          bounce_reason: string | null
          campaign_id: string | null
          clicked_at: string | null
          created_at: string | null
          delivered_at: string | null
          email: string
          id: string
          opened_at: string | null
          patient_id: string | null
          sent_at: string | null
          status: string | null
        }
        Insert: {
          bounce_reason?: string | null
          campaign_id?: string | null
          clicked_at?: string | null
          created_at?: string | null
          delivered_at?: string | null
          email: string
          id?: string
          opened_at?: string | null
          patient_id?: string | null
          sent_at?: string | null
          status?: string | null
        }
        Update: {
          bounce_reason?: string | null
          campaign_id?: string | null
          clicked_at?: string | null
          created_at?: string | null
          delivered_at?: string | null
          email?: string
          id?: string
          opened_at?: string | null
          patient_id?: string | null
          sent_at?: string | null
          status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "campaign_recipients_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "marketing_campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_recipients_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      clinic_settings: {
        Row: {
          data_type: string | null
          description: string | null
          id: string
          is_public: boolean | null
          key: string
          updated_at: string | null
          updated_by: string | null
          value: string | null
        }
        Insert: {
          data_type?: string | null
          description?: string | null
          id?: string
          is_public?: boolean | null
          key: string
          updated_at?: string | null
          updated_by?: string | null
          value?: string | null
        }
        Update: {
          data_type?: string | null
          description?: string | null
          id?: string
          is_public?: boolean | null
          key?: string
          updated_at?: string | null
          updated_by?: string | null
          value?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "clinic_settings_updated_by_fkey"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      cms_media: {
        Row: {
          alt_text: Json | null
          created_at: string | null
          description: Json | null
          file_size: number
          filename: string
          height: number | null
          id: string
          is_active: boolean | null
          mime_type: string
          original_filename: string
          public_url: string | null
          storage_path: string
          tags: string[] | null
          uploaded_by: string | null
          width: number | null
        }
        Insert: {
          alt_text?: Json | null
          created_at?: string | null
          description?: Json | null
          file_size: number
          filename: string
          height?: number | null
          id?: string
          is_active?: boolean | null
          mime_type: string
          original_filename: string
          public_url?: string | null
          storage_path: string
          tags?: string[] | null
          uploaded_by?: string | null
          width?: number | null
        }
        Update: {
          alt_text?: Json | null
          created_at?: string | null
          description?: Json | null
          file_size?: number
          filename?: string
          height?: number | null
          id?: string
          is_active?: boolean | null
          mime_type?: string
          original_filename?: string
          public_url?: string | null
          storage_path?: string
          tags?: string[] | null
          uploaded_by?: string | null
          width?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "cms_media_uploaded_by_fkey"
            columns: ["uploaded_by"]
            isOneToOne: false
            referencedRelation: "admin_users"
            referencedColumns: ["id"]
          },
        ]
      }
      cms_page_categories: {
        Row: {
          category_id: string
          page_id: string
        }
        Insert: {
          category_id: string
          page_id: string
        }
        Update: {
          category_id?: string
          page_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "cms_page_categories_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "blog_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "cms_page_categories_page_id_fkey"
            columns: ["page_id"]
            isOneToOne: false
            referencedRelation: "cms_pages"
            referencedColumns: ["id"]
          },
        ]
      }
      cms_page_tags: {
        Row: {
          page_id: string
          tag_id: string
        }
        Insert: {
          page_id: string
          tag_id: string
        }
        Update: {
          page_id?: string
          tag_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "cms_page_tags_page_id_fkey"
            columns: ["page_id"]
            isOneToOne: false
            referencedRelation: "cms_pages"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "cms_page_tags_tag_id_fkey"
            columns: ["tag_id"]
            isOneToOne: false
            referencedRelation: "blog_tags"
            referencedColumns: ["id"]
          },
        ]
      }
      cms_pages: {
        Row: {
          content: Json
          created_at: string | null
          created_by: string | null
          excerpt: Json | null
          featured_image_url: string | null
          id: string
          is_published: boolean | null
          meta_description: Json | null
          meta_keywords: string[] | null
          meta_title: Json | null
          page_type: string
          publish_at: string | null
          slug: string
          sort_order: number | null
          template: string | null
          title: Json
          updated_at: string | null
          updated_by: string | null
          view_count: number | null
        }
        Insert: {
          content: Json
          created_at?: string | null
          created_by?: string | null
          excerpt?: Json | null
          featured_image_url?: string | null
          id?: string
          is_published?: boolean | null
          meta_description?: Json | null
          meta_keywords?: string[] | null
          meta_title?: Json | null
          page_type?: string
          publish_at?: string | null
          slug: string
          sort_order?: number | null
          template?: string | null
          title: Json
          updated_at?: string | null
          updated_by?: string | null
          view_count?: number | null
        }
        Update: {
          content?: Json
          created_at?: string | null
          created_by?: string | null
          excerpt?: Json | null
          featured_image_url?: string | null
          id?: string
          is_published?: boolean | null
          meta_description?: Json | null
          meta_keywords?: string[] | null
          meta_title?: Json | null
          page_type?: string
          publish_at?: string | null
          slug?: string
          sort_order?: number | null
          template?: string | null
          title?: Json
          updated_at?: string | null
          updated_by?: string | null
          view_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "cms_pages_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "admin_users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "cms_pages_updated_by_fkey"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "admin_users"
            referencedColumns: ["id"]
          },
        ]
      }
      customer_interactions: {
        Row: {
          attachments: Json | null
          completed_at: string | null
          content: string | null
          created_at: string | null
          created_by: string | null
          direction: string
          follow_up_date: string | null
          id: string
          interaction_type: string
          outcome: string | null
          patient_id: string | null
          priority: Database["public"]["Enums"]["interaction_priority"] | null
          scheduled_at: string | null
          status: string | null
          subject: string | null
          tags: string[] | null
          updated_at: string | null
        }
        Insert: {
          attachments?: Json | null
          completed_at?: string | null
          content?: string | null
          created_at?: string | null
          created_by?: string | null
          direction?: string
          follow_up_date?: string | null
          id?: string
          interaction_type: string
          outcome?: string | null
          patient_id?: string | null
          priority?: Database["public"]["Enums"]["interaction_priority"] | null
          scheduled_at?: string | null
          status?: string | null
          subject?: string | null
          tags?: string[] | null
          updated_at?: string | null
        }
        Update: {
          attachments?: Json | null
          completed_at?: string | null
          content?: string | null
          created_at?: string | null
          created_by?: string | null
          direction?: string
          follow_up_date?: string | null
          id?: string
          interaction_type?: string
          outcome?: string | null
          patient_id?: string | null
          priority?: Database["public"]["Enums"]["interaction_priority"] | null
          scheduled_at?: string | null
          status?: string | null
          subject?: string | null
          tags?: string[] | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "customer_interactions_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "admin_users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_interactions_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      customer_notes: {
        Row: {
          content: string
          created_at: string | null
          created_by: string | null
          id: string
          is_important: boolean | null
          is_private: boolean | null
          note_type: string | null
          patient_id: string | null
          tags: string[] | null
          title: string | null
          updated_at: string | null
        }
        Insert: {
          content: string
          created_at?: string | null
          created_by?: string | null
          id?: string
          is_important?: boolean | null
          is_private?: boolean | null
          note_type?: string | null
          patient_id?: string | null
          tags?: string[] | null
          title?: string | null
          updated_at?: string | null
        }
        Update: {
          content?: string
          created_at?: string | null
          created_by?: string | null
          id?: string
          is_important?: boolean | null
          is_private?: boolean | null
          note_type?: string | null
          patient_id?: string | null
          tags?: string[] | null
          title?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "customer_notes_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "admin_users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_notes_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      customer_segments: {
        Row: {
          color: string | null
          created_at: string | null
          created_by: string | null
          criteria: Json
          description: string | null
          icon: string | null
          id: string
          is_dynamic: boolean | null
          last_updated_at: string | null
          name: string
          patient_count: number | null
        }
        Insert: {
          color?: string | null
          created_at?: string | null
          created_by?: string | null
          criteria: Json
          description?: string | null
          icon?: string | null
          id?: string
          is_dynamic?: boolean | null
          last_updated_at?: string | null
          name: string
          patient_count?: number | null
        }
        Update: {
          color?: string | null
          created_at?: string | null
          created_by?: string | null
          criteria?: Json
          description?: string | null
          icon?: string | null
          id?: string
          is_dynamic?: boolean | null
          last_updated_at?: string | null
          name?: string
          patient_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "customer_segments_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "admin_users"
            referencedColumns: ["id"]
          },
        ]
      }
      doctor_services: {
        Row: {
          created_at: string | null
          custom_price: number | null
          doctor_id: string | null
          id: string
          is_available: boolean | null
          service_id: string | null
        }
        Insert: {
          created_at?: string | null
          custom_price?: number | null
          doctor_id?: string | null
          id?: string
          is_available?: boolean | null
          service_id?: string | null
        }
        Update: {
          created_at?: string | null
          custom_price?: number | null
          doctor_id?: string | null
          id?: string
          is_available?: boolean | null
          service_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "doctor_services_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "doctors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "doctor_services_service_id_fkey"
            columns: ["service_id"]
            isOneToOne: false
            referencedRelation: "services"
            referencedColumns: ["id"]
          },
        ]
      }
      doctors: {
        Row: {
          bio: string | null
          consultation_fee: number | null
          created_at: string | null
          experience_years: number | null
          id: string
          image_url: string | null
          is_available: boolean | null
          languages_spoken: string[] | null
          license_number: string
          qualification: string
          rating: number | null
          specialization: string
          total_reviews: number | null
          updated_at: string | null
          working_hours: Json | null
        }
        Insert: {
          bio?: string | null
          consultation_fee?: number | null
          created_at?: string | null
          experience_years?: number | null
          id: string
          image_url?: string | null
          is_available?: boolean | null
          languages_spoken?: string[] | null
          license_number: string
          qualification: string
          rating?: number | null
          specialization: string
          total_reviews?: number | null
          updated_at?: string | null
          working_hours?: Json | null
        }
        Update: {
          bio?: string | null
          consultation_fee?: number | null
          created_at?: string | null
          experience_years?: number | null
          id?: string
          image_url?: string | null
          is_available?: boolean | null
          languages_spoken?: string[] | null
          license_number?: string
          qualification?: string
          rating?: number | null
          specialization?: string
          total_reviews?: number | null
          updated_at?: string | null
          working_hours?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "doctors_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      email_templates: {
        Row: {
          category: string
          created_at: string | null
          created_by: string | null
          html_content: Json
          id: string
          is_active: boolean | null
          name: string
          subject: Json
          text_content: Json | null
          updated_at: string | null
          usage_count: number | null
          variables: string[] | null
        }
        Insert: {
          category: string
          created_at?: string | null
          created_by?: string | null
          html_content: Json
          id?: string
          is_active?: boolean | null
          name: string
          subject: Json
          text_content?: Json | null
          updated_at?: string | null
          usage_count?: number | null
          variables?: string[] | null
        }
        Update: {
          category?: string
          created_at?: string | null
          created_by?: string | null
          html_content?: Json
          id?: string
          is_active?: boolean | null
          name?: string
          subject?: Json
          text_content?: Json | null
          updated_at?: string | null
          usage_count?: number | null
          variables?: string[] | null
        }
        Relationships: [
          {
            foreignKeyName: "email_templates_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "admin_users"
            referencedColumns: ["id"]
          },
        ]
      }
      inventory_items: {
        Row: {
          barcode: string | null
          category: string
          created_at: string | null
          created_by: string | null
          current_stock: number | null
          description: string | null
          expiry_tracking: boolean | null
          id: string
          is_active: boolean | null
          maximum_stock: number | null
          minimum_stock: number | null
          name: string
          reorder_point: number | null
          sku: string | null
          storage_location: string | null
          supplier: string | null
          supplier_contact: string | null
          unit_cost: number | null
          unit_of_measure: string
          updated_at: string | null
        }
        Insert: {
          barcode?: string | null
          category: string
          created_at?: string | null
          created_by?: string | null
          current_stock?: number | null
          description?: string | null
          expiry_tracking?: boolean | null
          id?: string
          is_active?: boolean | null
          maximum_stock?: number | null
          minimum_stock?: number | null
          name: string
          reorder_point?: number | null
          sku?: string | null
          storage_location?: string | null
          supplier?: string | null
          supplier_contact?: string | null
          unit_cost?: number | null
          unit_of_measure: string
          updated_at?: string | null
        }
        Update: {
          barcode?: string | null
          category?: string
          created_at?: string | null
          created_by?: string | null
          current_stock?: number | null
          description?: string | null
          expiry_tracking?: boolean | null
          id?: string
          is_active?: boolean | null
          maximum_stock?: number | null
          minimum_stock?: number | null
          name?: string
          reorder_point?: number | null
          sku?: string | null
          storage_location?: string | null
          supplier?: string | null
          supplier_contact?: string | null
          unit_cost?: number | null
          unit_of_measure?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "inventory_items_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "admin_users"
            referencedColumns: ["id"]
          },
        ]
      }
      inventory_transactions: {
        Row: {
          batch_number: string | null
          created_at: string | null
          created_by: string | null
          expiry_date: string | null
          id: string
          item_id: string | null
          notes: string | null
          quantity: number
          reference_id: string | null
          reference_type: string | null
          total_cost: number | null
          transaction_type: string
          unit_cost: number | null
        }
        Insert: {
          batch_number?: string | null
          created_at?: string | null
          created_by?: string | null
          expiry_date?: string | null
          id?: string
          item_id?: string | null
          notes?: string | null
          quantity: number
          reference_id?: string | null
          reference_type?: string | null
          total_cost?: number | null
          transaction_type: string
          unit_cost?: number | null
        }
        Update: {
          batch_number?: string | null
          created_at?: string | null
          created_by?: string | null
          expiry_date?: string | null
          id?: string
          item_id?: string | null
          notes?: string | null
          quantity?: number
          reference_id?: string | null
          reference_type?: string | null
          total_cost?: number | null
          transaction_type?: string
          unit_cost?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "inventory_transactions_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "admin_users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_transactions_item_id_fkey"
            columns: ["item_id"]
            isOneToOne: false
            referencedRelation: "inventory_items"
            referencedColumns: ["id"]
          },
        ]
      }
      invoices: {
        Row: {
          appointment_id: string | null
          created_at: string | null
          currency: string | null
          discount_amount: number | null
          due_date: string | null
          id: string
          invoice_number: string
          is_paid: boolean | null
          issue_date: string
          notes: string | null
          payment_id: string | null
          pdf_url: string | null
          subtotal: number
          tax_amount: number | null
          total_amount: number
        }
        Insert: {
          appointment_id?: string | null
          created_at?: string | null
          currency?: string | null
          discount_amount?: number | null
          due_date?: string | null
          id?: string
          invoice_number: string
          is_paid?: boolean | null
          issue_date?: string
          notes?: string | null
          payment_id?: string | null
          pdf_url?: string | null
          subtotal: number
          tax_amount?: number | null
          total_amount: number
        }
        Update: {
          appointment_id?: string | null
          created_at?: string | null
          currency?: string | null
          discount_amount?: number | null
          due_date?: string | null
          id?: string
          invoice_number?: string
          is_paid?: boolean | null
          issue_date?: string
          notes?: string | null
          payment_id?: string | null
          pdf_url?: string | null
          subtotal?: number
          tax_amount?: number | null
          total_amount?: number
        }
        Relationships: [
          {
            foreignKeyName: "invoices_appointment_id_fkey"
            columns: ["appointment_id"]
            isOneToOne: false
            referencedRelation: "appointments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_payment_id_fkey"
            columns: ["payment_id"]
            isOneToOne: false
            referencedRelation: "payments"
            referencedColumns: ["id"]
          },
        ]
      }
      kpi_metrics: {
        Row: {
          category: string
          created_at: string | null
          date: string
          id: string
          metric_name: string
          metric_unit: string | null
          metric_value: number
        }
        Insert: {
          category: string
          created_at?: string | null
          date: string
          id?: string
          metric_name: string
          metric_unit?: string | null
          metric_value: number
        }
        Update: {
          category?: string
          created_at?: string | null
          date?: string
          id?: string
          metric_name?: string
          metric_unit?: string | null
          metric_value?: number
        }
        Relationships: []
      }
      marketing_campaigns: {
        Row: {
          actual_cost: number | null
          bounced_count: number | null
          budget_amount: number | null
          campaign_type: string
          clicked_count: number | null
          completed_at: string | null
          content: Json
          created_at: string | null
          created_by: string | null
          delivered_count: number | null
          description: string | null
          email_template_id: string | null
          id: string
          name: string
          opened_count: number | null
          roi_percentage: number | null
          scheduled_at: string | null
          sender_email: string | null
          sender_name: string | null
          sent_count: number | null
          started_at: string | null
          status: Database["public"]["Enums"]["campaign_status"] | null
          subject_line: Json | null
          target_segment: string | null
          total_recipients: number | null
          unsubscribed_count: number | null
          updated_at: string | null
        }
        Insert: {
          actual_cost?: number | null
          bounced_count?: number | null
          budget_amount?: number | null
          campaign_type: string
          clicked_count?: number | null
          completed_at?: string | null
          content: Json
          created_at?: string | null
          created_by?: string | null
          delivered_count?: number | null
          description?: string | null
          email_template_id?: string | null
          id?: string
          name: string
          opened_count?: number | null
          roi_percentage?: number | null
          scheduled_at?: string | null
          sender_email?: string | null
          sender_name?: string | null
          sent_count?: number | null
          started_at?: string | null
          status?: Database["public"]["Enums"]["campaign_status"] | null
          subject_line?: Json | null
          target_segment?: string | null
          total_recipients?: number | null
          unsubscribed_count?: number | null
          updated_at?: string | null
        }
        Update: {
          actual_cost?: number | null
          bounced_count?: number | null
          budget_amount?: number | null
          campaign_type?: string
          clicked_count?: number | null
          completed_at?: string | null
          content?: Json
          created_at?: string | null
          created_by?: string | null
          delivered_count?: number | null
          description?: string | null
          email_template_id?: string | null
          id?: string
          name?: string
          opened_count?: number | null
          roi_percentage?: number | null
          scheduled_at?: string | null
          sender_email?: string | null
          sender_name?: string | null
          sent_count?: number | null
          started_at?: string | null
          status?: Database["public"]["Enums"]["campaign_status"] | null
          subject_line?: Json | null
          target_segment?: string | null
          total_recipients?: number | null
          unsubscribed_count?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "marketing_campaigns_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "admin_users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "marketing_campaigns_email_template_id_fkey"
            columns: ["email_template_id"]
            isOneToOne: false
            referencedRelation: "email_templates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "marketing_campaigns_target_segment_fkey"
            columns: ["target_segment"]
            isOneToOne: false
            referencedRelation: "customer_segments"
            referencedColumns: ["id"]
          },
        ]
      }
      newsletter_subscribers: {
        Row: {
          email: string
          first_name: string | null
          id: string
          is_active: boolean | null
          language: string | null
          last_name: string | null
          preferences: Json | null
          source: string | null
          subscribed_at: string | null
          subscriber_tags: string[] | null
          unsubscribed_at: string | null
        }
        Insert: {
          email: string
          first_name?: string | null
          id?: string
          is_active?: boolean | null
          language?: string | null
          last_name?: string | null
          preferences?: Json | null
          source?: string | null
          subscribed_at?: string | null
          subscriber_tags?: string[] | null
          unsubscribed_at?: string | null
        }
        Update: {
          email?: string
          first_name?: string | null
          id?: string
          is_active?: boolean | null
          language?: string | null
          last_name?: string | null
          preferences?: Json | null
          source?: string | null
          subscribed_at?: string | null
          subscriber_tags?: string[] | null
          unsubscribed_at?: string | null
        }
        Relationships: []
      }
      notifications: {
        Row: {
          appointment_id: string | null
          channel: string
          content: string
          created_at: string | null
          delivery_status: string | null
          error_message: string | null
          id: string
          metadata: Json | null
          recipient_id: string | null
          sent_at: string | null
          subject: string | null
          template_id: string | null
          type: Database["public"]["Enums"]["notification_type"]
        }
        Insert: {
          appointment_id?: string | null
          channel: string
          content: string
          created_at?: string | null
          delivery_status?: string | null
          error_message?: string | null
          id?: string
          metadata?: Json | null
          recipient_id?: string | null
          sent_at?: string | null
          subject?: string | null
          template_id?: string | null
          type: Database["public"]["Enums"]["notification_type"]
        }
        Update: {
          appointment_id?: string | null
          channel?: string
          content?: string
          created_at?: string | null
          delivery_status?: string | null
          error_message?: string | null
          id?: string
          metadata?: Json | null
          recipient_id?: string | null
          sent_at?: string | null
          subject?: string | null
          template_id?: string | null
          type?: Database["public"]["Enums"]["notification_type"]
        }
        Relationships: [
          {
            foreignKeyName: "notifications_appointment_id_fkey"
            columns: ["appointment_id"]
            isOneToOne: false
            referencedRelation: "appointments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_recipient_id_fkey"
            columns: ["recipient_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      payments: {
        Row: {
          amount: number
          appointment_id: string | null
          created_at: string | null
          id: string
          metadata: Json | null
          patient_id: string | null
          payment_date: string | null
          payment_method: Database["public"]["Enums"]["payment_method"]
          payment_status: Database["public"]["Enums"]["payment_status"] | null
          receipt_url: string | null
          refund_amount: number | null
          refund_date: string | null
          refund_reason: string | null
          stripe_payment_intent_id: string | null
          transaction_id: string | null
          updated_at: string | null
        }
        Insert: {
          amount: number
          appointment_id?: string | null
          created_at?: string | null
          id?: string
          metadata?: Json | null
          patient_id?: string | null
          payment_date?: string | null
          payment_method: Database["public"]["Enums"]["payment_method"]
          payment_status?: Database["public"]["Enums"]["payment_status"] | null
          receipt_url?: string | null
          refund_amount?: number | null
          refund_date?: string | null
          refund_reason?: string | null
          stripe_payment_intent_id?: string | null
          transaction_id?: string | null
          updated_at?: string | null
        }
        Update: {
          amount?: number
          appointment_id?: string | null
          created_at?: string | null
          id?: string
          metadata?: Json | null
          patient_id?: string | null
          payment_date?: string | null
          payment_method?: Database["public"]["Enums"]["payment_method"]
          payment_status?: Database["public"]["Enums"]["payment_status"] | null
          receipt_url?: string | null
          refund_amount?: number | null
          refund_date?: string | null
          refund_reason?: string | null
          stripe_payment_intent_id?: string | null
          transaction_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "payments_appointment_id_fkey"
            columns: ["appointment_id"]
            isOneToOne: false
            referencedRelation: "appointments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payments_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      promotion_usage: {
        Row: {
          appointment_id: string | null
          discount_amount: number
          id: string
          patient_id: string | null
          promotion_id: string | null
          used_at: string | null
        }
        Insert: {
          appointment_id?: string | null
          discount_amount: number
          id?: string
          patient_id?: string | null
          promotion_id?: string | null
          used_at?: string | null
        }
        Update: {
          appointment_id?: string | null
          discount_amount?: number
          id?: string
          patient_id?: string | null
          promotion_id?: string | null
          used_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "promotion_usage_appointment_id_fkey"
            columns: ["appointment_id"]
            isOneToOne: false
            referencedRelation: "appointments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "promotion_usage_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "promotion_usage_promotion_id_fkey"
            columns: ["promotion_id"]
            isOneToOne: false
            referencedRelation: "promotions"
            referencedColumns: ["id"]
          },
        ]
      }
      promotions: {
        Row: {
          applicable_services: string[] | null
          created_at: string | null
          description: string
          discount_type: string
          discount_value: number
          end_date: string
          id: string
          image_url: string | null
          is_active: boolean | null
          is_featured: boolean | null
          max_discount_amount: number | null
          min_purchase_amount: number | null
          promo_code: string | null
          start_date: string
          terms_conditions: string | null
          title: string
          updated_at: string | null
          usage_count: number | null
          usage_limit: number | null
        }
        Insert: {
          applicable_services?: string[] | null
          created_at?: string | null
          description: string
          discount_type: string
          discount_value: number
          end_date: string
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          is_featured?: boolean | null
          max_discount_amount?: number | null
          min_purchase_amount?: number | null
          promo_code?: string | null
          start_date: string
          terms_conditions?: string | null
          title: string
          updated_at?: string | null
          usage_count?: number | null
          usage_limit?: number | null
        }
        Update: {
          applicable_services?: string[] | null
          created_at?: string | null
          description?: string
          discount_type?: string
          discount_value?: number
          end_date?: string
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          is_featured?: boolean | null
          max_discount_amount?: number | null
          min_purchase_amount?: number | null
          promo_code?: string | null
          start_date?: string
          terms_conditions?: string | null
          title?: string
          updated_at?: string | null
          usage_count?: number | null
          usage_limit?: number | null
        }
        Relationships: []
      }
      reviews: {
        Row: {
          after_photo_url: string | null
          appointment_id: string | null
          before_photo_url: string | null
          comment: string | null
          created_at: string | null
          doctor_id: string | null
          helpful_count: number | null
          id: string
          is_anonymous: boolean | null
          is_approved: boolean | null
          is_featured: boolean | null
          patient_id: string | null
          rating: number
          service_id: string | null
          title: string | null
          updated_at: string | null
        }
        Insert: {
          after_photo_url?: string | null
          appointment_id?: string | null
          before_photo_url?: string | null
          comment?: string | null
          created_at?: string | null
          doctor_id?: string | null
          helpful_count?: number | null
          id?: string
          is_anonymous?: boolean | null
          is_approved?: boolean | null
          is_featured?: boolean | null
          patient_id?: string | null
          rating: number
          service_id?: string | null
          title?: string | null
          updated_at?: string | null
        }
        Update: {
          after_photo_url?: string | null
          appointment_id?: string | null
          before_photo_url?: string | null
          comment?: string | null
          created_at?: string | null
          doctor_id?: string | null
          helpful_count?: number | null
          id?: string
          is_anonymous?: boolean | null
          is_approved?: boolean | null
          is_featured?: boolean | null
          patient_id?: string | null
          rating?: number
          service_id?: string | null
          title?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "reviews_appointment_id_fkey"
            columns: ["appointment_id"]
            isOneToOne: false
            referencedRelation: "appointments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reviews_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "doctors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reviews_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reviews_service_id_fkey"
            columns: ["service_id"]
            isOneToOne: false
            referencedRelation: "services"
            referencedColumns: ["id"]
          },
        ]
      }
      segment_memberships: {
        Row: {
          added_at: string | null
          added_by: string | null
          patient_id: string
          segment_id: string
        }
        Insert: {
          added_at?: string | null
          added_by?: string | null
          patient_id: string
          segment_id: string
        }
        Update: {
          added_at?: string | null
          added_by?: string | null
          patient_id?: string
          segment_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "segment_memberships_added_by_fkey"
            columns: ["added_by"]
            isOneToOne: false
            referencedRelation: "admin_users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "segment_memberships_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "segment_memberships_segment_id_fkey"
            columns: ["segment_id"]
            isOneToOne: false
            referencedRelation: "customer_segments"
            referencedColumns: ["id"]
          },
        ]
      }
      service_categories: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          image_url: string | null
          is_active: boolean | null
          name: string
          slug: string
          sort_order: number | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          name: string
          slug: string
          sort_order?: number | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          name?: string
          slug?: string
          sort_order?: number | null
        }
        Relationships: []
      }
      services: {
        Row: {
          aftercare_instructions: string | null
          base_price: number
          benefits: string[] | null
          category_id: string | null
          contraindications: string | null
          created_at: string | null
          description: string
          difficulty: Database["public"]["Enums"]["treatment_difficulty"] | null
          discounted_price: number | null
          duration_minutes: number
          gallery_images: string[] | null
          id: string
          image_url: string | null
          is_active: boolean | null
          is_featured: boolean | null
          is_popular: boolean | null
          name: string
          preparation_instructions: string | null
          procedures: string[] | null
          seo_description: string | null
          seo_keywords: string[] | null
          seo_title: string | null
          short_description: string | null
          slug: string
          sort_order: number | null
          updated_at: string | null
        }
        Insert: {
          aftercare_instructions?: string | null
          base_price: number
          benefits?: string[] | null
          category_id?: string | null
          contraindications?: string | null
          created_at?: string | null
          description: string
          difficulty?:
            | Database["public"]["Enums"]["treatment_difficulty"]
            | null
          discounted_price?: number | null
          duration_minutes?: number
          gallery_images?: string[] | null
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          is_featured?: boolean | null
          is_popular?: boolean | null
          name: string
          preparation_instructions?: string | null
          procedures?: string[] | null
          seo_description?: string | null
          seo_keywords?: string[] | null
          seo_title?: string | null
          short_description?: string | null
          slug: string
          sort_order?: number | null
          updated_at?: string | null
        }
        Update: {
          aftercare_instructions?: string | null
          base_price?: number
          benefits?: string[] | null
          category_id?: string | null
          contraindications?: string | null
          created_at?: string | null
          description?: string
          difficulty?:
            | Database["public"]["Enums"]["treatment_difficulty"]
            | null
          discounted_price?: number | null
          duration_minutes?: number
          gallery_images?: string[] | null
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          is_featured?: boolean | null
          is_popular?: boolean | null
          name?: string
          preparation_instructions?: string | null
          procedures?: string[] | null
          seo_description?: string | null
          seo_keywords?: string[] | null
          seo_title?: string | null
          short_description?: string | null
          slug?: string
          sort_order?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "services_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "service_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      time_slots: {
        Row: {
          created_at: string | null
          date: string
          doctor_id: string | null
          end_time: string
          id: string
          is_available: boolean | null
          is_recurring: boolean | null
          recurring_pattern: Json | null
          start_time: string
        }
        Insert: {
          created_at?: string | null
          date: string
          doctor_id?: string | null
          end_time: string
          id?: string
          is_available?: boolean | null
          is_recurring?: boolean | null
          recurring_pattern?: Json | null
          start_time: string
        }
        Update: {
          created_at?: string | null
          date?: string
          doctor_id?: string | null
          end_time?: string
          id?: string
          is_available?: boolean | null
          is_recurring?: boolean | null
          recurring_pattern?: Json | null
          start_time?: string
        }
        Relationships: [
          {
            foreignKeyName: "time_slots_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      user_profiles: {
        Row: {
          address: string | null
          allergies: string | null
          city: string | null
          country: string | null
          created_at: string | null
          current_medications: string | null
          date_of_birth: string | null
          email: string
          emergency_contact_name: string | null
          emergency_contact_phone: string | null
          first_name: string
          gender: string | null
          id: string
          is_active: boolean | null
          last_login: string | null
          last_name: string
          marketing_consent: boolean | null
          medical_history: string | null
          phone: string | null
          preferred_language: string | null
          privacy_consent: boolean
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string | null
        }
        Insert: {
          address?: string | null
          allergies?: string | null
          city?: string | null
          country?: string | null
          created_at?: string | null
          current_medications?: string | null
          date_of_birth?: string | null
          email: string
          emergency_contact_name?: string | null
          emergency_contact_phone?: string | null
          first_name: string
          gender?: string | null
          id: string
          is_active?: boolean | null
          last_login?: string | null
          last_name: string
          marketing_consent?: boolean | null
          medical_history?: string | null
          phone?: string | null
          preferred_language?: string | null
          privacy_consent?: boolean
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
        }
        Update: {
          address?: string | null
          allergies?: string | null
          city?: string | null
          country?: string | null
          created_at?: string | null
          current_medications?: string | null
          date_of_birth?: string | null
          email?: string
          emergency_contact_name?: string | null
          emergency_contact_phone?: string | null
          first_name?: string
          gender?: string | null
          id?: string
          is_active?: boolean | null
          last_login?: string | null
          last_name?: string
          marketing_consent?: boolean | null
          medical_history?: string | null
          phone?: string | null
          preferred_language?: string | null
          privacy_consent?: boolean
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
        }
        Relationships: []
      }
      website_metrics: {
        Row: {
          avg_session_duration: number | null
          bounce_rate: number | null
          conversion_rate: number | null
          created_at: string | null
          date: string
          id: string
          page_path: string
          page_views: number | null
          unique_visitors: number | null
        }
        Insert: {
          avg_session_duration?: number | null
          bounce_rate?: number | null
          conversion_rate?: number | null
          created_at?: string | null
          date: string
          id?: string
          page_path: string
          page_views?: number | null
          unique_visitors?: number | null
        }
        Update: {
          avg_session_duration?: number | null
          bounce_rate?: number | null
          conversion_rate?: number | null
          created_at?: string | null
          date?: string
          id?: string
          page_path?: string
          page_views?: number | null
          unique_visitors?: number | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_admin_role: {
        Args: { user_id: string }
        Returns: Database["public"]["Enums"]["admin_role"]
      }
      get_current_user_role: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      has_admin_permission: {
        Args: { user_id: string; permission_name: string }
        Returns: boolean
      }
      is_admin_user: {
        Args: { user_id: string }
        Returns: boolean
      }
      is_current_user_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
    }
    Enums: {
      admin_role:
        | "super_admin"
        | "admin"
        | "manager"
        | "staff"
        | "content_editor"
        | "analyst"
      appointment_status:
        | "scheduled"
        | "confirmed"
        | "in_progress"
        | "completed"
        | "cancelled"
        | "no_show"
      campaign_status:
        | "draft"
        | "scheduled"
        | "active"
        | "paused"
        | "completed"
        | "cancelled"
      interaction_priority: "low" | "medium" | "high" | "urgent"
      notification_type:
        | "appointment_confirmation"
        | "appointment_reminder"
        | "payment_confirmation"
        | "treatment_followup"
        | "marketing"
      payment_method: "cash" | "card" | "bank_transfer" | "insurance"
      payment_status: "pending" | "paid" | "failed" | "refunded" | "partial"
      treatment_difficulty: "beginner" | "intermediate" | "advanced"
      user_role: "patient" | "doctor" | "admin" | "staff"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      admin_role: [
        "super_admin",
        "admin",
        "manager",
        "staff",
        "content_editor",
        "analyst",
      ],
      appointment_status: [
        "scheduled",
        "confirmed",
        "in_progress",
        "completed",
        "cancelled",
        "no_show",
      ],
      campaign_status: [
        "draft",
        "scheduled",
        "active",
        "paused",
        "completed",
        "cancelled",
      ],
      interaction_priority: ["low", "medium", "high", "urgent"],
      notification_type: [
        "appointment_confirmation",
        "appointment_reminder",
        "payment_confirmation",
        "treatment_followup",
        "marketing",
      ],
      payment_method: ["cash", "card", "bank_transfer", "insurance"],
      payment_status: ["pending", "paid", "failed", "refunded", "partial"],
      treatment_difficulty: ["beginner", "intermediate", "advanced"],
      user_role: ["patient", "doctor", "admin", "staff"],
    },
  },
} as const
