
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { useServices, useCreateAppointment } from '@/hooks/useSupabase';
import { useAuth } from '@/contexts/AuthProvider';
import { BookingStep, CreateAppointmentInput } from '@/types';
import { toast } from 'sonner';
import BookingSteps from '@/components/booking/BookingSteps';
import ServiceSelection from '@/components/booking/ServiceSelection';
import BookingConfirmation from '@/components/booking/BookingConfirmation';
import BookingNavigation from '@/components/booking/BookingNavigation';

const BookingSystem = () => {
  const { user } = useAuth();
  const { data: servicesData } = useServices();
  const createAppointmentMutation = useCreateAppointment();
  
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedService, setSelectedService] = useState<string>('');
  const [selectedDoctor, setSelectedDoctor] = useState<string>('');
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [patientNotes, setPatientNotes] = useState<string>('');

  const services = servicesData?.data || [];

  const steps: BookingStep[] = [
    {
      id: 'service',
      title: 'Select Service',
      description: 'Choose the service you need',
      isCompleted: !!selectedService,
      isActive: currentStep === 0
    },
    {
      id: 'doctor',
      title: 'Select Doctor',
      description: 'Choose your preferred doctor',
      isCompleted: !!selectedDoctor,
      isActive: currentStep === 1
    },
    {
      id: 'datetime',
      title: 'Select Date & Time',
      description: 'Pick your appointment slot',
      isCompleted: !!selectedDate,
      isActive: currentStep === 2
    },
    {
      id: 'confirmation',
      title: 'Confirmation',
      description: 'Review and confirm your booking',
      isCompleted: false,
      isActive: currentStep === 3
    }
  ];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrev = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0: return !!selectedService;
      case 1: return !!selectedDoctor;
      case 2: return !!selectedDate;
      default: return true;
    }
  };

  const handleBooking = async () => {
    if (!user || !selectedService || !selectedDoctor || !selectedDate) {
      toast.error('Please complete all booking steps');
      return;
    }

    const selectedServiceData = services.find(s => s.id === selectedService);
    if (!selectedServiceData) {
      toast.error('Service not found');
      return;
    }

    const appointmentData: CreateAppointmentInput = {
      service_id: selectedService,
      doctor_id: selectedDoctor,
      appointment_date: selectedDate,
      duration_minutes: selectedServiceData.duration_minutes,
      total_amount: selectedServiceData.base_price,
      deposit_amount: selectedServiceData.base_price * 0.3,
      patient_notes: patientNotes
    };

    try {
      await createAppointmentMutation.mutateAsync(appointmentData);
      toast.success('Appointment booked successfully!');
      setCurrentStep(0);
      setSelectedService('');
      setSelectedDoctor('');
      setSelectedDate('');
      setPatientNotes('');
    } catch (error) {
      toast.error('Failed to book appointment');
    }
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="pt-6">
          <p className="text-center text-muted-foreground">
            Please log in to book an appointment
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <BookingSteps steps={steps} currentStep={currentStep} />

      <Card>
        <CardHeader>
          <CardTitle>{steps[currentStep]?.title}</CardTitle>
        </CardHeader>
        <CardContent>
          {currentStep === 0 && (
            <ServiceSelection
              services={services}
              selectedService={selectedService}
              onServiceSelect={setSelectedService}
            />
          )}

          {currentStep === 1 && (
            <div className="space-y-4">
              <h3 className="font-medium">Select a Doctor</h3>
              <p className="text-muted-foreground">Doctor selection coming soon...</p>
            </div>
          )}

          {currentStep === 2 && (
            <div className="space-y-4">
              <h3 className="font-medium">Select Date & Time</h3>
              <p className="text-muted-foreground">Date/time selection coming soon...</p>
            </div>
          )}

          {currentStep === 3 && (
            <BookingConfirmation
              services={services}
              selectedService={selectedService}
              selectedDoctor={selectedDoctor}
              selectedDate={selectedDate}
              patientNotes={patientNotes}
              onNotesChange={setPatientNotes}
            />
          )}
        </CardContent>
      </Card>

      <BookingNavigation
        currentStep={currentStep}
        totalSteps={steps.length}
        canProceed={canProceed()}
        isLoading={createAppointmentMutation.isPending}
        onPrevious={handlePrev}
        onNext={handleNext}
        onConfirm={handleBooking}
      />
    </div>
  );
};

export default BookingSystem;
