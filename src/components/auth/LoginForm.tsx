
/**
 * Lullaby Clinic - Login Form Component
 * Secure patient authentication with Supabase
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import React, { useState } from 'react';
import { Eye, EyeOff, Mail, Lock, AlertCircle, Loader2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/hooks/useSupabase';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import SocialAuthButtons from './SocialAuthButtons';

interface LoginFormProps {
  onSuccess?: () => void;
  redirectTo?: string;
  showSignupLink?: boolean;
}

const LoginForm: React.FC<LoginFormProps> = ({
  onSuccess,
  redirectTo = '/dashboard',
  showSignupLink = true
}) => {
  const { t } = useLanguage();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { signIn } = useAuth();

  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Get redirect URL from search params or use default
  const finalRedirectTo = searchParams.get('redirect') || redirectTo;

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.email) {
      newErrors.email = t('validation.emailRequired');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = t('validation.emailInvalid');
    }

    if (!formData.password) {
      newErrors.password = t('validation.passwordRequired');
    } else if (formData.password.length < 6) {
      newErrors.password = t('validation.passwordTooShort');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const result = await signIn.mutateAsync({
        email: formData.email,
        password: formData.password
      });

      if (result.error) {
        // Handle specific auth errors
        if (result.error.message.includes('Invalid login credentials')) {
          setErrors({ general: t('auth.invalidCredentials') });
        } else if (result.error.message.includes('Email not confirmed')) {
          setErrors({ general: t('auth.emailNotConfirmed') });
        } else {
          setErrors({ general: result.error.message });
        }
        return;
      }

      // Success
      toast({
        title: t('auth.loginSuccess'),
        description: t('auth.welcomeBack')
      });

      if (onSuccess) {
        onSuccess();
      } else {
        navigate(finalRedirectTo);
      }
    } catch (error) {
      setErrors({ 
        general: error instanceof Error ? error.message : t('auth.loginError') 
      });
    }
  };

  const handleForgotPassword = () => {
    if (formData.email) {
      navigate(`/auth/reset-password?email=${encodeURIComponent(formData.email)}`);
    } else {
      navigate('/auth/reset-password');
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold text-center">
          {t('auth.login')}
        </CardTitle>
        <CardDescription className="text-center">
          {t('auth.loginSubtitle')}
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {/* Social Auth Buttons */}
        <div className="space-y-4 mb-6">
          <SocialAuthButtons 
            redirectTo={finalRedirectTo}
            mode="signin"
            disabled={signIn.isPending}
          />
          
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <Separator className="w-full" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-white px-2 text-gray-500">
                {t('auth.orContinueWith')}
              </span>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* General Error */}
          {errors.general && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{errors.general}</AlertDescription>
            </Alert>
          )}

          {/* Email Field */}
          <div className="space-y-2">
            <Label htmlFor="email">{t('form.email')}</Label>
            <div className="relative">
              <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="email"
                type="email"
                placeholder={t('form.emailPlaceholder')}
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={`pl-10 ${errors.email ? 'border-red-500' : ''}`}
                required
                autoComplete="email"
              />
            </div>
            {errors.email && (
              <p className="text-sm text-red-500">{errors.email}</p>
            )}
          </div>

          {/* Password Field */}
          <div className="space-y-2">
            <Label htmlFor="password">{t('form.password')}</Label>
            <div className="relative">
              <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                placeholder={t('form.passwordPlaceholder')}
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                className={`pl-10 pr-10 ${errors.password ? 'border-red-500' : ''}`}
                required
                autoComplete="current-password"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
            {errors.password && (
              <p className="text-sm text-red-500">{errors.password}</p>
            )}
          </div>

          {/* Forgot Password Link */}
          <div className="text-right">
            <button
              type="button"
              onClick={handleForgotPassword}
              className="text-sm text-primary hover:underline"
            >
              {t('auth.forgotPassword')}
            </button>
          </div>

          {/* Login Button */}
          <Button
            type="submit"
            className="w-full"
            disabled={signIn.isPending}
          >
            {signIn.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t('auth.signingIn')}
              </>
            ) : (
              t('auth.login')
            )}
          </Button>

          {/* Divider */}
          {showSignupLink && (
            <>
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <Separator className="w-full" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-white px-2 text-gray-500">
                    {t('auth.or')}
                  </span>
                </div>
              </div>

              {/* Sign Up Link */}
              <div className="text-center">
                <span className="text-sm text-gray-600">
                  {t('auth.noAccount')}{' '}
                </span>
                <Link
                  to={`/auth/signup${finalRedirectTo !== '/dashboard' ? `?redirect=${encodeURIComponent(finalRedirectTo)}` : ''}`}
                  className="text-sm text-primary hover:underline font-medium"
                >
                  {t('auth.signup')}
                </Link>
              </div>
            </>
          )}
        </form>

        {/* Terms & Privacy */}
        <div className="mt-6 text-center text-xs text-gray-500">
          {t('auth.termsDisclaimer')}{' '}
          <Link to="/terms" className="text-primary hover:underline">
            {t('footer.terms')}
          </Link>{' '}
          {t('common.and')}{' '}
          <Link to="/privacy" className="text-primary hover:underline">
            {t('footer.privacy')}
          </Link>
        </div>
      </CardContent>
    </Card>
  );
};

export default LoginForm;
