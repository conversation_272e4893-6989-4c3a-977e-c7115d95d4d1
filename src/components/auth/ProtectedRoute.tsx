/**
 * Lullaby Clinic - Protected Route Component
 * Route protection with authentication and role-based access
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Loader2, <PERSON>Alert } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth, useUserProfile } from '@/hooks/useSupabase';
import { useLanguage } from '@/contexts/LanguageContext';
import type { UserRole } from '@/types';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requiredRole?: UserRole | UserRole[];
  redirectTo?: string;
  fallback?: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAuth = true,
  requiredRole,
  redirectTo = '/auth/login',
  fallback
}) => {
  const { t } = useLanguage();
  const { user, loading: authLoading } = useAuth();
  const location = useLocation();
  
  // Get user profile to check role
  const { data: userProfile, isLoading: profileLoading, error: profileError } = useUserProfile(
    user?.id
  );

  // Show loading state while checking authentication
  if (authLoading || (user && profileLoading)) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <Loader2 className="w-8 h-8 animate-spin mx-auto text-primary" />
          <p className="text-gray-600">{t('auth.verifyingAccess')}</p>
        </div>
      </div>
    );
  }

  // If authentication is required and user is not authenticated
  if (requireAuth && !user) {
    // Store the current location for redirect after login
    const redirectUrl = `${redirectTo}?redirect=${encodeURIComponent(location.pathname + location.search)}`;
    return <Navigate to={redirectUrl} replace />;
  }

  // If user is authenticated but profile couldn't be loaded
  if (user && profileError) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <ShieldAlert className="w-5 h-5 text-red-500" />
              <span>{t('auth.profileError')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert variant="destructive">
              <AlertDescription>
                {t('auth.profileLoadError')}
              </AlertDescription>
            </Alert>
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                onClick={() => window.location.reload()}
                className="flex-1"
              >
                {t('common.retry')}
              </Button>
              <Button 
                variant="outline" 
                onClick={() => window.location.href = '/auth/logout'}
                className="flex-1"
              >
                {t('auth.logout')}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // If role-based access is required
  if (requiredRole && userProfile?.data) {
    const userRole = userProfile.data.role;
    const allowedRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
    
    if (!allowedRoles.includes(userRole)) {
      return (
        <div className="min-h-screen flex items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <ShieldAlert className="w-5 h-5 text-amber-500" />
                <span>{t('auth.accessDenied')}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertTitle>{t('auth.insufficientPermissions')}</AlertTitle>
                <AlertDescription>
                  {t('auth.requiredRole')}: {allowedRoles.join(', ')}
                  <br />
                  {t('auth.currentRole')}: {userRole}
                </AlertDescription>
              </Alert>
              <div className="flex space-x-2">
                <Button 
                  variant="outline" 
                  onClick={() => window.history.back()}
                  className="flex-1"
                >
                  {t('common.goBack')}
                </Button>
                <Button 
                  onClick={() => window.location.href = '/'}
                  className="flex-1"
                >
                  {t('nav.home')}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }
  }

  // All checks passed, render the protected content
  return <>{children}</>;
};

export default ProtectedRoute;