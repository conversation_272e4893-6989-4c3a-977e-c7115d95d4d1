/**
 * Lullaby Clinic - Signup Form Component
 * Patient registration with profile creation
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import React, { useState } from 'react';
import { Eye, EyeOff, Mail, Lock, User, Phone, Calendar, AlertCircle, Loader2, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/hooks/useSupabase';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import type { UserProfile } from '@/types';
import SocialAuthButtons from './SocialAuthButtons';

interface SignupFormProps {
  onSuccess?: () => void;
  redirectTo?: string;
  showLoginLink?: boolean;
}

interface SignupFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  gender: string;
  password: string;
  confirmPassword: string;
  marketingConsent: boolean;
  privacyConsent: boolean;
}

const SignupForm: React.FC<SignupFormProps> = ({
  onSuccess,
  redirectTo = '/dashboard',
  showLoginLink = true
}) => {
  const { t, currentLanguage } = useLanguage();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { signUp } = useAuth();

  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState<SignupFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    gender: '',
    password: '',
    confirmPassword: '',
    marketingConsent: false,
    privacyConsent: false
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Get redirect URL from search params or use default
  const finalRedirectTo = searchParams.get('redirect') || redirectTo;

  const validateStep1 = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = t('validation.firstNameRequired');
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = t('validation.lastNameRequired');
    }

    if (!formData.email) {
      newErrors.email = t('validation.emailRequired');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = t('validation.emailInvalid');
    }

    if (!formData.phone) {
      newErrors.phone = t('validation.phoneRequired');
    } else if (!/^[0-9+\-\s()]+$/.test(formData.phone)) {
      newErrors.phone = t('validation.phoneInvalid');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateStep2 = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.password) {
      newErrors.password = t('validation.passwordRequired');
    } else if (formData.password.length < 8) {
      newErrors.password = t('validation.passwordTooShort');
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password = t('validation.passwordWeak');
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = t('validation.confirmPasswordRequired');
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = t('validation.passwordMismatch');
    }

    if (!formData.privacyConsent) {
      newErrors.privacyConsent = t('validation.privacyConsentRequired');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof SignupFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleNextStep = () => {
    if (step === 1 && validateStep1()) {
      setStep(2);
    }
  };

  const handlePreviousStep = () => {
    if (step === 2) {
      setStep(1);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateStep2()) {
      return;
    }

    try {
      const userData: Partial<UserProfile> = {
        first_name: formData.firstName.trim(),
        last_name: formData.lastName.trim(),
        email: formData.email.toLowerCase().trim(),
        phone: formData.phone.trim(),
        date_of_birth: formData.dateOfBirth || undefined,
        gender: formData.gender || undefined,
        preferred_language: currentLanguage,
        marketing_consent: formData.marketingConsent,
        privacy_consent: formData.privacyConsent,
        role: 'patient'
      };

      const result = await signUp.mutateAsync({
        email: formData.email.toLowerCase().trim(),
        password: formData.password,
        userData
      });

      if (result.error) {
        // Handle specific auth errors
        if (result.error.message.includes('User already registered')) {
          setErrors({ general: t('auth.emailAlreadyExists') });
        } else if (result.error.message.includes('Password should be at least')) {
          setErrors({ password: t('validation.passwordTooShort') });
        } else if (result.error.message.includes('email not confirmed')) {
          // Email confirmation required - this is normal
          toast({
            title: t('auth.signupSuccess'),
            description: t('auth.checkEmailConfirmation')
          });
          if (onSuccess) {
            onSuccess();
          } else {
            navigate('/auth/verify-email?email=' + encodeURIComponent(formData.email));
          }
          return;
        } else {
          console.error('Signup error:', result.error);
          setErrors({ general: result.error.message });
        }
        return;
      }

      // Success
      toast({
        title: t('auth.signupSuccess'),
        description: t('auth.checkEmailConfirmation')
      });

      if (onSuccess) {
        onSuccess();
      } else {
        navigate('/auth/verify-email?email=' + encodeURIComponent(formData.email));
      }
    } catch (error) {
      setErrors({ 
        general: error instanceof Error ? error.message : t('auth.signupError') 
      });
    }
  };

  const renderStep1 = () => (
    <div className="space-y-4">
      <div className="text-center mb-4">
        <h3 className="text-lg font-semibold">{t('auth.personalInfo')}</h3>
        <p className="text-sm text-gray-600">{t('auth.step1Description')}</p>
      </div>

      {/* Name Fields */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="firstName">{t('form.firstName')} *</Label>
          <div className="relative">
            <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              id="firstName"
              type="text"
              placeholder={t('form.firstNamePlaceholder')}
              value={formData.firstName}
              onChange={(e) => handleInputChange('firstName', e.target.value)}
              className={`pl-10 ${errors.firstName ? 'border-red-500' : ''}`}
              required
            />
          </div>
          {errors.firstName && (
            <p className="text-sm text-red-500">{errors.firstName}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="lastName">{t('form.lastName')} *</Label>
          <div className="relative">
            <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              id="lastName"
              type="text"
              placeholder={t('form.lastNamePlaceholder')}
              value={formData.lastName}
              onChange={(e) => handleInputChange('lastName', e.target.value)}
              className={`pl-10 ${errors.lastName ? 'border-red-500' : ''}`}
              required
            />
          </div>
          {errors.lastName && (
            <p className="text-sm text-red-500">{errors.lastName}</p>
          )}
        </div>
      </div>

      {/* Email Field */}
      <div className="space-y-2">
        <Label htmlFor="email">{t('form.email')} *</Label>
        <div className="relative">
          <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
          <Input
            id="email"
            type="email"
            placeholder={t('form.emailPlaceholder')}
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            className={`pl-10 ${errors.email ? 'border-red-500' : ''}`}
            required
            autoComplete="email"
          />
        </div>
        {errors.email && (
          <p className="text-sm text-red-500">{errors.email}</p>
        )}
      </div>

      {/* Phone Field */}
      <div className="space-y-2">
        <Label htmlFor="phone">{t('form.phone')} *</Label>
        <div className="relative">
          <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
          <Input
            id="phone"
            type="tel"
            placeholder={t('form.phonePlaceholder')}
            value={formData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            className={`pl-10 ${errors.phone ? 'border-red-500' : ''}`}
            required
          />
        </div>
        {errors.phone && (
          <p className="text-sm text-red-500">{errors.phone}</p>
        )}
      </div>

      {/* Optional Fields */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="dateOfBirth">{t('form.dateOfBirth')}</Label>
          <div className="relative">
            <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              id="dateOfBirth"
              type="date"
              value={formData.dateOfBirth}
              onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
              className="pl-10"
              max={new Date().toISOString().split('T')[0]}
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="gender">{t('form.gender')}</Label>
          <Select
            value={formData.gender}
            onValueChange={(value) => handleInputChange('gender', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder={t('form.selectGender')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="male">{t('form.male')}</SelectItem>
              <SelectItem value="female">{t('form.female')}</SelectItem>
              <SelectItem value="other">{t('form.other')}</SelectItem>
              <SelectItem value="prefer_not_to_say">{t('form.preferNotToSay')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-4">
      <div className="text-center mb-4">
        <h3 className="text-lg font-semibold">{t('auth.securitySettings')}</h3>
        <p className="text-sm text-gray-600">{t('auth.step2Description')}</p>
      </div>

      {/* Password Fields */}
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="password">{t('form.password')} *</Label>
          <div className="relative">
            <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              id="password"
              type={showPassword ? 'text' : 'password'}
              placeholder={t('form.passwordPlaceholder')}
              value={formData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              className={`pl-10 pr-10 ${errors.password ? 'border-red-500' : ''}`}
              required
              autoComplete="new-password"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </button>
          </div>
          {errors.password && (
            <p className="text-sm text-red-500">{errors.password}</p>
          )}
          <div className="text-xs text-gray-500">
            {t('auth.passwordRequirements')}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="confirmPassword">{t('form.confirmPassword')} *</Label>
          <div className="relative">
            <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              id="confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              placeholder={t('form.confirmPasswordPlaceholder')}
              value={formData.confirmPassword}
              onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
              className={`pl-10 pr-10 ${errors.confirmPassword ? 'border-red-500' : ''}`}
              required
              autoComplete="new-password"
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
            >
              {showConfirmPassword ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </button>
          </div>
          {errors.confirmPassword && (
            <p className="text-sm text-red-500">{errors.confirmPassword}</p>
          )}
        </div>
      </div>

      {/* Consent Checkboxes */}
      <div className="space-y-4">
        <div className="flex items-start space-x-2">
          <Checkbox
            id="privacyConsent"
            checked={formData.privacyConsent}
            onCheckedChange={(checked) => handleInputChange('privacyConsent', !!checked)}
            className={errors.privacyConsent ? 'border-red-500' : ''}
          />
          <div className="grid gap-1.5 leading-none">
            <Label
              htmlFor="privacyConsent"
              className="text-sm font-normal cursor-pointer"
            >
              {t('auth.privacyConsent')}{' '}
              <Link to="/privacy" className="text-primary hover:underline">
                {t('footer.privacy')}
              </Link>{' '}
              {t('common.and')}{' '}
              <Link to="/terms" className="text-primary hover:underline">
                {t('footer.terms')}
              </Link>
              . *
            </Label>
          </div>
        </div>
        {errors.privacyConsent && (
          <p className="text-sm text-red-500 ml-6">{errors.privacyConsent}</p>
        )}

        <div className="flex items-start space-x-2">
          <Checkbox
            id="marketingConsent"
            checked={formData.marketingConsent}
            onCheckedChange={(checked) => handleInputChange('marketingConsent', !!checked)}
          />
          <div className="grid gap-1.5 leading-none">
            <Label
              htmlFor="marketingConsent"
              className="text-sm font-normal cursor-pointer"
            >
              {t('auth.marketingConsent')}
            </Label>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold text-center">
          {t('auth.signup')}
        </CardTitle>
        <CardDescription className="text-center">
          {t('auth.signupSubtitle')}
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {/* Social Auth Buttons - Only show on step 1 */}
        {step === 1 && (
          <div className="space-y-4 mb-6">
            <SocialAuthButtons 
              redirectTo={finalRedirectTo}
              mode="signup"
              disabled={signUp.isPending}
            />
            
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <Separator className="w-full" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-white px-2 text-gray-500">
                  {t('auth.orSignUpWith')}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Progress Indicator */}
        <div className="flex items-center justify-center mb-6">
          <div className="flex items-center space-x-2">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              step >= 1 ? 'bg-primary text-white' : 'bg-gray-200 text-gray-600'
            }`}>
              {step > 1 ? <CheckCircle className="w-4 h-4" /> : '1'}
            </div>
            <div className={`w-12 h-1 ${step >= 2 ? 'bg-primary' : 'bg-gray-200'}`} />
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              step >= 2 ? 'bg-primary text-white' : 'bg-gray-200 text-gray-600'
            }`}>
              2
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* General Error */}
          {errors.general && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{errors.general}</AlertDescription>
            </Alert>
          )}

          {/* Step Content */}
          {step === 1 ? renderStep1() : renderStep2()}

          {/* Navigation Buttons */}
          <div className="flex justify-between pt-4">
            {step === 1 ? (
              <div></div> // Empty div for spacing
            ) : (
              <Button
                type="button"
                variant="outline"
                onClick={handlePreviousStep}
              >
                {t('common.previous')}
              </Button>
            )}

            {step === 1 ? (
              <Button
                type="button"
                onClick={handleNextStep}
                className="ml-auto"
              >
                {t('common.next')}
              </Button>
            ) : (
              <Button
                type="submit"
                disabled={signUp.isPending}
              >
                {signUp.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t('auth.creatingAccount')}
                  </>
                ) : (
                  t('auth.createAccount')
                )}
              </Button>
            )}
          </div>

          {/* Login Link */}
          {showLoginLink && (
            <>
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <Separator className="w-full" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-white px-2 text-gray-500">
                    {t('auth.or')}
                  </span>
                </div>
              </div>

              <div className="text-center">
                <span className="text-sm text-gray-600">
                  {t('auth.alreadyHaveAccount')}{' '}
                </span>
                <Link
                  to={`/auth/login${finalRedirectTo !== '/dashboard' ? `?redirect=${encodeURIComponent(finalRedirectTo)}` : ''}`}
                  className="text-sm text-primary hover:underline font-medium"
                >
                  {t('auth.login')}
                </Link>
              </div>
            </>
          )}
        </form>
      </CardContent>
    </Card>
  );
};

export default SignupForm;