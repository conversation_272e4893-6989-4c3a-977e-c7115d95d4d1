/**
 * Lullaby Clinic - Reset Password Form Component
 * Secure password reset functionality
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import React, { useState } from 'react';
import { Mail, Lock, AlertCircle, Loader2, CheckCircle, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/hooks/useSupabase';
import { Link, useSearchParams } from 'react-router-dom';

interface ResetPasswordFormProps {
  onSuccess?: () => void;
}

const ResetPasswordForm: React.FC<ResetPasswordFormProps> = ({ onSuccess }) => {
  const { t } = useLanguage();
  const { toast } = useToast();
  const [searchParams] = useSearchParams();
  const { resetPassword } = useAuth();

  const [email, setEmail] = useState(searchParams.get('email') || '');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!email) {
      newErrors.email = t('validation.emailRequired');
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = t('validation.emailInvalid');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const result = await resetPassword.mutateAsync(email);

      if (result.error) {
        setErrors({ general: result.error.message });
        return;
      }

      // Success
      setIsSubmitted(true);
      toast({
        title: t('auth.resetEmailSent'),
        description: t('auth.checkEmailForInstructions')
      });

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      setErrors({ 
        general: error instanceof Error ? error.message : t('auth.resetPasswordError') 
      });
    }
  };

  const handleInputChange = (value: string) => {
    setEmail(value);
    // Clear error when user starts typing
    if (errors.email) {
      setErrors(prev => ({ ...prev, email: '' }));
    }
  };

  if (isSubmitted) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-center">
            {t('auth.emailSent')}
          </CardTitle>
          <CardDescription className="text-center">
            {t('auth.resetInstructionsSent')}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="text-center space-y-2">
            <p className="text-sm text-gray-600">
              {t('auth.checkEmail')} <strong>{email}</strong>
            </p>
            <p className="text-sm text-gray-600">
              {t('auth.resetLinkExpiry')}
            </p>
          </div>

          <div className="space-y-3">
            <Button
              onClick={() => setIsSubmitted(false)}
              variant="outline"
              className="w-full"
            >
              {t('auth.sendAnotherEmail')}
            </Button>
            
            <div className="text-center">
              <Link
                to="/auth/login"
                className="text-sm text-primary hover:underline inline-flex items-center"
              >
                <ArrowLeft className="w-4 h-4 mr-1" />
                {t('auth.backToLogin')}
              </Link>
            </div>
          </div>

          {/* Spam Folder Notice */}
          <Alert>
            <Mail className="h-4 w-4" />
            <AlertDescription>
              {t('auth.checkSpamFolder')}
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold text-center">
          {t('auth.resetPassword')}
        </CardTitle>
        <CardDescription className="text-center">
          {t('auth.resetPasswordSubtitle')}
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* General Error */}
          {errors.general && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{errors.general}</AlertDescription>
            </Alert>
          )}

          {/* Instructions */}
          <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-lg">
            {t('auth.resetPasswordInstructions')}
          </div>

          {/* Email Field */}
          <div className="space-y-2">
            <Label htmlFor="email">{t('form.email')}</Label>
            <div className="relative">
              <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="email"
                type="email"
                placeholder={t('form.emailPlaceholder')}
                value={email}
                onChange={(e) => handleInputChange(e.target.value)}
                className={`pl-10 ${errors.email ? 'border-red-500' : ''}`}
                required
                autoComplete="email"
                autoFocus
              />
            </div>
            {errors.email && (
              <p className="text-sm text-red-500">{errors.email}</p>
            )}
          </div>

          {/* Submit Button */}
          <Button
            type="submit"
            className="w-full"
            disabled={resetPassword.isPending}
          >
            {resetPassword.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t('auth.sendingEmail')}
              </>
            ) : (
              t('auth.sendResetEmail')
            )}
          </Button>

          {/* Back to Login Link */}
          <div className="text-center">
            <Link
              to="/auth/login"
              className="text-sm text-primary hover:underline inline-flex items-center"
            >
              <ArrowLeft className="w-4 h-4 mr-1" />
              {t('auth.backToLogin')}
            </Link>
          </div>
        </form>

        {/* Help Section */}
        <div className="mt-6 text-center text-xs text-gray-500 space-y-2">
          <p>{t('auth.troubleSigningIn')}</p>
          <div className="space-x-4">
            <Link to="/contact" className="text-primary hover:underline">
              {t('nav.contact')}
            </Link>
            <span>•</span>
            <Link to="/help" className="text-primary hover:underline">
              {t('nav.help')}
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ResetPasswordForm;