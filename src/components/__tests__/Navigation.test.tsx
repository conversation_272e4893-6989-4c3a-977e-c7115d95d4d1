import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { vi } from 'vitest';
import Navigation from '../Navigation';
import { useAuth } from '@/contexts/AuthProvider';
import { useLanguage } from '@/contexts/LanguageContext';

vi.mock('@/contexts/AuthProvider');
vi.mock('@/contexts/LanguageContext');

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>;
const mockUseLanguage = useLanguage as jest.MockedFunction<typeof useLanguage>;

const mockTranslations = {
  'nav.home': 'Home',
  'nav.services': 'Services',
  'nav.doctors': 'Doctors',
  'nav.about': 'About',
  'nav.gallery': 'Gallery',
  'nav.beforeAfter': 'Before & After',
  'nav.pricing': 'Pricing',
  'nav.reviews': 'Reviews',
  'nav.appointments': 'Appointments',
  'nav.contact': 'Contact',
  'nav.blog': 'Blog',
  'nav.dashboard': 'Dashboard',
  'nav.signIn': 'Sign In',
  'nav.signUp': 'Sign Up',
  'nav.signOut': 'Sign Out',
};

const renderNavigation = (user = null) => {
  mockUseAuth.mockReturnValue({
    user,
    signOut: vi.fn(),
    signIn: vi.fn(),
    signUp: vi.fn(),
    loading: false,
    error: null,
  });

  mockUseLanguage.mockReturnValue({
    t: (key: string) => mockTranslations[key] || key,
    currentLanguage: 'en',
    setLanguage: vi.fn(),
  });

  return render(
    <BrowserRouter>
      <Navigation />
    </BrowserRouter>
  );
};

describe('Navigation Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders all navigation links', () => {
    renderNavigation();

    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('Services')).toBeInTheDocument();
    expect(screen.getByText('Doctors')).toBeInTheDocument();
    expect(screen.getByText('About')).toBeInTheDocument();
    expect(screen.getByText('Gallery')).toBeInTheDocument();
    expect(screen.getByText('Before & After')).toBeInTheDocument();
    expect(screen.getByText('Pricing')).toBeInTheDocument();
    expect(screen.getByText('Reviews')).toBeInTheDocument();
    expect(screen.getByText('Appointments')).toBeInTheDocument();
    expect(screen.getByText('Contact')).toBeInTheDocument();
    expect(screen.getByText('Blog')).toBeInTheDocument();
  });

  it('shows sign in/up buttons when user is not authenticated', () => {
    renderNavigation();

    expect(screen.getByText('Sign In')).toBeInTheDocument();
    expect(screen.getByText('Sign Up')).toBeInTheDocument();
  });

  it('shows user menu when user is authenticated', () => {
    const mockUser = { id: '1', email: '<EMAIL>' };
    renderNavigation(mockUser);

    const userButton = screen.getByRole('button');
    fireEvent.click(userButton);

    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Sign Out')).toBeInTheDocument();
  });

  it('opens mobile menu when hamburger is clicked', () => {
    renderNavigation();

    const hamburgerButton = screen.getByRole('button', { name: /menu/i });
    fireEvent.click(hamburgerButton);

    expect(screen.getAllByText('Home')).toHaveLength(2);
  });

  it('has correct href attributes for navigation links', () => {
    renderNavigation();

    expect(screen.getByText('Home').closest('a')).toHaveAttribute('href', '/');
    expect(screen.getByText('Services').closest('a')).toHaveAttribute('href', '/services');
    expect(screen.getByText('Doctors').closest('a')).toHaveAttribute('href', '/doctors');
    expect(screen.getByText('About').closest('a')).toHaveAttribute('href', '/about');
    expect(screen.getByText('Gallery').closest('a')).toHaveAttribute('href', '/gallery');
    expect(screen.getByText('Before & After').closest('a')).toHaveAttribute('href', '/before-after-gallery');
    expect(screen.getByText('Pricing').closest('a')).toHaveAttribute('href', '/pricing');
    expect(screen.getByText('Reviews').closest('a')).toHaveAttribute('href', '/reviews');
    expect(screen.getByText('Appointments').closest('a')).toHaveAttribute('href', '/appointments');
    expect(screen.getByText('Contact').closest('a')).toHaveAttribute('href', '/contact');
    expect(screen.getByText('Blog').closest('a')).toHaveAttribute('href', '/blog');
  });

  it('is responsive and shows mobile menu on small screens', () => {
    renderNavigation();

    const mobileMenuButton = screen.getByRole('button');
    expect(mobileMenuButton).toBeInTheDocument();
  });
});
