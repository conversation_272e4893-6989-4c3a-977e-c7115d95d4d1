import React, { useState, useEffect, useMemo } from 'react';
import { ChevronLeft, ChevronRight, Clock, Percent, Gift, Zap, Star, Calendar, Users, AlertTriangle, Sparkles } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, <PERSON>Content, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import LazyImage from '@/components/LazyImage';
import { cn } from '@/lib/utils';

interface Promotion {
  id: string;
  title: string;
  description: string;
  discount: number;
  originalPrice: number;
  discountedPrice: number;
  currency: string;
  image: string;
  category: string;
  validUntil: string;
  totalSlots: number;
  bookedSlots: number;
  featured: boolean;
  trending: boolean;
  newOffer: boolean;
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical';
  benefits: string[];
  terms: string[];
}

interface MonthlyPromotionsProps {
  translations: {
    promotions: {
      title: string;
      subtitle: string;
      monthlySpecial: string;
      limitedTime: string;
      bookNow: string;
      learnMore: string;
      save: string;
      originalPrice: string;
      newPrice: string;
      validUntil: string;
      spotsLeft: string;
      almostGone: string;
      lastChance: string;
      popular: string;
      trending: string;
      newOffer: string;
      featured: string;
      timeLeft: string;
      days: string;
      hours: string;
      minutes: string;
      seconds: string;
      benefits: string;
      terms: string;
      viewAll: string;
      bookingProgress: string;
      peopleBooked: string;
      thisMonth: string;
      exclusiveOffer: string;
      memberOnly: string;
    };
  };
  onPromotionClick?: (promotion: Promotion) => void;
  onBookNow?: (promotion: Promotion) => void;
}

const MonthlyPromotions: React.FC<MonthlyPromotionsProps> = ({
  translations,
  onPromotionClick,
  onBookNow
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [timeLeft, setTimeLeft] = useState<Record<string, { days: number; hours: number; minutes: number; seconds: number }>>({});
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Mock promotions data
  const promotions: Promotion[] = useMemo(() => [
    {
      id: '1',
      title: 'Ultimate Glow Package',
      description: 'Complete facial rejuvenation with Botox + Dermal Fillers + LED therapy',
      discount: 40,
      originalPrice: 25000,
      discountedPrice: 15000,
      currency: 'THB',
      image: '/placeholder.svg',
      category: 'Facial Treatments',
      validUntil: '2024-12-31T23:59:59',
      totalSlots: 20,
      bookedSlots: 17,
      featured: true,
      trending: true,
      newOffer: false,
      urgencyLevel: 'critical',
      benefits: [
        'Instant wrinkle reduction',
        'Volume restoration',
        'Skin brightening',
        'Free consultation included',
        '3-month warranty'
      ],
      terms: [
        'Valid for new customers only',
        'Must be used within 3 months',
        'Cannot be combined with other offers'
      ]
    },
    {
      id: '2',
      title: 'Holiday Body Contouring',
      description: 'Non-invasive body sculpting with advanced laser technology',
      discount: 30,
      originalPrice: 18000,
      discountedPrice: 12600,
      currency: 'THB',
      image: '/placeholder.svg',
      category: 'Body Treatments',
      validUntil: '2024-12-25T23:59:59',
      totalSlots: 15,
      bookedSlots: 8,
      featured: false,
      trending: true,
      newOffer: true,
      urgencyLevel: 'medium',
      benefits: [
        'Visible results in 4-6 weeks',
        'No downtime required',
        'FDA-approved technology',
        'Personalized treatment plan'
      ],
      terms: [
        'Package of 4 sessions',
        'Valid for 6 months',
        'Free consultation included'
      ]
    },
    {
      id: '3',
      title: 'New Year Skin Reset',
      description: 'Chemical peel + Microneedling + Vitamin C infusion package',
      discount: 35,
      originalPrice: 12000,
      discountedPrice: 7800,
      currency: 'THB',
      image: '/placeholder.svg',
      category: 'Skin Treatments',
      validUntil: '2025-01-15T23:59:59',
      totalSlots: 25,
      bookedSlots: 12,
      featured: true,
      trending: false,
      newOffer: true,
      urgencyLevel: 'medium',
      benefits: [
        'Improved skin texture',
        'Reduced acne scars',
        'Brighter complexion',
        'Collagen stimulation'
      ],
      terms: [
        'Three-session package',
        'Sessions spaced 4 weeks apart',
        'Includes aftercare products'
      ]
    },
    {
      id: '4',
      title: 'VIP Couples Package',
      description: 'Luxury spa day for two with premium treatments',
      discount: 25,
      originalPrice: 30000,
      discountedPrice: 22500,
      currency: 'THB',
      image: '/placeholder.svg',
      category: 'Couples Treatments',
      validUntil: '2024-12-14T23:59:59',
      totalSlots: 10,
      bookedSlots: 9,
      featured: false,
      trending: false,
      newOffer: false,
      urgencyLevel: 'critical',
      benefits: [
        'Private VIP room',
        'Champagne service',
        'Customized treatments',
        'Professional photography'
      ],
      terms: [
        'Advance booking required',
        'Valid weekdays only',
        'Subject to availability'
      ]
    }
  ], []);

  // Calculate countdown timers
  useEffect(() => {
    const updateTimers = () => {
      const newTimeLeft: Record<string, { days: number; hours: number; minutes: number; seconds: number }> = {};
      
      promotions.forEach(promotion => {
        const now = new Date().getTime();
        const target = new Date(promotion.validUntil).getTime();
        const distance = target - now;

        if (distance > 0) {
          newTimeLeft[promotion.id] = {
            days: Math.floor(distance / (1000 * 60 * 60 * 24)),
            hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
            minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
            seconds: Math.floor((distance % (1000 * 60)) / 1000)
          };
        } else {
          newTimeLeft[promotion.id] = { days: 0, hours: 0, minutes: 0, seconds: 0 };
        }
      });
      
      setTimeLeft(newTimeLeft);
    };

    updateTimers();
    const interval = setInterval(updateTimers, 1000);
    return () => clearInterval(interval);
  }, [promotions]);

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentSlide(prev => (prev + 1) % promotions.length);
    }, 6000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, promotions.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % promotions.length);
    setIsAutoPlaying(false);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + promotions.length) % promotions.length);
    setIsAutoPlaying(false);
  };

  const getUrgencyColor = (level: string) => {
    switch (level) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default: return 'text-green-600 bg-green-50 border-green-200';
    }
  };

  const getBookingPercentage = (promotion: Promotion) => {
    return (promotion.bookedSlots / promotion.totalSlots) * 100;
  };

  const isAlmostFull = (promotion: Promotion) => {
    return getBookingPercentage(promotion) >= 80;
  };

  const isCritical = (promotion: Promotion) => {
    return getBookingPercentage(promotion) >= 90;
  };

  return (
    <section className="py-20 bg-gradient-to-br from-primary-50 via-white to-purple-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center bg-primary-100 text-primary-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Gift className="h-4 w-4 mr-2" />
            {translations.promotions.monthlySpecial}
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            {translations.promotions.title}
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {translations.promotions.subtitle}
          </p>
        </div>

        {/* Main Carousel */}
        <div className="relative max-w-6xl mx-auto">
          <div className="overflow-hidden rounded-2xl shadow-2xl">
            <div 
              className="flex transition-transform duration-500 ease-in-out"
              style={{ transform: `translateX(-${currentSlide * 100}%)` }}
            >
              {promotions.map((promotion, index) => {
                const timer = timeLeft[promotion.id] || { days: 0, hours: 0, minutes: 0, seconds: 0 };
                const bookingPercentage = getBookingPercentage(promotion);
                
                return (
                  <div key={promotion.id} className="w-full flex-shrink-0">
                    <Card className="border-0 shadow-none bg-white">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-0 min-h-[500px]">
                        
                        {/* Image Section */}
                        <div className="relative overflow-hidden">
                          <LazyImage
                            src={promotion.image}
                            alt={promotion.title}
                            className="w-full h-full"
                            objectFit="cover"
                            priority={index === 0}
                          />
                          
                          {/* Overlay Badges */}
                          <div className="absolute top-4 left-4 flex flex-col gap-2">
                            {promotion.featured && (
                              <Badge className="bg-yellow-500 text-white">
                                <Star className="h-3 w-3 mr-1" />
                                {translations.promotions.featured}
                              </Badge>
                            )}
                            {promotion.trending && (
                              <Badge className="bg-red-500 text-white">
                                <Zap className="h-3 w-3 mr-1" />
                                {translations.promotions.trending}
                              </Badge>
                            )}
                            {promotion.newOffer && (
                              <Badge className="bg-green-500 text-white">
                                <Sparkles className="h-3 w-3 mr-1" />
                                {translations.promotions.newOffer}
                              </Badge>
                            )}
                          </div>

                          {/* Discount Badge */}
                          <div className="absolute top-4 right-4">
                            <div className="bg-primary-600 text-white rounded-full w-16 h-16 flex items-center justify-center shadow-lg">
                              <div className="text-center">
                                <div className="text-lg font-bold">{promotion.discount}%</div>
                                <div className="text-xs">OFF</div>
                              </div>
                            </div>
                          </div>

                          {/* Countdown Timer Overlay */}
                          <div className="absolute bottom-4 left-4 right-4">
                            <div className="bg-black/80 backdrop-blur-sm rounded-lg p-4 text-white">
                              <div className="flex items-center gap-2 mb-2">
                                <Clock className="h-4 w-4 text-yellow-300" />
                                <span className="text-sm font-medium">{translations.promotions.timeLeft}</span>
                              </div>
                              <div className="grid grid-cols-4 gap-2">
                                <div className="text-center">
                                  <div className="text-xl font-bold">{timer.days.toString().padStart(2, '0')}</div>
                                  <div className="text-xs opacity-80">{translations.promotions.days}</div>
                                </div>
                                <div className="text-center">
                                  <div className="text-xl font-bold">{timer.hours.toString().padStart(2, '0')}</div>
                                  <div className="text-xs opacity-80">{translations.promotions.hours}</div>
                                </div>
                                <div className="text-center">
                                  <div className="text-xl font-bold">{timer.minutes.toString().padStart(2, '0')}</div>
                                  <div className="text-xs opacity-80">{translations.promotions.minutes}</div>
                                </div>
                                <div className="text-center">
                                  <div className="text-xl font-bold">{timer.seconds.toString().padStart(2, '0')}</div>
                                  <div className="text-xs opacity-80">{translations.promotions.seconds}</div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Content Section */}
                        <div className="p-8 lg:p-12 flex flex-col justify-between">
                          <div className="space-y-6">
                            {/* Header */}
                            <div>
                              <Badge variant="secondary" className="mb-3">
                                {promotion.category}
                              </Badge>
                              <h3 className="text-2xl lg:text-3xl font-bold text-foreground mb-3">
                                {promotion.title}
                              </h3>
                              <p className="text-muted-foreground leading-relaxed">
                                {promotion.description}
                              </p>
                            </div>

                            {/* Pricing */}
                            <div className="space-y-2">
                              <div className="flex items-center gap-3">
                                <span className="text-sm text-muted-foreground line-through">
                                  {translations.promotions.originalPrice}: {promotion.originalPrice.toLocaleString()} {promotion.currency}
                                </span>
                                <Badge className="bg-green-100 text-green-800">
                                  {translations.promotions.save} {(promotion.originalPrice - promotion.discountedPrice).toLocaleString()} {promotion.currency}
                                </Badge>
                              </div>
                              <div className="text-3xl font-bold text-primary-600">
                                {promotion.discountedPrice.toLocaleString()} {promotion.currency}
                              </div>
                            </div>

                            {/* Booking Progress */}
                            <div className="space-y-3">
                              <div className="flex justify-between items-center text-sm">
                                <span className="text-muted-foreground">{translations.promotions.bookingProgress}</span>
                                <span className={cn(
                                  "font-medium",
                                  isCritical(promotion) ? "text-red-600" : 
                                  isAlmostFull(promotion) ? "text-orange-600" : "text-green-600"
                                )}>
                                  {promotion.bookedSlots}/{promotion.totalSlots} {translations.promotions.peopleBooked}
                                </span>
                              </div>
                              
                              <Progress 
                                value={bookingPercentage} 
                                className={cn(
                                  "h-3",
                                  isCritical(promotion) && "bg-red-100"
                                )}
                              />
                              
                              {isAlmostFull(promotion) && (
                                <div className={cn(
                                  "flex items-center gap-2 p-2 rounded-lg text-sm font-medium",
                                  getUrgencyColor(promotion.urgencyLevel)
                                )}>
                                  <AlertTriangle className="h-4 w-4" />
                                  {isCritical(promotion) 
                                    ? translations.promotions.lastChance
                                    : translations.promotions.almostGone
                                  }
                                </div>
                              )}
                            </div>

                            {/* Benefits */}
                            <div>
                              <h4 className="font-semibold text-foreground mb-3 flex items-center gap-2">
                                <Star className="h-4 w-4 text-primary-600" />
                                {translations.promotions.benefits}
                              </h4>
                              <div className="grid grid-cols-1 gap-2">
                                {promotion.benefits.slice(0, 3).map((benefit, idx) => (
                                  <div key={idx} className="flex items-center gap-2 text-sm text-muted-foreground">
                                    <div className="w-1.5 h-1.5 bg-primary-600 rounded-full"></div>
                                    {benefit}
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>

                          {/* Action Buttons */}
                          <div className="space-y-3 pt-6">
                            <Button
                              size="lg"
                              className={cn(
                                "w-full text-lg font-semibold h-12",
                                isCritical(promotion) && "animate-pulse bg-red-600 hover:bg-red-700"
                              )}
                              onClick={() => onBookNow?.(promotion)}
                            >
                              <Calendar className="h-5 w-5 mr-2" />
                              {translations.promotions.bookNow}
                            </Button>
                            
                            <Button
                              variant="outline"
                              size="lg"
                              className="w-full"
                              onClick={() => onPromotionClick?.(promotion)}
                            >
                              {translations.promotions.learnMore}
                            </Button>
                          </div>
                        </div>
                      </div>
                    </Card>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Navigation Arrows */}
          <Button
            variant="outline"
            size="icon"
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm shadow-lg"
            onClick={prevSlide}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          <Button
            variant="outline"
            size="icon"
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm shadow-lg"
            onClick={nextSlide}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>

          {/* Slide Indicators */}
          <div className="flex justify-center mt-6 gap-2">
            {promotions.map((_, index) => (
              <button
                key={index}
                className={cn(
                  "w-3 h-3 rounded-full transition-all duration-300",
                  index === currentSlide 
                    ? "bg-primary-600 w-8" 
                    : "bg-gray-300 hover:bg-gray-400"
                )}
                onClick={() => setCurrentSlide(index)}
              />
            ))}
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12 max-w-4xl mx-auto">
          <div className="text-center p-6 bg-white rounded-xl shadow-soft">
            <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <Users className="h-6 w-6 text-primary-600" />
            </div>
            <div className="text-2xl font-bold text-foreground mb-1">2,847</div>
            <div className="text-sm text-muted-foreground">{translations.promotions.peopleBooked} {translations.promotions.thisMonth}</div>
          </div>
          
          <div className="text-center p-6 bg-white rounded-xl shadow-soft">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <Percent className="h-6 w-6 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-foreground mb-1">40%</div>
            <div className="text-sm text-muted-foreground">Average savings on treatments</div>
          </div>
          
          <div className="text-center p-6 bg-white rounded-xl shadow-soft">
            <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <Star className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="text-2xl font-bold text-foreground mb-1">4.9</div>
            <div className="text-sm text-muted-foreground">Customer satisfaction rating</div>
          </div>
        </div>

        {/* View All Button */}
        <div className="text-center mt-8">
          <Button variant="outline" size="lg" className="border-primary-200 text-primary-600 hover:bg-primary-50">
            {translations.promotions.viewAll}
            <ChevronRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default MonthlyPromotions;