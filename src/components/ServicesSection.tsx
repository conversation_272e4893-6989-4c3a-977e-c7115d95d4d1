
import React from 'react';
import type { Translation } from '@/utils/translations';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Heart, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface ServicesSectionProps {
  translations: Translation;
}

const ServicesSection = ({ translations }: ServicesSectionProps) => {
  const services = [
    {
      icon: <Sparkles className="h-8 w-8 text-primary-500" />,
      title: translations.services.facial.title,
      description: translations.services.facial.description,
      image: "/placeholder.svg",
      price: translations.services.facial.price,
      popular: true
    },
    {
      icon: <Heart className="h-8 w-8 text-primary-500" />,
      title: translations.services.botox.title,
      description: translations.services.botox.description,
      image: "/placeholder.svg",
      price: translations.services.botox.price,
      popular: false
    },
    {
      icon: <Star className="h-8 w-8 text-primary-500" />,
      title: translations.services.laser.title,
      description: translations.services.laser.description,
      image: "/placeholder.svg",
      price: translations.services.laser.price,
      popular: false
    }
  ];

  return (
    <section id="services" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-primary-100 text-primary-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Sparkles className="h-4 w-4 mr-2" />
            {translations.services.badge}
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            {translations.services.title}
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {translations.services.subtitle}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Card 
              key={index} 
              className="group hover:shadow-soft transition-all duration-300 hover:-translate-y-2 relative overflow-hidden"
            >
              {service.popular && (
                <div className="absolute top-4 right-4 bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                  {translations.services.popular}
                </div>
              )}
              
              <div className="aspect-video relative overflow-hidden">
                <img 
                  src={service.image} 
                  alt={service.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                <div className="absolute bottom-4 left-4">
                  {service.icon}
                </div>
              </div>

              <CardHeader>
                <CardTitle className="text-xl text-foreground group-hover:text-primary-600 transition-colors">
                  {service.title}
                </CardTitle>
                <CardDescription className="text-muted-foreground">
                  {service.description}
                </CardDescription>
              </CardHeader>

              <CardContent>
                <div className="flex items-center justify-between mb-4">
                  <div className="text-2xl font-bold text-primary-600">
                    {service.price}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {translations.services.starting}
                  </div>
                </div>
                
                <Button className="w-full bg-primary-500 hover:bg-primary-600 text-white group">
                  {translations.services.learnMore}
                  <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button variant="outline" size="lg" className="border-primary-200 text-primary-600 hover:bg-primary-50">
            {translations.services.viewAll}
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
