
import React, { useState, useRef, useCallback } from 'react';
import { Images, ArrowLeft, ArrowRight } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import LazyImage from '@/components/LazyImage';
import { cn } from '@/lib/utils';

interface BeforeAfterComparisonProps {
  beforeImage: string;
  afterImage: string;
  title: string;
  description?: string;
  className?: string;
}

const BeforeAfterComparison = ({
  beforeImage,
  afterImage,
  title,
  description,
  className
}: BeforeAfterComparisonProps) => {
  const [sliderPosition, setSliderPosition] = useState(50);
  const [isDragging, setIsDragging] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    setSliderPosition(percentage);
  }, [isDragging]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!isDragging || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const touch = e.touches[0];
    const x = touch.clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    setSliderPosition(percentage);
  }, [isDragging]);

  const handleTouchEnd = useCallback(() => {
    setIsDragging(false);
  }, []);

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.addEventListener('touchmove', handleTouchMove);
      document.addEventListener('touchend', handleTouchEnd);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  }, [isDragging, handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd]);

  return (
    <Card className={cn('overflow-hidden', className)}>
      <div className="p-4">
        <h3 className="text-lg font-semibold text-foreground mb-2 flex items-center">
          <Images className="h-5 w-5 mr-2 text-primary-500" />
          {title}
        </h3>
        {description && (
          <p className="text-sm text-muted-foreground mb-4">{description}</p>
        )}
      </div>

      <div 
        ref={containerRef}
        className="relative w-full h-80 overflow-hidden cursor-col-resize select-none"
        onMouseDown={handleMouseDown}
        onTouchStart={handleTouchStart}
      >
        {/* After Image (Background) */}
        <div className="absolute inset-0">
          <LazyImage
            src={afterImage}
            alt="After treatment"
            className="w-full h-full"
            objectFit="cover"
            loading="lazy"
          />
          <div className="absolute bottom-4 right-4 bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium">
            After
          </div>
        </div>

        {/* Before Image (Foreground with clip) */}
        <div 
          className="absolute inset-0 overflow-hidden"
          style={{ clipPath: `inset(0 ${100 - sliderPosition}% 0 0)` }}
        >
          <LazyImage
            src={beforeImage}
            alt="Before treatment"
            className="w-full h-full"
            objectFit="cover"
            loading="lazy"
          />
          <div className="absolute bottom-4 left-4 bg-gray-800 text-white px-3 py-1 rounded-full text-xs font-medium">
            Before
          </div>
        </div>

        {/* Slider Handle */}
        <div 
          className="absolute top-0 bottom-0 w-1 bg-white shadow-lg cursor-col-resize"
          style={{ left: `${sliderPosition}%`, transform: 'translateX(-50%)' }}
        >
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center">
            <ArrowLeft className="h-3 w-3 text-gray-600 -mr-1" />
            <ArrowRight className="h-3 w-3 text-gray-600 -ml-1" />
          </div>
        </div>

        {/* Drag instruction overlay */}
        {!isDragging && (
          <div className="absolute inset-0 bg-black/20 opacity-0 hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
            <div className="bg-white/90 px-4 py-2 rounded-lg text-sm font-medium text-gray-800">
              Drag to compare
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default BeforeAfterComparison;
