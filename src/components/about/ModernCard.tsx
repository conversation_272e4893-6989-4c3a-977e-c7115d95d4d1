
import React, { memo } from 'react';
import { Card, CardContent } from '@/components/ui/card';

interface ModernCardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
}

const ModernCard = memo(({ 
  children, 
  className = '', 
  hover = false 
}: ModernCardProps) => (
  <Card 
    className={`
      bg-white border border-gray-200 shadow-lg rounded-lg
      ${hover ? 'hover:shadow-xl hover:scale-105 transition-all duration-300' : ''}
      ${className}
    `}
  >
    <CardContent className="p-8">
      {children}
    </CardContent>
  </Card>
));

ModernCard.displayName = 'ModernCard';

export default ModernCard;
