
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { useLanguage } from '@/contexts/LanguageContext';
import GlassCard from './GlassCard';

const partnersData = ['Allergan', 'Galderma', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Juvederm'];

const PartnersSection = () => {
  const { t } = useLanguage();

  return (
    <section className="text-center space-y-6">
      <h3 className="text-3xl font-semibold text-foreground">{t('about.partners.title')}</h3>
      <ModernCard>
        <div className="flex flex-wrap justify-center items-center gap-8">
          {partnersData.map((partner, idx) => (
            <Badge
              key={idx}
              variant="secondary"
              className="px-6 py-3 bg-white/10 text-foreground font-medium hover:bg-white/20 transition-all duration-300 cursor-pointer border border-white/20"
            >
              {partner}
            </Badge>
          ))}
        </div>
      </ModernCard>
    </section>
  );
};

export default PartnersSection;
