
import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import ModernCard from './ModernCard';

const AboutApproach = () => {
  const { t } = useLanguage();

  return (
    <section>
      <ModernCard>
        <div className="text-center">
          <h2 className="text-3xl font-semibold text-foreground mb-6">{t('about.approach.title')}</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            {t('about.approach.description')}
          </p>
        </div>
      </ModernCard>
    </section>
  );
};

export default AboutApproach;
