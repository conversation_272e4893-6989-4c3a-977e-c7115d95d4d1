
import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import ModernCard from './ModernCard';

const MissionVision = () => {
  const { t } = useLanguage();

  return (
    <section className="grid grid-cols-1 md:grid-cols-2 gap-8">
      <ModernCard hover className="group">
        <div className="text-4xl mb-4">🎯</div>
        <h2 className="text-2xl font-bold text-foreground mb-4">{t('about.mission.title')}</h2>
        <p className="text-muted-foreground leading-relaxed">{t('about.mission.description')}</p>
      </ModernCard>

      <ModernCard hover className="group">
        <div className="text-4xl mb-4">🌟</div>
        <h2 className="text-2xl font-bold text-foreground mb-4">{t('about.vision.title')}</h2>
        <p className="text-muted-foreground leading-relaxed">{t('about.vision.description')}</p>
      </ModernCard>
    </section>
  );
};

export default MissionVision;
