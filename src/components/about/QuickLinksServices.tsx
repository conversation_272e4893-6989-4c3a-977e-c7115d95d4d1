
import React from 'react';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/contexts/LanguageContext';
import GlassCard from './GlassCard';

const quickLinksData = [
  { key: 'nav.home', href: '/' },
  { key: 'nav.services', href: '/services' },
  { key: 'nav.gallery', href: '/gallery' },
  { key: 'nav.blog', href: '/blog' },
  { key: 'footer.contact', href: '/contact' }
];

const servicesData = [
  'services.facial.title',
  'services.botox.title',
  'services.laser.title',
  'serviceDetail.procedure',
  'serviceDetail.aftercare'
];

const QuickLinksServices = () => {
  const { t } = useLanguage();

  return (
    <section className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <ModernCard>
        <h4 className="text-2xl font-semibold mb-6 text-foreground">{t('about.quickLinks.title')}</h4>
        <ul className="space-y-3">
          {quickLinksData.map((link, idx) => (
            <li key={idx}>
              <a
                href={link.href}
                className="text-foreground/80 hover:text-foreground hover:underline transition-colors text-lg"
              >
                {t(link.key)}
              </a>
            </li>
          ))}
        </ul>
      </ModernCard>

      <ModernCard>
        <h4 className="text-2xl font-semibold mb-6 text-foreground">{t('about.servicesSection.title')}</h4>
        <ul className="space-y-4">
          {servicesData.map((service, idx) => (
            <li key={idx} className="flex justify-between items-center">
              <span className="text-foreground/80 text-lg">{t(service)}</span>
              <Button 
                variant="ghost" 
                size="sm" 
                className="text-pink-200 hover:text-foreground hover:bg-white/10"
              >
                {t('about.servicesSection.learnMore')}
              </Button>
            </li>
          ))}
        </ul>
      </ModernCard>
    </section>
  );
};

export default QuickLinksServices;
