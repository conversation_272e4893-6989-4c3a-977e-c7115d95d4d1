
import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';

const AboutHero = () => {
  const { t } = useLanguage();

  return (
    <section className="relative bg-gradient-soft min-h-[400px] flex items-center rounded-lg">
      <div className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-6">
          {t('about.hero.title')}
        </h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
          {t('about.hero.subtitle')}
        </p>
      </div>
    </section>
  );
};

export default AboutHero;
