
import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/AuthProvider';
import { useUserProfile } from '@/hooks/useUserProfile';
import { useUserAppointments } from '@/hooks/useUserProfile';
import DashboardStats from '@/components/dashboard/DashboardStats';
import AppointmentsList from '@/components/dashboard/AppointmentsList';
import ProfileEditor from '@/components/dashboard/ProfileEditor';

const PatientDashboard = () => {
  const { user } = useAuth();
  const { data: profileData } = useUserProfile(user?.id);
  const { data: appointmentsData } = useUserAppointments(user?.id);
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [editedProfile, setEditedProfile] = useState({
    first_name: '',
    last_name: '',
    phone: '',
    email: '',
    address: '',
    medical_history: '',
    allergies: '',
    current_medications: ''
  });

  const appointments = appointmentsData?.data || [];
  const profile = profileData?.data;

  React.useEffect(() => {
    if (profile) {
      setEditedProfile({
        first_name: profile.first_name || '',
        last_name: profile.last_name || '',
        phone: profile.phone || '',
        email: profile.email || '',
        address: profile.address || '',
        medical_history: profile.medical_history || '',
        allergies: profile.allergies || '',
        current_medications: profile.current_medications || ''
      });
    }
  }, [profile]);

  const upcomingAppointments = appointments.filter(apt => 
    apt.status === 'scheduled' || apt.status === 'confirmed'
  );
  
  const completedAppointments = appointments.filter(apt => 
    apt.status === 'completed'
  );

  const totalSpent = appointments.reduce((total, apt) => total + (apt.total_amount || 0), 0);

  const handleEditProfile = () => {
    setIsEditingProfile(true);
  };

  const handleSaveProfile = () => {
    setIsEditingProfile(false);
  };

  const handleCancelEdit = () => {
    if (profile) {
      setEditedProfile({
        first_name: profile.first_name || '',
        last_name: profile.last_name || '',
        phone: profile.phone || '',
        email: profile.email || '',
        address: profile.address || '',
        medical_history: profile.medical_history || '',
        allergies: profile.allergies || '',
        current_medications: profile.current_medications || ''
      });
    }
    setIsEditingProfile(false);
  };

  const handleProfileChange = (field: string, value: string) => {
    setEditedProfile(prev => ({ ...prev, [field]: value }));
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">Please log in to view your dashboard</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Welcome back, {profile?.first_name || 'Patient'}
            </h1>
            <p className="text-gray-600">Manage your appointments and profile</p>
          </div>
        </div>

        <DashboardStats
          upcomingCount={upcomingAppointments.length}
          completedCount={completedAppointments.length}
          totalSpent={totalSpent}
          reviewsCount={0}
        />

        <Tabs defaultValue="appointments" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="appointments">Appointments</TabsTrigger>
            <TabsTrigger value="profile">Profile</TabsTrigger>
            <TabsTrigger value="history">Medical History</TabsTrigger>
          </TabsList>

          <TabsContent value="appointments" className="space-y-6">
            <AppointmentsList appointments={appointments} />
          </TabsContent>

          <TabsContent value="profile" className="space-y-6">
            <ProfileEditor
              profile={profile}
              isEditing={isEditingProfile}
              editedProfile={editedProfile}
              onEdit={handleEditProfile}
              onSave={handleSaveProfile}
              onCancel={handleCancelEdit}
              onProfileChange={handleProfileChange}
            />
          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <Card>
              <CardContent className="space-y-6 pt-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Medical History</label>
                  <p className="text-gray-900">{profile?.medical_history || 'No medical history provided'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Allergies</label>
                  <p className="text-gray-900">{profile?.allergies || 'No allergies reported'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Current Medications</label>
                  <p className="text-gray-900">{profile?.current_medications || 'No current medications'}</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default PatientDashboard;
