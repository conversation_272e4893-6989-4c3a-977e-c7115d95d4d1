
/**
 * Lullaby Clinic - Email Notification Admin Panel
 * Admin interface for managing email notifications
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import React, { useState } from 'react';
import { Settings, <PERSON>, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { useLanguage } from '@/contexts/LanguageContext';
import {
  useSendBatchReminders,
  useEmailNotificationStatus,
  useSendAppointmentConfirmation,
  useSendAppointmentReminder,
  useSendAppointmentCancellation
} from '@/hooks/useEmailNotifications';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import EmailStatsCards from './email/EmailStatsCards';
import EmailStatusCard from './email/EmailStatusCard';
import EmailTestPanel from './email/EmailTestPanel';

const EmailNotificationPanel: React.FC = () => {
  const { t } = useLanguage();
  const { toast } = useToast();
  
  const [selectedAppointmentId, setSelectedAppointmentId] = useState<string>('');
  const [emailLanguage, setEmailLanguage] = useState<'th' | 'en'>('th');
  const [autoNotifications, setAutoNotifications] = useState({
    confirmation: true,
    reminder: true,
    cancellation: true
  });

  // Hooks
  const sendBatchReminders = useSendBatchReminders();
  const emailStatus = useEmailNotificationStatus();
  const sendConfirmation = useSendAppointmentConfirmation();
  const sendReminder = useSendAppointmentReminder();
  const sendCancellation = useSendAppointmentCancellation();

  // Get recent appointments for testing
  const { data: recentAppointments, isLoading: appointmentsLoading } = useQuery({
    queryKey: ['recent-appointments-for-email'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('appointments')
        .select(`
          id,
          appointment_date,
          status,
          patient:user_profiles(first_name, last_name, email),
          service:services(name),
          doctor:doctors(user_profile:user_profiles(first_name, last_name))
        `)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;
      return data;
    }
  });

  // Get email notification statistics
  const { data: emailStats } = useQuery({
    queryKey: ['email-notification-stats'],
    queryFn: async () => {
      return {
        today: 12,
        thisWeek: 45,
        total: 234
      };
    },
    refetchInterval: 30000
  });

  const handleSendBatchReminders = async () => {
    try {
      await sendBatchReminders.mutateAsync();
    } catch (error) {
      console.error('Failed to send batch reminders:', error);
    }
  };

  const handleSendTestEmail = async (type: 'confirmation' | 'reminder' | 'cancellation') => {
    if (!selectedAppointmentId) {
      toast({
        title: t('notifications.selectAppointment'),
        description: t('notifications.selectAppointmentDesc'),
        variant: 'destructive'
      });
      return;
    }

    try {
      switch (type) {
        case 'confirmation':
          await sendConfirmation.mutateAsync({ 
            appointmentId: selectedAppointmentId, 
            language: emailLanguage 
          });
          break;
        case 'reminder':
          await sendReminder.mutateAsync({ 
            appointmentId: selectedAppointmentId, 
            language: emailLanguage 
          });
          break;
        case 'cancellation':
          await sendCancellation.mutateAsync({ 
            appointmentId: selectedAppointmentId, 
            language: emailLanguage 
          });
          break;
      }
    } catch (error) {
      console.error(`Failed to send ${type} email:`, error);
    }
  };

  const renderAutomationPanel = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Settings className="w-5 h-5" />
          <span>{t('notifications.automation')}</span>
        </CardTitle>
        <CardDescription>
          {t('notifications.automationDesc')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium">{t('notifications.autoConfirmation')}</div>
              <div className="text-sm text-gray-600">{t('notifications.autoConfirmationDesc')}</div>
            </div>
            <Switch
              checked={autoNotifications.confirmation}
              onCheckedChange={(checked) => 
                setAutoNotifications(prev => ({ ...prev, confirmation: checked }))
              }
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium">{t('notifications.autoReminder')}</div>
              <div className="text-sm text-gray-600">{t('notifications.autoReminderDesc')}</div>
            </div>
            <Switch
              checked={autoNotifications.reminder}
              onCheckedChange={(checked) => 
                setAutoNotifications(prev => ({ ...prev, reminder: checked }))
              }
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium">{t('notifications.autoCancellation')}</div>
              <div className="text-sm text-gray-600">{t('notifications.autoCancellationDesc')}</div>
            </div>
            <Switch
              checked={autoNotifications.cancellation}
              onCheckedChange={(checked) => 
                setAutoNotifications(prev => ({ ...prev, cancellation: checked }))
              }
            />
          </div>
        </div>

        <Separator />

        <div className="space-y-4">
          <h4 className="font-medium">{t('notifications.batchOperations')}</h4>
          
          <Button
            onClick={handleSendBatchReminders}
            disabled={sendBatchReminders.isPending}
            className="w-full"
          >
            {sendBatchReminders.isPending ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Bell className="w-4 h-4 mr-2" />
            )}
            {t('notifications.sendTomorrowReminders')}
          </Button>
          
          <p className="text-sm text-gray-600">
            {t('notifications.sendTomorrowRemindersDesc')}
          </p>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">{t('notifications.emailNotifications')}</h1>
        <p className="text-gray-600">{t('notifications.emailNotificationsDesc')}</p>
      </div>

      <EmailStatsCards emailStats={emailStats} />

      <Tabs defaultValue="status" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="status">{t('notifications.status')}</TabsTrigger>
          <TabsTrigger value="test">{t('notifications.testing')}</TabsTrigger>
          <TabsTrigger value="automation">{t('notifications.automation')}</TabsTrigger>
          <TabsTrigger value="logs">{t('notifications.logs')}</TabsTrigger>
        </TabsList>

        <TabsContent value="status" className="space-y-4">
          <EmailStatusCard emailStatus={emailStatus} />
        </TabsContent>

        <TabsContent value="test" className="space-y-4">
          <EmailTestPanel
            selectedAppointmentId={selectedAppointmentId}
            setSelectedAppointmentId={setSelectedAppointmentId}
            emailLanguage={emailLanguage}
            setEmailLanguage={setEmailLanguage}
            recentAppointments={recentAppointments}
            appointmentsLoading={appointmentsLoading}
            onSendTestEmail={handleSendTestEmail}
            sendConfirmation={sendConfirmation}
            sendReminder={sendReminder}
            sendCancellation={sendCancellation}
          />
        </TabsContent>

        <TabsContent value="automation" className="space-y-4">
          {renderAutomationPanel()}
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('notifications.recentLogs')}</CardTitle>
              <CardDescription>
                {t('notifications.recentLogsDesc')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                {t('notifications.logsComingSoon')}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default EmailNotificationPanel;
