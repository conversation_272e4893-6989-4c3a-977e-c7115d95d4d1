
import React from 'react';
import { Mail, Calendar, Users } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { useLanguage } from '@/contexts/LanguageContext';

interface EmailStatsData {
  today: number;
  thisWeek: number;
  total: number;
}

interface EmailStatsCardsProps {
  emailStats?: EmailStatsData;
}

const EmailStatsCards: React.FC<EmailStatsCardsProps> = ({ emailStats }) => {
  const { t } = useLanguage();

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <Mail className="w-5 h-5 text-blue-600" />
            <div>
              <div className="text-sm text-gray-600">{t('notifications.todayEmails')}</div>
              <div className="text-2xl font-bold">{emailStats?.today || 0}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <Calendar className="w-5 h-5 text-green-600" />
            <div>
              <div className="text-sm text-gray-600">{t('notifications.weekEmails')}</div>
              <div className="text-2xl font-bold">{emailStats?.thisWeek || 0}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <Users className="w-5 h-5 text-purple-600" />
            <div>
              <div className="text-sm text-gray-600">{t('notifications.totalEmails')}</div>
              <div className="text-2xl font-bold">{emailStats?.total || 0}</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EmailStatsCards;
