
import React from 'react';
import { Activity, Loader2, AlertCircle } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useLanguage } from '@/contexts/LanguageContext';

interface EmailStatusData {
  enabled: boolean;
  configured: boolean;
  provider: string;
}

interface EmailStatusCardProps {
  emailStatus: {
    data?: EmailStatusData;
    isLoading: boolean;
  };
}

const EmailStatusCard: React.FC<EmailStatusCardProps> = ({ emailStatus }) => {
  const { t } = useLanguage();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Activity className="w-5 h-5" />
          <span>{t('notifications.systemStatus')}</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {emailStatus.isLoading ? (
          <div className="flex items-center space-x-2">
            <Loader2 className="w-4 h-4 animate-spin" />
            <span>{t('common.loading')}</span>
          </div>
        ) : (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span>{t('notifications.emailService')}</span>
              <Badge variant={emailStatus.data?.enabled ? 'default' : 'destructive'}>
                {emailStatus.data?.enabled ? t('status.enabled') : t('status.disabled')}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span>{t('notifications.configuration')}</span>
              <Badge variant={emailStatus.data?.configured ? 'default' : 'secondary'}>
                {emailStatus.data?.configured ? t('status.configured') : t('status.notConfigured')}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span>{t('notifications.provider')}</span>
              <span className="text-sm text-gray-600">
                {emailStatus.data?.provider || 'Console'}
              </span>
            </div>

            {!emailStatus.data?.enabled && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>{t('notifications.configurationRequired')}</AlertTitle>
                <AlertDescription>
                  {t('notifications.configurationRequiredDesc')}
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default EmailStatusCard;
