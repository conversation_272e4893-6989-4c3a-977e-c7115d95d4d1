
import React from 'react';
import { Send, Loader2, <PERSON><PERSON><PERSON><PERSON>, Clock, XCircle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { useLanguage } from '@/contexts/LanguageContext';

interface AppointmentForTest {
  id: string;
  patient?: {
    first_name: string;
    last_name: string;
  };
  service?: {
    name: string;
  };
}

interface EmailTestPanelProps {
  selectedAppointmentId: string;
  setSelectedAppointmentId: (id: string) => void;
  emailLanguage: 'th' | 'en';
  setEmailLanguage: (lang: 'th' | 'en') => void;
  recentAppointments?: AppointmentForTest[];
  appointmentsLoading: boolean;
  onSendTestEmail: (type: 'confirmation' | 'reminder' | 'cancellation') => void;
  sendConfirmation: { isPending: boolean };
  sendReminder: { isPending: boolean };
  sendCancellation: { isPending: boolean };
}

const EmailTestPanel: React.FC<EmailTestPanelProps> = ({
  selectedAppointmentId,
  setSelectedAppointmentId,
  emailLanguage,
  setEmailLanguage,
  recentAppointments,
  appointmentsLoading,
  onSendTestEmail,
  sendConfirmation,
  sendReminder,
  sendCancellation
}) => {
  const { t } = useLanguage();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Send className="w-5 h-5" />
          <span>{t('notifications.testEmails')}</span>
        </CardTitle>
        <CardDescription>
          {t('notifications.testEmailsDesc')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="appointment-select">{t('notifications.selectAppointment')}</Label>
            <Select
              value={selectedAppointmentId}
              onValueChange={setSelectedAppointmentId}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('notifications.chooseAppointment')} />
              </SelectTrigger>
              <SelectContent>
                {appointmentsLoading ? (
                  <div className="p-2 text-center">
                    <Loader2 className="w-4 h-4 animate-spin mx-auto" />
                  </div>
                ) : (
                  recentAppointments?.map((appointment) => (
                    <SelectItem key={appointment.id} value={appointment.id}>
                      {appointment.patient?.first_name} {appointment.patient?.last_name} - {appointment.service?.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="email-language">{t('notifications.emailLanguage')}</Label>
            <Select
              value={emailLanguage}
              onValueChange={(value: 'th' | 'en') => setEmailLanguage(value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="th">ไทย (Thai)</SelectItem>
                <SelectItem value="en">English</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Separator />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button
            onClick={() => onSendTestEmail('confirmation')}
            disabled={!selectedAppointmentId || sendConfirmation.isPending}
            variant="outline"
          >
            {sendConfirmation.isPending ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <CheckCircle className="w-4 h-4 mr-2" />
            )}
            {t('notifications.testConfirmation')}
          </Button>

          <Button
            onClick={() => onSendTestEmail('reminder')}
            disabled={!selectedAppointmentId || sendReminder.isPending}
            variant="outline"
          >
            {sendReminder.isPending ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Clock className="w-4 h-4 mr-2" />
            )}
            {t('notifications.testReminder')}
          </Button>

          <Button
            onClick={() => onSendTestEmail('cancellation')}
            disabled={!selectedAppointmentId || sendCancellation.isPending}
            variant="outline"
          >
            {sendCancellation.isPending ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <XCircle className="w-4 h-4 mr-2" />
            )}
            {t('notifications.testCancellation')}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default EmailTestPanel;
