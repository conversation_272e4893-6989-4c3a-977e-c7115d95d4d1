
import React from 'react';
import { Images, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import BeforeAfterComparison from '@/components/BeforeAfterComparison';

interface BeforeAfterShowcaseProps {
  translations: {
    beforeAfter: {
      badge: string;
      title: string;
      subtitle: string;
      viewGallery: string;
      comparison1: { title: string; description: string };
      comparison2: { title: string; description: string };
      comparison3: { title: string; description: string };
    };
  };
}

const BeforeAfterShowcase = ({ translations }: BeforeAfterShowcaseProps) => {
  const comparisons = [
    {
      id: 1,
      beforeImage: "/placeholder.svg",
      afterImage: "/lovable-uploads/f2cc713d-2432-4879-9096-77fb8e115404.png",
      title: translations.beforeAfter.comparison1.title,
      description: translations.beforeAfter.comparison1.description
    },
    {
      id: 2,
      beforeImage: "/placeholder.svg",
      afterImage: "/lovable-uploads/f2cc713d-2432-4879-9096-77fb8e115404.png",
      title: translations.beforeAfter.comparison2.title,
      description: translations.beforeAfter.comparison2.description
    },
    {
      id: 3,
      beforeImage: "/placeholder.svg",
      afterImage: "/lovable-uploads/f2cc713d-2432-4879-9096-77fb8e115404.png",
      title: translations.beforeAfter.comparison3.title,
      description: translations.beforeAfter.comparison3.description
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-primary-50 to-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-primary-100 text-primary-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Images className="h-4 w-4 mr-2" />
            {translations.beforeAfter.badge}
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            {translations.beforeAfter.title}
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {translations.beforeAfter.subtitle}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8 mb-12">
          {comparisons.map((comparison) => (
            <BeforeAfterComparison
              key={comparison.id}
              beforeImage={comparison.beforeImage}
              afterImage={comparison.afterImage}
              title={comparison.title}
              description={comparison.description}
              className="animate-fade-in hover:shadow-soft transition-all duration-300"
            />
          ))}
        </div>

        <div className="text-center">
          <Button 
            variant="outline" 
            size="lg" 
            className="border-primary-200 text-primary-600 hover:bg-primary-50"
            onClick={() => {
              const gallerySection = document.getElementById('gallery');
              gallerySection?.scrollIntoView({ behavior: 'smooth' });
            }}
          >
            {translations.beforeAfter.viewGallery}
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default BeforeAfterShowcase;
