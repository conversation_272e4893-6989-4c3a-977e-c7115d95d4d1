
import React from 'react';
import { Calendar, Clock, ArrowRight, BookOpen } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

interface BlogSectionProps {
  translations: {
    blog: {
      badge: string;
      title: string;
      subtitle: string;
      readMore: string;
      viewAll: string;
      post1: {
        title: string;
        excerpt: string;
        date: string;
        readTime: string;
        category: string;
      };
      post2: {
        title: string;
        excerpt: string;
        date: string;
        readTime: string;
        category: string;
      };
      post3: {
        title: string;
        excerpt: string;
        date: string;
        readTime: string;
        category: string;
      };
    };
  };
}

const BlogSection = ({ translations }: BlogSectionProps) => {
  const blogPosts = [
    {
      title: translations.blog.post1.title,
      excerpt: translations.blog.post1.excerpt,
      image: "/placeholder.svg",
      date: translations.blog.post1.date,
      readTime: translations.blog.post1.readTime,
      category: translations.blog.post1.category
    },
    {
      title: translations.blog.post2.title,
      excerpt: translations.blog.post2.excerpt,
      image: "/placeholder.svg",
      date: translations.blog.post2.date,
      readTime: translations.blog.post2.readTime,
      category: translations.blog.post2.category
    },
    {
      title: translations.blog.post3.title,
      excerpt: translations.blog.post3.excerpt,
      image: "/placeholder.svg",
      date: translations.blog.post3.date,
      readTime: translations.blog.post3.readTime,
      category: translations.blog.post3.category
    }
  ];

  return (
    <section id="blog" className="py-20 bg-medical-softPink">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-primary-100 text-primary-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <BookOpen className="h-4 w-4 mr-2" />
            {translations.blog.badge}
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            {translations.blog.title}
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {translations.blog.subtitle}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {blogPosts.map((post, index) => (
            <Card key={index} className="group hover:shadow-soft transition-all duration-300 hover:-translate-y-2 bg-white">
              <div className="aspect-video relative overflow-hidden rounded-t-lg">
                <img 
                  src={post.image}
                  alt={post.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-4 left-4">
                  <span className="bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                    {post.category}
                  </span>
                </div>
              </div>

              <CardHeader>
                <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-3">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    {post.date}
                  </div>
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-1" />
                    {post.readTime}
                  </div>
                </div>
                
                <h3 className="text-xl font-semibold text-foreground group-hover:text-primary-600 transition-colors leading-tight">
                  {post.title}
                </h3>
              </CardHeader>

              <CardContent>
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  {post.excerpt}
                </p>
                
                <Button variant="ghost" className="text-primary-600 hover:text-primary-700 hover:bg-primary-50 p-0 group">
                  {translations.blog.readMore}
                  <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button variant="outline" size="lg" className="border-primary-200 text-primary-600 hover:bg-primary-50">
            {translations.blog.viewAll}
            <BookOpen className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default BlogSection;
