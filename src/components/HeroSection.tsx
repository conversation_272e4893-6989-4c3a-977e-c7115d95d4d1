
import React, { useState } from 'react';
import type { Translation } from '@/utils/translations';
import { Calendar, MapPin, User, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

interface HeroSectionProps {
  translations: Translation;
}

const HeroSection = ({ translations }: HeroSectionProps) => {
  const [selectedService, setSelectedService] = useState('');
  const [selectedDoctor, setSelectedDoctor] = useState('');
  const [selectedBranch, setSelectedBranch] = useState('');
  const [showBookingModal, setShowBookingModal] = useState(false);

  const handleBooking = () => {
    if (selectedService && selectedDoctor && selectedBranch) {
      setShowBookingModal(true);
    }
  };

  return (
    <section id="home" className="relative bg-gradient-soft min-h-[600px] flex items-center">
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="space-y-6 animate-fade-in">
            <div className="inline-flex items-center bg-primary-100 text-primary-700 px-4 py-2 rounded-full text-sm font-medium">
              <span className="w-2 h-2 bg-primary-500 rounded-full mr-2"></span>
              {translations.hero.badge}
            </div>
            
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground leading-tight">
              {translations.hero.title.split(' ').map((word: string, index: number) => (
                <span key={index} className={index === 1 ? 'text-gradient' : ''}>
                  {word}{' '}
                </span>
              ))}
            </h1>
            
            <p className="text-lg text-muted-foreground leading-relaxed max-w-xl">
              {translations.hero.subtitle}
            </p>

            {/* Booking Card */}
            <Card className="p-6 shadow-soft bg-white/80 backdrop-blur-sm">
              <h3 className="text-lg font-semibold mb-4 text-foreground">
                {translations.hero.bookingTitle}
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <Select value={selectedService} onValueChange={setSelectedService}>
                  <SelectTrigger>
                    <User className="h-4 w-4 mr-2" />
                    <SelectValue placeholder={translations.hero.selectService} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="facial">Facial Treatment</SelectItem>
                    <SelectItem value="botox">Botox</SelectItem>
                    <SelectItem value="filler">Dermal Filler</SelectItem>
                    <SelectItem value="laser">Laser Treatment</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={selectedDoctor} onValueChange={setSelectedDoctor}>
                  <SelectTrigger>
                    <User className="h-4 w-4 mr-2" />
                    <SelectValue placeholder={translations.hero.selectDoctor} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="dr-smith">Dr. Smith</SelectItem>
                    <SelectItem value="dr-johnson">Dr. Johnson</SelectItem>
                    <SelectItem value="dr-lee">Dr. Lee</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={selectedBranch} onValueChange={setSelectedBranch}>
                  <SelectTrigger>
                    <MapPin className="h-4 w-4 mr-2" />
                    <SelectValue placeholder={translations.hero.selectBranch} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="central">Central Bangkok</SelectItem>
                    <SelectItem value="sukhumvit">Sukhumvit</SelectItem>
                    <SelectItem value="silom">Silom</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Dialog open={showBookingModal} onOpenChange={setShowBookingModal}>
                <DialogTrigger asChild>
                  <Button 
                    onClick={handleBooking}
                    className="w-full bg-primary-500 hover:bg-primary-600 text-white"
                    disabled={!selectedService || !selectedDoctor || !selectedBranch}
                  >
                    <Calendar className="h-4 w-4 mr-2" />
                    {translations.hero.bookButton}
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>{translations.hero.bookingModal.title}</DialogTitle>
                    <DialogDescription>
                      {translations.hero.bookingModal.description}
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="bg-medical-pink p-4 rounded-lg">
                      <h4 className="font-semibold mb-2">{translations.hero.bookingModal.summary}</h4>
                      <p><strong>Service:</strong> {selectedService}</p>
                      <p><strong>Doctor:</strong> {selectedDoctor}</p>
                      <p><strong>Branch:</strong> {selectedBranch}</p>
                    </div>
                    <Button className="w-full bg-primary-500 hover:bg-primary-600 text-white">
                      {translations.hero.bookingModal.confirm}
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </Card>
          </div>

          {/* Hero Image */}
          <div className="relative animate-fade-in">
            <div className="relative h-[500px] rounded-2xl overflow-hidden shadow-soft">
              <img 
                src="/lovable-uploads/f2cc713d-2432-4879-9096-77fb8e115404.png"
                alt="Lullaby Clinic Hero"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
            </div>
            
            {/* Floating elements */}
            <div className="absolute -top-4 -right-4 bg-white p-4 rounded-xl shadow-soft">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary-600">5000+</div>
                <div className="text-sm text-muted-foreground">{translations.hero.happyClients}</div>
              </div>
            </div>
            
            <div className="absolute -bottom-4 -left-4 bg-white p-4 rounded-xl shadow-soft">
              <div className="text-center">
                <div className="text-2xl font-bold text-medical-blue">15+</div>
                <div className="text-sm text-muted-foreground">{translations.hero.yearsExperience}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
