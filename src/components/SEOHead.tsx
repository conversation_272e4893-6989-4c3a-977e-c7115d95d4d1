import React from 'react';
import { Helmet } from 'react-helmet-async';

interface SEOHeadProps {
  lang: string;
  title?: string;
  description?: string;
  keywords?: string;
  canonicalUrl?: string;
  ogImage?: string;
  ogType?: string;
  structuredData?: object;
}

const SEOHead: React.FC<SEOHeadProps> = ({
  lang = 'en',
  title,
  description,
  keywords,
  canonicalUrl,
  ogImage = '/logo.svg',
  ogType = 'website',
  structuredData
}) => {
  // Default multilingual content
  const seoContent = {
    th: {
      title: 'คลินิกลูลลาบาย - คลินิกความงามและการแพทย์เสริมความงามระดับพรีเมียม',
      description: 'เปลี่ยนความฝันของคุณให้เป็นจริงที่คลินิกลูลลาบาย คลินิกความงามชั้นนำด้วยเทคโนโลยีล้ำสมัยและทีมแพทย์ผู้เชี่ยวชาญสำหรับการรักษาด้านความงามอย่างครcomprehensive',
      keywords: 'คลินิกความงาม, การแพทย์เสริมความงาม, ศัลยกรรมตกแต่ง, ดูแลผิวหน้า, บอทอกซ์, ฟิลเลอร์, เลเซอร์, ลูลลาบาย',
      siteName: 'คลินิกลูลลาบาย'
    },
    en: {
      title: 'Lullaby Clinic - Premium Beauty & Medical Aesthetics',
      description: 'Transform your dreams into reality at Lullaby Clinic. Leading beauty clinic with cutting-edge technology and expert medical team for comprehensive aesthetic treatments.',
      keywords: 'beauty clinic, medical aesthetics, cosmetic surgery, skincare, botox, fillers, laser treatments, lullaby clinic',
      siteName: 'Lullaby Clinic'
    },
    zh: {
      title: '摇篮诊所 - 高端美容医疗美学',
      description: '在摇篮诊所实现您的美丽梦想。领先的美容诊所，拥有尖端技术和专业医疗团队，提供全面的美学治疗。',
      keywords: '美容诊所, 医疗美学, 整容手术, 护肤, 肉毒杆菌, 填充剂, 激光治疗, 摇篮诊所',
      siteName: '摇篮诊所'
    }
  };

  const currentLang = lang as keyof typeof seoContent;
  const content = seoContent[currentLang] || seoContent.en;
  
  const finalTitle = title || content.title;
  const finalDescription = description || content.description;
  const finalKeywords = keywords || content.keywords;
  const siteUrl = 'https://lullabyclinic.com';
  const finalCanonicalUrl = canonicalUrl || `${siteUrl}/${lang === 'en' ? '' : lang}`;
  const finalOgImage = ogImage.startsWith('http') ? ogImage : `${siteUrl}${ogImage}`;

  // Medical practice structured data
  const medicalBusinessSchema = {
    "@context": "https://schema.org",
    "@type": "MedicalBusiness",
    "name": content.siteName,
    "description": finalDescription,
    "url": siteUrl,
    "logo": `${siteUrl}/logo.svg`,
    "image": finalOgImage,
    "telephone": "+66-64-646-8656",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "123 Medical Center",
      "addressLocality": "Bangkok",
      "addressRegion": "Bangkok",
      "postalCode": "10110",
      "addressCountry": "TH"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": "13.7563",
      "longitude": "100.5018"
    },
    "openingHoursSpecification": [
      {
        "@type": "OpeningHoursSpecification",
        "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
        "opens": "09:00",
        "closes": "18:00"
      },
      {
        "@type": "OpeningHoursSpecification",
        "dayOfWeek": ["Saturday"],
        "opens": "09:00",
        "closes": "17:00"
      }
    ],
    "medicalSpecialty": [
      "Dermatology",
      "Plastic Surgery",
      "Cosmetic Surgery",
      "Aesthetic Medicine"
    ],
    "serviceType": [
      "Botox Injections",
      "Dermal Fillers",
      "Laser Treatments",
      "Facial Treatments",
      "Body Contouring"
    ],
    "areaServed": {
      "@type": "GeoCircle",
      "geoMidpoint": {
        "@type": "GeoCoordinates",
        "latitude": "13.7563",
        "longitude": "100.5018"
      },
      "geoRadius": "50000"
    },
    "priceRange": "$$-$$$",
    "currenciesAccepted": "THB",
    "paymentAccepted": "Cash, Credit Card, Bank Transfer",
    "languagesSpoken": ["Thai", "English", "Chinese"],
    "sameAs": [
      "https://www.facebook.com/lullabyclinic",
      "https://www.instagram.com/lullabyclinic",
      "https://line.me/lullabyclinic"
    ]
  };

  // WebSite structured data for search box
  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": content.siteName,
    "url": siteUrl,
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${siteUrl}/search?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    },
    "inLanguage": [
      {
        "@type": "Language",
        "name": "Thai",
        "alternateName": "th"
      },
      {
        "@type": "Language", 
        "name": "English",
        "alternateName": "en"
      },
      {
        "@type": "Language",
        "name": "Chinese",
        "alternateName": "zh"
      }
    ]
  };

  // Organization structured data
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": content.siteName,
    "url": siteUrl,
    "logo": `${siteUrl}/logo.svg`,
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+66-64-646-8656",
      "contactType": "customer service",
      "availableLanguage": ["Thai", "English", "Chinese"],
      "areaServed": "TH"
    },
    "founder": {
      "@type": "Person",
      "name": "Dr. Lullaby Clinic Team"
    },
    "foundingDate": "2020",
    "description": finalDescription
  };

  const combinedStructuredData = structuredData || {
    "@context": "https://schema.org",
    "@graph": [medicalBusinessSchema, websiteSchema, organizationSchema]
  };

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <html lang={lang} />
      <title>{finalTitle}</title>
      <meta name="description" content={finalDescription} />
      <meta name="keywords" content={finalKeywords} />
      <meta name="author" content={content.siteName} />
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
      <meta name="googlebot" content="index, follow" />
      
      {/* Canonical URL */}
      <link rel="canonical" href={finalCanonicalUrl} />
      
      {/* Language Alternates */}
      <link rel="alternate" hrefLang="th" href={`${siteUrl}/th`} />
      <link rel="alternate" hrefLang="en" href={`${siteUrl}/`} />
      <link rel="alternate" hrefLang="zh" href={`${siteUrl}/zh`} />
      <link rel="alternate" hrefLang="x-default" href={`${siteUrl}/`} />
      
      {/* Open Graph Meta Tags */}
      <meta property="og:type" content={ogType} />
      <meta property="og:title" content={finalTitle} />
      <meta property="og:description" content={finalDescription} />
      <meta property="og:image" content={finalOgImage} />
      <meta property="og:image:alt" content={finalTitle} />
      <meta property="og:url" content={finalCanonicalUrl} />
      <meta property="og:site_name" content={content.siteName} />
      <meta property="og:locale" content={lang === 'th' ? 'th_TH' : lang === 'zh' ? 'zh_CN' : 'en_US'} />
      {lang !== 'en' && <meta property="og:locale:alternate" content="en_US" />}
      {lang !== 'th' && <meta property="og:locale:alternate" content="th_TH" />}
      {lang !== 'zh' && <meta property="og:locale:alternate" content="zh_CN" />}
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={finalTitle} />
      <meta name="twitter:description" content={finalDescription} />
      <meta name="twitter:image" content={finalOgImage} />
      <meta name="twitter:image:alt" content={finalTitle} />
      <meta name="twitter:site" content="@lullabyclinic" />
      <meta name="twitter:creator" content="@lullabyclinic" />
      
      {/* Additional Meta Tags for Medical Website */}
      <meta name="format-detection" content="telephone=yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content={content.siteName} />
      <meta name="theme-color" content="#EF476F" />
      <meta name="msapplication-TileColor" content="#EF476F" />
      <meta name="msapplication-navbutton-color" content="#EF476F" />
      
      {/* Preconnect for Performance */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      
      {/* Favicon and Icons */}
      <link rel="icon" type="image/svg+xml" href="/logo.svg" />
      <link rel="apple-touch-icon" sizes="180x180" href="/logo.png" />
      <link rel="icon" type="image/png" sizes="32x32" href="/logo.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/logo.png" />
      
      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify(combinedStructuredData, null, 2)}
      </script>
      
      {/* Additional Performance Hints */}
      <link rel="preload" href="/fonts/kanit.woff2" as="font" type="font/woff2" crossOrigin="" />
      <link rel="preload" href="/fonts/poppins.woff2" as="font" type="font/woff2" crossOrigin="" />
    </Helmet>
  );
};

export default SEOHead;