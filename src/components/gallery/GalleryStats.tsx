
import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import GlassCard from './GlassCard';

const GalleryStats = () => {
  const { t } = useLanguage();

  return (
    <section>
      <GlassCard>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div>
            <div className="text-4xl font-bold text-white mb-2">5000+</div>
            <div className="text-white/80">{t('gallery.stats.happyClients')}</div>
          </div>
          <div>
            <div className="text-4xl font-bold text-white mb-2">15+</div>
            <div className="text-white/80">{t('gallery.stats.yearsExperience')}</div>
          </div>
          <div>
            <div className="text-4xl font-bold text-white mb-2">98%</div>
            <div className="text-white/80">{t('gallery.stats.satisfaction')}</div>
          </div>
          <div>
            <div className="text-4xl font-bold text-white mb-2">500+</div>
            <div className="text-white/80">{t('gallery.stats.procedures')}</div>
          </div>
        </div>
      </GlassCard>
    </section>
  );
};

export default GalleryStats;
