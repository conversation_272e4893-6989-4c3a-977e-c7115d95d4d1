
import React, { memo, useState } from 'react';
import { Play } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { useLanguage } from '@/contexts/LanguageContext';
import GlassCard from './GlassCard';

interface GalleryItemData {
  id: string;
  category: string;
  beforeImage: string;
  afterImage: string;
  featured: boolean;
  video: boolean;
}

interface GalleryItemProps {
  item: GalleryItemData;
}

const GalleryItem = memo<GalleryItemProps>(({ item }) => {
  const { t } = useLanguage();
  
  return (
    <Dialog>
      <DialogTrigger asChild>
        <GlassCard hover className="cursor-pointer">
          <div className="space-y-4">
            <div className="relative">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-4xl mb-2">{item.beforeImage}</div>
                  <Badge variant="secondary" className="bg-white/20 text-white text-xs">
                    {t('gallery.before')}
                  </Badge>
                </div>
                <div className="text-center">
                  <div className="text-4xl mb-2">{item.afterImage}</div>
                  <Badge variant="secondary" className="bg-white/20 text-white text-xs">
                    {t('gallery.after')}
                  </Badge>
                </div>
              </div>
              {item.video && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="bg-black/50 rounded-full p-3">
                    <Play className="w-6 h-6 text-white" />
                  </div>
                </div>
              )}
            </div>
            
            <div className="text-center">
              <h3 className="text-lg font-semibold text-white mb-2">
                {t(`gallery.items.${item.id}.title`)}
              </h3>
              <p className="text-white/80 text-sm">
                {t(`gallery.items.${item.id}.description`)}
              </p>
            </div>
          </div>
        </GlassCard>
      </DialogTrigger>
      <DialogContent className="max-w-4xl">
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-8">
            <div className="text-center">
              <div className="text-8xl mb-4">{item.beforeImage}</div>
              <Badge variant="outline">{t('gallery.before')}</Badge>
            </div>
            <div className="text-center">
              <div className="text-8xl mb-4">{item.afterImage}</div>
              <Badge variant="outline">{t('gallery.after')}</Badge>
            </div>
          </div>
          <div className="text-center">
            <h3 className="text-2xl font-bold mb-2">
              {t(`gallery.items.${item.id}.title`)}
            </h3>
            <p className="text-muted-foreground">
              {t(`gallery.items.${item.id}.fullDescription`)}
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
});

GalleryItem.displayName = 'GalleryItem';

export default GalleryItem;
