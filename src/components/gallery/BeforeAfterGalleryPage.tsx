import React from 'react';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import SEOHead from '@/components/SEOHead';
import ErrorBoundary from '@/components/ErrorBoundary';
import { useLanguage } from '@/contexts/LanguageContext';
import { BeforeAfterGallery } from '@/components/BeforeAfterGallery';

const BeforeAfterGalleryPage: React.FC = () => {
  const { t, currentLanguage } = useLanguage();

  return (
    <ErrorBoundary level="page" showDetails={import.meta.env.DEV}>
      <SEOHead 
        lang={currentLanguage}
        title={`${t('gallery.beforeAfter.title') || 'Before & After Gallery'} | Lullaby Clinic`}
        description={t('gallery.beforeAfter.subtitle') || 'Witness the remarkable transformations achieved through our advanced treatments and expert care.'}
        keywords="lullaby clinic before after, treatment results, facial transformation, botox results, laser treatment results, medical aesthetics"
      />
      
      <div className="min-h-screen bg-gray-50">
        {/* Navigation */}
        <Navigation />

        {/* Main Content */}
        <main className="pt-20">
          <BeforeAfterGallery 
            translations={{
              beforeAfter: {
                title: t('gallery.beforeAfter.title') || 'Before & After Gallery',
                subtitle: t('gallery.beforeAfter.subtitle') || 'Witness the remarkable transformations achieved through our advanced treatments and expert care.'
              }
            }}
          />
        </main>

        {/* Footer */}
        <Footer translations={{
          footer: {
            partners: t('footer.partners'),
            description: t('footer.description'),
            address: t('footer.address'),
            phone: t('footer.phone'),
            email: t('footer.email'),
            quickLinks: t('footer.quickLinks'),
            services: t('footer.services'),
            rights: t('footer.rights'),
            socialMedia: {
              facebook: t('footer.socialMedia.facebook'),
              line: t('footer.socialMedia.line'),
              googleMaps: t('footer.socialMedia.googleMaps')
            }
          },
          nav: {
            home: t('nav.home'),
            services: t('nav.services'),
            about: t('nav.about'),
            gallery: t('nav.gallery'),
            blog: t('nav.blog')
          },
          services: {
            facial: {
              title: t('services.facial.title')
            },
            botox: {
              title: t('services.botox.title')
            },
            laser: {
              title: t('services.laser.title')
            }
          }
        }} />
      </div>
    </ErrorBoundary>
  );
};

export default BeforeAfterGalleryPage;
