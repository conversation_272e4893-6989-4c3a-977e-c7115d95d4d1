import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Al<PERSON><PERSON>riangle, RefreshCw, Home, Bug } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  level?: 'page' | 'component' | 'critical';
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

class ErrorBoundary extends Component<Props, State> {
  private retryCount: number = 0;
  private maxRetries: number = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Generate unique error ID for tracking
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      hasError: true,
      error,
      errorId
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);

    // Log to external service (replace with your error tracking service)
    this.logErrorToService(error, errorInfo);
  }

  private logErrorToService = (error: Error, errorInfo: ErrorInfo) => {
    try {
      // Example: Send to error tracking service
      const errorData = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        errorId: this.state.errorId,
        level: this.props.level || 'component'
      };

      // Replace with your actual error tracking service
      // Example: Sentry, LogRocket, or custom API
      console.log('Error logged:', errorData);
      
      // You can uncomment and configure this for production
      // fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorData)
      // });
    } catch (loggingError) {
      console.error('Failed to log error:', loggingError);
    }
  };

  private handleRetry = () => {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: ''
      });
    }
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  private getErrorMessage = (error: Error | null): string => {
    if (!error) return 'An unexpected error occurred';

    // Custom error messages for better UX
    if (error.name === 'NetworkError') {
      return 'Network connection failed. Please check your internet connection.';
    }
    
    if (error.name === 'ChunkLoadError') {
      return 'Failed to load application resources. Please refresh the page.';
    }

    if (error.message.includes('Loading chunk')) {
      return 'Application update detected. Please refresh the page.';
    }

    return error.message || 'An unexpected error occurred';
  };

  private getErrorSeverity = (): 'low' | 'medium' | 'high' => {
    const { level } = this.props;
    const { error } = this.state;

    if (level === 'critical') return 'high';
    if (level === 'page') return 'medium';
    
    // Determine severity based on error type
    if (error?.name === 'ChunkLoadError') return 'medium';
    if (error?.name === 'NetworkError') return 'high';
    
    return 'low';
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const severity = this.getErrorSeverity();
      const errorMessage = this.getErrorMessage(this.state.error);
      const canRetry = this.retryCount < this.maxRetries;

      // Critical errors - full page takeover
      if (this.props.level === 'critical' || severity === 'high') {
        return (
          <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-50 flex items-center justify-center p-4">
            <Card className="w-full max-w-lg shadow-2xl border-red-200">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <AlertTriangle className="w-8 h-8 text-red-600" />
                </div>
                <CardTitle className="text-2xl text-red-800">
                  Application Error
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center space-y-6">
                <p className="text-gray-600 leading-relaxed">
                  {errorMessage}
                </p>
                
                {this.props.showDetails && this.state.error && (
                  <details className="text-left bg-gray-50 p-4 rounded-lg">
                    <summary className="cursor-pointer font-medium text-gray-700 mb-2">
                      Technical Details
                    </summary>
                    <div className="text-sm text-gray-600 space-y-2">
                      <p><strong>Error:</strong> {this.state.error.message}</p>
                      <p><strong>Error ID:</strong> {this.state.errorId}</p>
                      {this.state.error.stack && (
                        <pre className="text-xs bg-white p-2 rounded overflow-x-auto">
                          {this.state.error.stack}
                        </pre>
                      )}
                    </div>
                  </details>
                )}

                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <Button 
                    onClick={this.handleReload}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Reload Page
                  </Button>
                  <Button 
                    onClick={this.handleGoHome}
                    variant="outline"
                    className="border-red-200 text-red-700 hover:bg-red-50"
                  >
                    <Home className="w-4 h-4 mr-2" />
                    Go Home
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        );
      }

      // Component-level errors - inline error display
      return (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 my-4">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <Bug className="w-5 h-5 text-yellow-600" />
            </div>
            <div className="flex-1">
              <h3 className="text-sm font-medium text-yellow-800">
                Component Error
              </h3>
              <p className="mt-1 text-sm text-yellow-700">
                {errorMessage}
              </p>
              
              {this.props.showDetails && (
                <details className="mt-3">
                  <summary className="cursor-pointer text-xs text-yellow-600">
                    Show Details
                  </summary>
                  <div className="mt-2 text-xs text-yellow-600">
                    <p><strong>Error ID:</strong> {this.state.errorId}</p>
                    {this.state.error?.stack && (
                      <pre className="mt-1 bg-yellow-100 p-2 rounded text-xs overflow-x-auto">
                        {this.state.error.stack.split('\n').slice(0, 5).join('\n')}
                      </pre>
                    )}
                  </div>
                </details>
              )}
              
              <div className="mt-4 flex space-x-2">
                {canRetry && (
                  <Button
                    size="sm"
                    onClick={this.handleRetry}
                    className="bg-yellow-600 hover:bg-yellow-700 text-white"
                  >
                    <RefreshCw className="w-3 h-3 mr-1" />
                    Retry ({this.maxRetries - this.retryCount} left)
                  </Button>
                )}
                <Button
                  size="sm"
                  variant="outline"
                  onClick={this.handleReload}
                  className="border-yellow-300 text-yellow-700 hover:bg-yellow-100"
                >
                  Reload
                </Button>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;

// Higher-order component for easier usage
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );
  
  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

// Hook for error boundary context
export const useErrorHandler = () => {
  return (error: Error, errorInfo?: ErrorInfo) => {
    console.error('Manual error report:', error, errorInfo);
    
    // Manual error reporting
    const errorData = {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      type: 'manual'
    };
    
    // Log to service
    console.log('Manual error logged:', errorData);
  };
};