
import React, { useState, useEffect, useMemo } from 'react';
import { Search, Filter, Calendar, Clock, User, Tag, ArrowRight, BookOpen, TrendingUp, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import LazyImage from '@/components/LazyImage';
import { cn } from '@/lib/utils';

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: string;
  publishDate: string;
  readTime: string;
  category: string;
  tags: string[];
  image: string;
  featured: boolean;
  trending: boolean;
  views: number;
  likes: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
}

interface BlogSearchProps {
  translations: {
    blogSearch: {
      searchPlaceholder: string;
      filterBy: string;
      allCategories: string;
      sortBy: string;
      newest: string;
      oldest: string;
      mostPopular: string;
      mostViewed: string;
      readTime: string;
      author: string;
      tags: string;
      featured: string;
      trending: string;
      results: string;
      noResults: string;
      noResultsDesc: string;
      clearFilters: string;
      readMore: string;
      minutes: string;
      views: string;
      likes: string;
      difficulty: {
        beginner: string;
        intermediate: string;
        advanced: string;
      };
      categoryOptions: {
        skincare: string;
        treatments: string;
        lifestyle: string;
        technology: string;
        beforeafter: string;
        tips: string;
        news: string;
      };
    };
  };
  onPostClick?: (post: BlogPost) => void;
}

const BlogSearch: React.FC<BlogSearchProps> = ({ translations, onPostClick }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState('newest');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedDifficulty, setSelectedDifficulty] = useState<string[]>([]);
  const [showFeaturedOnly, setShowFeaturedOnly] = useState(false);
  const [showTrendingOnly, setShowTrendingOnly] = useState(false);

  // Mock blog posts data
  const [blogPosts] = useState<BlogPost[]>([
    {
      id: '1',
      title: 'Complete Guide to Botox: Everything You Need to Know',
      excerpt: 'Discover the benefits, process, and aftercare for Botox treatments. Learn why it\'s one of the most popular cosmetic procedures worldwide.',
      content: 'Full article content here...',
      author: 'Dr. Sarah Johnson',
      publishDate: '2024-12-15',
      readTime: '8',
      category: 'treatments',
      tags: ['botox', 'anti-aging', 'wrinkles', 'cosmetic'],
      image: '/placeholder.svg',
      featured: true,
      trending: true,
      views: 1250,
      likes: 89,
      difficulty: 'beginner'
    },
    {
      id: '2',
      title: 'Dermal Fillers vs Botox: Which is Right for You?',
      excerpt: 'Understanding the differences between dermal fillers and Botox to make the best choice for your aesthetic goals.',
      content: 'Full article content here...',
      author: 'Dr. Michael Chen',
      publishDate: '2024-12-12',
      readTime: '6',
      category: 'treatments',
      tags: ['fillers', 'botox', 'comparison', 'anti-aging'],
      image: '/placeholder.svg',
      featured: false,
      trending: true,
      views: 980,
      likes: 67,
      difficulty: 'intermediate'
    },
    {
      id: '3',
      title: 'Post-Treatment Skincare: Essential Tips for Best Results',
      excerpt: 'Maximize your treatment results with proper post-care routines and skincare tips from our experts.',
      content: 'Full article content here...',
      author: 'Dr. Lisa Wang',
      publishDate: '2024-12-10',
      readTime: '5',
      category: 'skincare',
      tags: ['skincare', 'aftercare', 'routine', 'tips'],
      image: '/placeholder.svg',
      featured: true,
      trending: false,
      views: 756,
      likes: 45,
      difficulty: 'beginner'
    },
    {
      id: '4',
      title: 'Latest Technology in Medical Aesthetics 2024',
      excerpt: 'Explore cutting-edge technologies revolutionizing the medical aesthetics industry this year.',
      content: 'Full article content here...',
      author: 'Dr. James Wilson',
      publishDate: '2024-12-08',
      readTime: '10',
      category: 'technology',
      tags: ['technology', 'innovation', 'equipment', 'future'],
      image: '/placeholder.svg',
      featured: false,
      trending: false,
      views: 432,
      likes: 28,
      difficulty: 'advanced'
    },
    {
      id: '5',
      title: 'Healthy Lifestyle Habits for Glowing Skin',
      excerpt: 'Simple daily habits that can transform your skin naturally, complementing your aesthetic treatments.',
      content: 'Full article content here...',
      author: 'Dr. Emma Taylor',
      publishDate: '2024-12-05',
      readTime: '7',
      category: 'lifestyle',
      tags: ['lifestyle', 'skincare', 'natural', 'health'],
      image: '/placeholder.svg',
      featured: false,
      trending: false,
      views: 623,
      likes: 52,
      difficulty: 'beginner'
    },
    {
      id: '6',
      title: 'Before & After: Real Patient Transformation Stories',
      excerpt: 'Inspiring transformation stories from our patients, showcasing real results and experiences.',
      content: 'Full article content here...',
      author: 'Lullaby Clinic Team',
      publishDate: '2024-12-03',
      readTime: '12',
      category: 'beforeafter',
      tags: ['transformations', 'results', 'testimonials', 'real'],
      image: '/placeholder.svg',
      featured: true,
      trending: false,
      views: 1890,
      likes: 134,
      difficulty: 'beginner'
    }
  ]);

  // Get all unique tags
  const allTags = useMemo(() => {
    const tags = new Set<string>();
    blogPosts.forEach(post => post.tags.forEach(tag => tags.add(tag)));
    return Array.from(tags);
  }, [blogPosts]);

  // Filter and sort posts
  const filteredPosts = useMemo(() => {
    const filtered = blogPosts.filter(post => {
      const matchesSearch = searchTerm === '' || 
        post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesCategory = selectedCategory === 'all' || post.category === selectedCategory;
      
      const matchesTags = selectedTags.length === 0 || 
        selectedTags.some(tag => post.tags.includes(tag));

      const matchesDifficulty = selectedDifficulty.length === 0 || 
        selectedDifficulty.includes(post.difficulty);

      const matchesFeatured = !showFeaturedOnly || post.featured;
      const matchesTrending = !showTrendingOnly || post.trending;

      return matchesSearch && matchesCategory && matchesTags && 
             matchesDifficulty && matchesFeatured && matchesTrending;
    });

    // Sort posts
    switch (sortBy) {
      case 'oldest':
        filtered.sort((a, b) => new Date(a.publishDate).getTime() - new Date(b.publishDate).getTime());
        break;
      case 'mostPopular':
        filtered.sort((a, b) => b.likes - a.likes);
        break;
      case 'mostViewed':
        filtered.sort((a, b) => b.views - a.views);
        break;
      default: // newest
        filtered.sort((a, b) => new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime());
    }

    return filtered;
  }, [blogPosts, searchTerm, selectedCategory, selectedTags, sortBy, selectedDifficulty, showFeaturedOnly, showTrendingOnly]);

  const clearAllFilters = () => {
    setSearchTerm('');
    setSelectedCategory('all');
    setSelectedTags([]);
    setSortBy('newest');
    setSelectedDifficulty([]);
    setShowFeaturedOnly(false);
    setShowTrendingOnly(false);
  };

  const toggleTag = (tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const toggleDifficulty = (difficulty: string) => {
    setSelectedDifficulty(prev => 
      prev.includes(difficulty)
        ? prev.filter(d => d !== difficulty)
        : [...prev, difficulty]
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-8">
      {/* Search Header */}
      <div className="text-center space-y-4">
        <h2 className="text-3xl md:text-4xl font-bold text-foreground">
          <BookOpen className="inline-block h-8 w-8 mr-3 text-primary-600" />
          Blog & Articles
        </h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Discover expert insights, treatment guides, and the latest trends in medical aesthetics
        </p>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-xl shadow-soft p-6 space-y-4">
        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
          <Input
            placeholder={translations.blogSearch.searchPlaceholder}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 h-12 text-lg"
          />
        </div>

        {/* Quick Filters */}
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex items-center gap-2">
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder={translations.blogSearch.allCategories} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{translations.blogSearch.allCategories}</SelectItem>
                <SelectItem value="treatments">{translations.blogSearch.categoryOptions.treatments}</SelectItem>
                <SelectItem value="skincare">{translations.blogSearch.categoryOptions.skincare}</SelectItem>
                <SelectItem value="lifestyle">{translations.blogSearch.categoryOptions.lifestyle}</SelectItem>
                <SelectItem value="technology">{translations.blogSearch.categoryOptions.technology}</SelectItem>
                <SelectItem value="beforeafter">{translations.blogSearch.categoryOptions.beforeafter}</SelectItem>
                <SelectItem value="tips">{translations.blogSearch.categoryOptions.tips}</SelectItem>
                <SelectItem value="news">{translations.blogSearch.categoryOptions.news}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2">
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">{translations.blogSearch.newest}</SelectItem>
                <SelectItem value="oldest">{translations.blogSearch.oldest}</SelectItem>
                <SelectItem value="mostPopular">{translations.blogSearch.mostPopular}</SelectItem>
                <SelectItem value="mostViewed">{translations.blogSearch.mostViewed}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="gap-2"
          >
            <Filter className="h-4 w-4" />
            {translations.blogSearch.filterBy}
          </Button>

          {(selectedTags.length > 0 || selectedDifficulty.length > 0 || showFeaturedOnly || showTrendingOnly || selectedCategory !== 'all') && (
            <Button variant="ghost" onClick={clearAllFilters} className="text-primary-600">
              {translations.blogSearch.clearFilters}
            </Button>
          )}
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="border-t pt-4 space-y-4">
            {/* Tags Filter */}
            <div>
              <h4 className="font-medium text-foreground mb-2">{translations.blogSearch.tags}</h4>
              <div className="flex flex-wrap gap-2">
                {allTags.map(tag => (
                  <Badge
                    key={tag}
                    variant={selectedTags.includes(tag) ? "default" : "outline"}
                    className="cursor-pointer hover:bg-primary-100"
                    onClick={() => toggleTag(tag)}
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Difficulty Filter */}
            <div>
              <h4 className="font-medium text-foreground mb-2">Difficulty Level</h4>
              <div className="flex gap-4">
                {(['beginner', 'intermediate', 'advanced'] as const).map(difficulty => (
                  <div key={difficulty} className="flex items-center space-x-2">
                    <Checkbox
                      id={difficulty}
                      checked={selectedDifficulty.includes(difficulty)}
                      onCheckedChange={(checked) => {
                        if (checked === true) {
                          toggleDifficulty(difficulty);
                        } else if (checked === false) {
                          toggleDifficulty(difficulty);
                        }
                      }}
                    />
                    <label htmlFor={difficulty} className="text-sm cursor-pointer">
                      {translations.blogSearch.difficulty[difficulty]}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            {/* Special Filters */}
            <div className="flex gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="featured"
                  checked={showFeaturedOnly}
                  onCheckedChange={(checked) => {
                    if (checked === true) {
                      setShowFeaturedOnly(true);
                    } else if (checked === false) {
                      setShowFeaturedOnly(false);
                    }
                  }}
                />
                <label htmlFor="featured" className="text-sm cursor-pointer flex items-center gap-1">
                  <Star className="h-3 w-3" />
                  {translations.blogSearch.featured}
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="trending"
                  checked={showTrendingOnly}
                  onCheckedChange={(checked) => {
                    if (checked === true) {
                      setShowTrendingOnly(true);
                    } else if (checked === false) {
                      setShowTrendingOnly(false);
                    }
                  }}
                />
                <label htmlFor="trending" className="text-sm cursor-pointer flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  {translations.blogSearch.trending}
                </label>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Results Header */}
      <div className="flex items-center justify-between">
        <div className="text-muted-foreground">
          {translations.blogSearch.results}: <span className="font-semibold text-foreground">{filteredPosts.length}</span>
        </div>
        
        {/* Active Filters */}
        {(selectedTags.length > 0 || selectedCategory !== 'all') && (
          <div className="flex items-center gap-2 text-sm">
            <span className="text-muted-foreground">Active filters:</span>
            {selectedCategory !== 'all' && (
              <Badge variant="secondary">{selectedCategory}</Badge>
            )}
            {selectedTags.map(tag => (
              <Badge key={tag} variant="secondary">{tag}</Badge>
            ))}
          </div>
        )}
      </div>

      {/* Results */}
      {filteredPosts.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredPosts.map((post) => (
            <Card 
              key={post.id} 
              className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 cursor-pointer overflow-hidden"
              onClick={() => onPostClick?.(post)}
            >
              <div className="relative aspect-video overflow-hidden">
                <LazyImage
                  src={post.image}
                  alt={post.title}
                  className="w-full h-full group-hover:scale-105 transition-transform duration-300"
                  objectFit="cover"
                />
                
                {/* Badges */}
                <div className="absolute top-4 left-4 flex gap-2">
                  {post.featured && (
                    <Badge className="bg-yellow-500 text-white">
                      <Star className="h-3 w-3 mr-1" />
                      {translations.blogSearch.featured}
                    </Badge>
                  )}
                  {post.trending && (
                    <Badge className="bg-red-500 text-white">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      {translations.blogSearch.trending}
                    </Badge>
                  )}
                </div>

                <div className="absolute top-4 right-4">
                  <Badge className={getDifficultyColor(post.difficulty)}>
                    {translations.blogSearch.difficulty[post.difficulty]}
                  </Badge>
                </div>

                <div className="absolute bottom-4 left-4">
                  <Badge variant="secondary" className="bg-primary-600 text-white">
                    {translations.blogSearch.categoryOptions[post.category as keyof typeof translations.blogSearch.categoryOptions]}
                  </Badge>
                </div>
              </div>

              <CardHeader>
                <div className="flex items-center gap-4 text-sm text-muted-foreground mb-3">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    {formatDate(post.publishDate)}
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    {post.readTime} {translations.blogSearch.minutes}
                  </div>
                </div>

                <h3 className="text-xl font-semibold text-foreground group-hover:text-primary-600 transition-colors leading-tight line-clamp-2">
                  {post.title}
                </h3>
              </CardHeader>

              <CardContent>
                <div className="space-y-4">
                  <p className="text-muted-foreground leading-relaxed line-clamp-3">
                    {post.excerpt}
                  </p>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-1">
                    {post.tags.slice(0, 3).map(tag => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        <Tag className="h-2 w-2 mr-1" />
                        {tag}
                      </Badge>
                    ))}
                    {post.tags.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{post.tags.length - 3}
                      </Badge>
                    )}
                  </div>

                  {/* Stats and CTA */}
                  <div className="flex items-center justify-between pt-2 border-t">
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {post.author}
                      </span>
                      <span>{post.views} {translations.blogSearch.views}</span>
                      <span>{post.likes} {translations.blogSearch.likes}</span>
                    </div>

                    <Button variant="ghost" size="sm" className="text-primary-600 hover:text-primary-700 group/btn">
                      {translations.blogSearch.readMore}
                      <ArrowRight className="h-4 w-4 ml-1 group-hover/btn:translate-x-1 transition-transform" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-16">
          <BookOpen className="h-24 w-24 text-muted-foreground mx-auto mb-4 opacity-50" />
          <h3 className="text-2xl font-semibold text-foreground mb-2">
            {translations.blogSearch.noResults}
          </h3>
          <p className="text-muted-foreground mb-6 max-w-md mx-auto">
            {translations.blogSearch.noResultsDesc}
          </p>
          <Button onClick={clearAllFilters} variant="outline">
            {translations.blogSearch.clearFilters}
          </Button>
        </div>
      )}
    </div>
  );
};

export default BlogSearch;
