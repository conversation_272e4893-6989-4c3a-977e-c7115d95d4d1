
import React from 'react';
import { Phone, Mail, MapPin, Facebook, Instagram, Youtube, MessageCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface FooterProps {
  translations: {
    footer: {
      partners: string;
      description: string;
      address: string;
      phone: string;
      email: string;
      quickLinks: string;
      services: string;
      rights: string;
      socialMedia: {
        facebook: string;
        line: string;
        googleMaps: string;
      };
    };
    nav: {
      home: string;
      services: string;
      about: string;
      gallery: string;
      blog: string;
    };
    services: {
      facial: {
        title: string;
      };
      botox: {
        title: string;
      };
      laser: {
        title: string;
      };
    };
  };
}

const Footer = ({ translations }: FooterProps) => {
  const brandLogos = [
    { name: '<PERSON>ergan', logo: '/placeholder.svg' },
    { name: '<PERSON><PERSON>er<PERSON>', logo: '/placeholder.svg' },
    { name: '<PERSON><PERSON>', logo: '/placeholder.svg' },
    { name: '<PERSON><PERSON><PERSON>', logo: '/placeholder.svg' },
    { name: '<PERSON><PERSON><PERSON><PERSON>', logo: '/placeholder.svg' }
  ];

  return (
    <footer className="bg-foreground text-white">
      {/* Brand Partners */}
      <div className="border-b border-white/10">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center mb-8">
            <h3 className="text-lg font-semibold mb-4">{translations.footer.partners}</h3>
          </div>
          <div className="flex flex-wrap justify-center items-center gap-8 opacity-70">
            {brandLogos.map((brand, index) => (
              <div key={index} className="grayscale hover:grayscale-0 transition-all duration-300">
                <img 
                  src={brand.logo}
                  alt={brand.name}
                  className="h-12 w-auto"
                />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Footer */}
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-primary-400 to-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">L</span>
              </div>
              <span className="text-xl font-bold">Lullaby Clinic</span>
            </div>
            <p className="text-white/70 mb-6 max-w-md leading-relaxed">
              {translations.footer.description}
            </p>
            
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-primary-400" />
                <span className="text-white/90">{translations.footer.phone}</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-primary-400" />
                <span className="text-white/90">{translations.footer.email}</span>
              </div>
              <div className="flex items-center space-x-3">
                <MapPin className="h-5 w-5 text-primary-400" />
                <span className="text-white/90">{translations.footer.address}</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">{translations.footer.quickLinks}</h3>
            <ul className="space-y-3">
              <li><a href="#home" className="text-white/70 hover:text-primary-400 transition-colors">{translations.nav.home}</a></li>
              <li><a href="#services" className="text-white/70 hover:text-primary-400 transition-colors">{translations.nav.services}</a></li>
              <li><a href="#about" className="text-white/70 hover:text-primary-400 transition-colors">{translations.nav.about}</a></li>
              <li><a href="#gallery" className="text-white/70 hover:text-primary-400 transition-colors">{translations.nav.gallery}</a></li>
              <li><a href="#blog" className="text-white/70 hover:text-primary-400 transition-colors">{translations.nav.blog}</a></li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-semibold mb-4">{translations.footer.services}</h3>
            <ul className="space-y-3">
              <li><a href="#" className="text-white/70 hover:text-primary-400 transition-colors">{translations.services.facial.title}</a></li>
              <li><a href="#" className="text-white/70 hover:text-primary-400 transition-colors">{translations.services.botox.title}</a></li>
              <li><a href="#" className="text-white/70 hover:text-primary-400 transition-colors">{translations.services.laser.title}</a></li>
              <li><a href="#" className="text-white/70 hover:text-primary-400 transition-colors">Dermal Fillers</a></li>
              <li><a href="#" className="text-white/70 hover:text-primary-400 transition-colors">Consultation</a></li>
            </ul>
          </div>
        </div>

        {/* Social Media & Newsletter */}
        <div className="border-t border-white/10 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex space-x-4">
              <Button 
                variant="ghost" 
                size="sm" 
                className="text-white/70 hover:text-primary-400 hover:bg-white/10"
                onClick={() => window.open(translations.footer.socialMedia.facebook, '_blank')}
              >
                <Facebook className="h-5 w-5" />
              </Button>
              <Button 
                variant="ghost" 
                size="sm" 
                className="text-white/70 hover:text-primary-400 hover:bg-white/10"
                onClick={() => window.open(`https://line.me/ti/p/${translations.footer.socialMedia.line}`, '_blank')}
              >
                <MessageCircle className="h-5 w-5" />
              </Button>
              <Button 
                variant="ghost" 
                size="sm" 
                className="text-white/70 hover:text-primary-400 hover:bg-white/10"
                onClick={() => window.open(translations.footer.socialMedia.googleMaps, '_blank')}
              >
                <MapPin className="h-5 w-5" />
              </Button>
            </div>
            
            <div className="text-center md:text-right">
              <p className="text-white/60 text-sm">
                © 2024 Lullaby Clinic. {translations.footer.rights}
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
