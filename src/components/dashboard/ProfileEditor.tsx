
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Edit2, Save, X } from 'lucide-react';

interface UserProfile {
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  address?: string;
}

interface ProfileEditorProps {
  profile?: UserProfile;
  isEditing: boolean;
  editedProfile: UserProfile;
  onEdit: () => void;
  onSave: () => void;
  onCancel: () => void;
  onProfileChange: (field: keyof UserProfile, value: string) => void;
}

const ProfileEditor: React.FC<ProfileEditorProps> = ({
  profile,
  isEditing,
  editedProfile,
  onEdit,
  onSave,
  onCancel,
  onProfileChange
}) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Personal Information</CardTitle>
        {!isEditing ? (
          <Button variant="outline" size="sm" onClick={onEdit}>
            <Edit2 className="h-4 w-4 mr-2" />
            Edit
          </Button>
        ) : (
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={onCancel}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button size="sm" onClick={onSave}>
              <Save className="h-4 w-4 mr-2" />
              Save
            </Button>
          </div>
        )}
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
            {isEditing ? (
              <Input
                value={editedProfile.first_name || ''}
                onChange={(e) => onProfileChange('first_name', e.target.value)}
              />
            ) : (
              <p className="text-gray-900">{profile?.first_name || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
            {isEditing ? (
              <Input
                value={editedProfile.last_name || ''}
                onChange={(e) => onProfileChange('last_name', e.target.value)}
              />
            ) : (
              <p className="text-gray-900">{profile?.last_name || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
            {isEditing ? (
              <Input
                type="email"
                value={editedProfile.email || ''}
                onChange={(e) => onProfileChange('email', e.target.value)}
              />
            ) : (
              <p className="text-gray-900">{profile?.email || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
            {isEditing ? (
              <Input
                type="tel"
                value={editedProfile.phone || ''}
                onChange={(e) => onProfileChange('phone', e.target.value)}
              />
            ) : (
              <p className="text-gray-900">{profile?.phone || 'Not provided'}</p>
            )}
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
          {isEditing ? (
            <Textarea
              value={editedProfile.address || ''}
              onChange={(e) => onProfileChange('address', e.target.value)}
              rows={3}
            />
          ) : (
            <p className="text-gray-900">{profile?.address || 'Not provided'}</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfileEditor;
