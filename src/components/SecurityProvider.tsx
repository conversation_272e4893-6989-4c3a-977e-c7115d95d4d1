/**
 * Lullaby Clinic - Security Context Provider
 * Global security and compliance management
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useAuth } from '@/hooks/useSupabase';
import SecurityComplianceService from '@/lib/security-compliance';
import { useToast } from '@/hooks/use-toast';
import { useLanguage } from '@/contexts/LanguageContext';

interface SecurityContextType {
  sessionValid: boolean;
  timeRemaining: number;
  shouldRefresh: boolean;
  userRole: string | null;
  hasPermission: (requiredRole: string | string[]) => boolean;
  logSecurityEvent: (action: string, details: Record<string, unknown>) => Promise<void>;
  validatePasswordStrength: (password: string) => {
    isValid: boolean;
    score: number;
    requirements: {
      length: boolean;
      uppercase: boolean;
      lowercase: boolean;
      numbers: boolean;
      symbols: boolean;
      noCommonWords: boolean;
    };
  };
  checkRateLimit: (identifier: string) => boolean;
  resetRateLimit: (identifier: string) => void;
  refreshSession: () => Promise<void>;
}

const SecurityContext = createContext<SecurityContextType | undefined>(undefined);

interface SecurityProviderProps {
  children: React.ReactNode;
}

export const SecurityProvider: React.FC<SecurityProviderProps> = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const { toast } = useToast();
  const { t } = useLanguage();
  
  const [sessionValid, setSessionValid] = useState(true);
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [shouldRefresh, setShouldRefresh] = useState(false);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [sessionWarningShown, setSessionWarningShown] = useState(false);

  // Log security events
  const logSecurityEvent = useCallback(async (action: string, details: Record<string, unknown>) => {
    try {
      await SecurityComplianceService.logSecurityEvent(
        action,
        'security_events',
        user?.id || 'anonymous',
        null,
        { action, details, timestamp: new Date().toISOString() }
      );
    } catch (error) {
      console.error('Failed to log security event:', error);
    }
  }, [user?.id]);

  // Refresh session
  const refreshSession = useCallback(async () => {
    try {
      // In a real implementation, this would refresh the auth token
      console.log('Refreshing session...');
      setSessionWarningShown(false);
      
      // Log session refresh
      await logSecurityEvent('session_refresh', { 
        user_id: user?.id,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Failed to refresh session:', error);
      toast({
        title: t('security.sessionRefreshFailed'),
        description: t('security.sessionRefreshFailedDesc'),
        variant: 'destructive'
      });
    }
  }, [user?.id, logSecurityEvent, toast, t]);

  // Check session validity
  const checkSession = useCallback(async () => {
    if (!isAuthenticated) {
      setSessionValid(false);
      setTimeRemaining(0);
      setShouldRefresh(false);
      return;
    }

    try {
      const sessionCheck = await SecurityComplianceService.validateSession();
      setSessionValid(sessionCheck.isValid);
      setTimeRemaining(sessionCheck.timeRemaining || 0);
      setShouldRefresh(sessionCheck.shouldRefresh || false);

      // Show warning when session is about to expire
      if (sessionCheck.timeRemaining && sessionCheck.timeRemaining < 5 * 60 * 1000 && !sessionWarningShown) {
        setSessionWarningShown(true);
        toast({
          title: t('security.sessionExpiring'),
          description: t('security.sessionExpiringDesc'),
          variant: 'destructive'
        });
      }

      // Auto-refresh session if needed
      if (sessionCheck.shouldRefresh) {
        await refreshSession();
      }
    } catch (error) {
      console.error('Session check failed:', error);
      setSessionValid(false);
    }
  }, [isAuthenticated, sessionWarningShown, toast, t, refreshSession]);

  // Get user role
  const getUserRole = useCallback(async () => {
    if (!user?.id) {
      setUserRole(null);
      return;
    }

    try {
      const roleCheck = await SecurityComplianceService.validateUserRole(['patient', 'doctor', 'admin', 'staff'], user.id);
      setUserRole(roleCheck.userRole || null);
    } catch (error) {
      console.error('Failed to get user role:', error);
      setUserRole(null);
    }
  }, [user?.id]);

  // Check user permissions
  const hasPermission = useCallback((requiredRole: string | string[]): boolean => {
    if (!userRole) return false;
    
    const allowedRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
    return allowedRoles.includes(userRole);
  }, [userRole]);

  // Password strength validation
  const validatePasswordStrength = useCallback((password: string) => {
    return SecurityComplianceService.validatePasswordStrength(password);
  }, []);

  // Rate limiting
  const checkRateLimit = useCallback((identifier: string): boolean => {
    return SecurityComplianceService.rateLimiter.checkRateLimit(identifier);
  }, []);

  const resetRateLimit = useCallback((identifier: string): void => {
    SecurityComplianceService.rateLimiter.resetRateLimit(identifier);
  }, []);

  // Monitor for suspicious activity
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // User switched away from tab
        logSecurityEvent('tab_hidden', { timestamp: new Date().toISOString() });
      } else {
        // User returned to tab
        logSecurityEvent('tab_visible', { timestamp: new Date().toISOString() });
        checkSession(); // Recheck session when user returns
      }
    };

    const handleBeforeUnload = () => {
      logSecurityEvent('page_unload', { timestamp: new Date().toISOString() });
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [logSecurityEvent, checkSession]);

  // Session monitoring interval
  useEffect(() => {
    if (!isAuthenticated) return;

    const interval = setInterval(checkSession, 30000); // Check every 30 seconds
    checkSession(); // Initial check

    return () => clearInterval(interval);
  }, [isAuthenticated, checkSession]);

  // Get user role when user changes
  useEffect(() => {
    getUserRole();
  }, [getUserRole]);

  // Log security events for authentication changes
  useEffect(() => {
    if (isAuthenticated && user) {
      logSecurityEvent('user_authenticated', {
        user_id: user.id,
        timestamp: new Date().toISOString()
      });
    } else if (!isAuthenticated) {
      logSecurityEvent('user_signed_out', {
        timestamp: new Date().toISOString()
      });
    }
  }, [isAuthenticated, user, logSecurityEvent]);

  // Auto-logout on session expiry
  useEffect(() => {
    if (isAuthenticated && !sessionValid) {
      toast({
        title: t('security.sessionExpired'),
        description: t('security.sessionExpiredDesc'),
        variant: 'destructive'
      });
      
      // Redirect to login
      setTimeout(() => {
        window.location.href = '/auth/login';
      }, 3000);
    }
  }, [isAuthenticated, sessionValid, toast, t]);

  const contextValue: SecurityContextType = {
    sessionValid,
    timeRemaining,
    shouldRefresh,
    userRole,
    hasPermission,
    logSecurityEvent,
    validatePasswordStrength,
    checkRateLimit,
    resetRateLimit,
    refreshSession
  };

  return (
    <SecurityContext.Provider value={contextValue}>
      {children}
    </SecurityContext.Provider>
  );
};

// Hook to use security context
export const useSecurity = (): SecurityContextType => {
  const context = useContext(SecurityContext);
  if (!context) {
    throw new Error('useSecurity must be used within a SecurityProvider');
  }
  return context;
};

export default SecurityProvider;