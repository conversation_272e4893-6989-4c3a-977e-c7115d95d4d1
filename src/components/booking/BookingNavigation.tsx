
import React from 'react';
import { Button } from '@/components/ui/button';

interface BookingNavigationProps {
  currentStep: number;
  totalSteps: number;
  canProceed: boolean;
  isLoading: boolean;
  onPrevious: () => void;
  onNext: () => void;
  onConfirm: () => void;
}

const BookingNavigation: React.FC<BookingNavigationProps> = ({
  currentStep,
  totalSteps,
  canProceed,
  isLoading,
  onPrevious,
  onNext,
  onConfirm
}) => {
  const isLastStep = currentStep === totalSteps - 1;

  return (
    <div className="flex justify-between">
      <Button 
        variant="outline" 
        onClick={onPrevious}
        disabled={currentStep === 0}
      >
        Previous
      </Button>
      
      {!isLastStep ? (
        <Button 
          onClick={onNext}
          disabled={!canProceed}
        >
          Next
        </Button>
      ) : (
        <Button 
          onClick={onConfirm}
          disabled={isLoading}
        >
          {isLoading ? 'Booking...' : 'Confirm Booking'}
        </Button>
      )}
    </div>
  );
};

export default BookingNavigation;
