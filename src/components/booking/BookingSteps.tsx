
import React from 'react';
import { BookingStep } from '@/types';

interface BookingStepsProps {
  steps: BookingStep[];
  currentStep: number;
}

const BookingSteps: React.FC<BookingStepsProps> = ({ steps, currentStep }) => {
  return (
    <div className="flex justify-between items-center">
      {steps.map((step, index) => (
        <div key={step.id} className="flex items-center">
          <div className={`
            w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
            ${step.isCompleted ? 'bg-primary text-primary-foreground' : 
              step.isActive ? 'bg-primary/20 text-primary' : 'bg-muted text-muted-foreground'}
          `}>
            {index + 1}
          </div>
          <div className="ml-2">
            <p className="text-sm font-medium">{step.title}</p>
            <p className="text-xs text-muted-foreground">{step.description}</p>
          </div>
          {index < steps.length - 1 && (
            <div className="mx-4 h-px bg-border flex-1" />
          )}
        </div>
      ))}
    </div>
  );
};

export default BookingSteps;
