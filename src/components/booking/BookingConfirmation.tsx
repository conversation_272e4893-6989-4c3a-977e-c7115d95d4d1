
import React from 'react';

interface Service {
  id: string;
  name: string;
}

interface BookingConfirmationProps {
  services: Service[];
  selectedService: string;
  selectedDoctor: string;
  selectedDate: string;
  patientNotes: string;
  onNotesChange: (notes: string) => void;
}

const BookingConfirmation: React.FC<BookingConfirmationProps> = ({
  services,
  selectedService,
  selectedDoctor,
  selectedDate,
  patientNotes,
  onNotesChange
}) => {
  return (
    <div className="space-y-4">
      <h3 className="font-medium">Confirm Your Booking</h3>
      <div className="space-y-2">
        <p><strong>Service:</strong> {services.find(s => s.id === selectedService)?.name}</p>
        <p><strong>Doctor:</strong> {selectedDoctor || 'To be assigned'}</p>
        <p><strong>Date:</strong> {selectedDate || 'To be scheduled'}</p>
      </div>
      <div className="space-y-2">
        <label className="text-sm font-medium">Additional Notes</label>
        <textarea
          value={patientNotes}
          onChange={(e) => onNotesChange(e.target.value)}
          className="w-full p-3 border rounded-md"
          rows={3}
          placeholder="Any special requests or medical information..."
        />
      </div>
    </div>
  );
};

export default BookingConfirmation;
