
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';

interface Service {
  id: string;
  name: string;
  short_description: string;
  base_price: number;
}

interface ServiceSelectionProps {
  services: Service[];
  selectedService: string;
  onServiceSelect: (serviceId: string) => void;
}

const ServiceSelection: React.FC<ServiceSelectionProps> = ({
  services,
  selectedService,
  onServiceSelect
}) => {
  return (
    <div className="space-y-4">
      <h3 className="font-medium">Select a Service</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {services.map((service) => (
          <Card 
            key={service.id} 
            className={`cursor-pointer transition-colors ${
              selectedService === service.id ? 'border-primary' : ''
            }`}
            onClick={() => onServiceSelect(service.id)}
          >
            <CardContent className="p-4">
              <h4 className="font-medium">{service.name}</h4>
              <p className="text-sm text-muted-foreground mt-1">
                {service.short_description}
              </p>
              <p className="text-sm font-medium mt-2">
                ฿{service.base_price.toLocaleString()}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default ServiceSelection;
