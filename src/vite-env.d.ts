
/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_SUPABASE_URL: string;
  readonly VITE_SUPABASE_ANON_KEY: string;
  readonly VITE_EMAIL_SERVICE_CONFIGURED?: string;
  readonly VITE_SENDGRID_API_KEY?: string;
  readonly VITE_AWS_SES_REGION?: string;
  readonly VITE_EMAIL_PROVIDER?: string;
  // Backend Integration (Optional)
  readonly VITE_PAYLOAD_API_URL?: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
