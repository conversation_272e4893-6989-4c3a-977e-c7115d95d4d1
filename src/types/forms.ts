
/**
 * Lullaby Clinic - Form Types
 * Types for form handling and validation
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: unknown) => string | null;
}

export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'tel' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'date' | 'time';
  placeholder?: string;
  options?: { value: string; label: string }[];
  validation?: ValidationRule;
  required?: boolean;
}

export interface FormErrors {
  [fieldName: string]: string;
}
