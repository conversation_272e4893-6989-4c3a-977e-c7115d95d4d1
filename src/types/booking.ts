
export interface BookingSlot {
  id: string;
  doctor_id: string;
  start_time: string;
  end_time: string;
  is_available: boolean;
}

export interface BookingStep {
  id: string;
  title: string;
  description?: string;
  isCompleted: boolean;
  isActive: boolean;
}

export interface CreateAppointmentInput {
  service_id: string;
  doctor_id: string;
  appointment_date: string;
  duration_minutes: number;
  total_amount: number;
  deposit_amount: number;
  patient_notes?: string;
}

export interface AppointmentWithDetails {
  id: string;
  patient_id: string;
  doctor_id: string;
  service_id: string;
  appointment_date: string;
  duration_minutes: number;
  status: 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show';
  total_amount: number;
  deposit_amount?: number;
  patient_notes?: string;
  doctor_notes?: string;
  after_photos?: string[];
  before_photos?: string[];
  cancellation_reason?: string;
  cancelled_at?: string;
  checked_in_at?: string;
  completed_at?: string;
  confirmation_sent_at?: string;
  reminder_sent_at?: string;
  followup_date?: string;
  next_appointment_recommended?: boolean;
  prescription?: string;
  slot_id?: string;
  treatment_plan?: string;
  created_at: string;
  updated_at: string;
  service: {
    id: string;
    name: string;
    description: string;
  };
  doctor: {
    id: string;
    specialization: string;
    qualification: string;
  };
  payment?: {
    id: string;
    amount: number;
    payment_status: string;
    payment_method: string;
    payment_date?: string;
  };
}
