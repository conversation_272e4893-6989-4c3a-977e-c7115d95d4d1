
export type NotificationChannel = 'email' | 'sms' | 'push' | 'in_app';

export type NotificationStatus = 'pending' | 'sent' | 'delivered' | 'failed' | 'read';

export interface Notification {
  id: string;
  recipient_id: string;
  type: string;
  channel: NotificationChannel;
  subject?: string;
  content: string;
  metadata?: Record<string, unknown>;
  delivery_status: NotificationStatus;
  sent_at?: string;
  error_message?: string;
  appointment_id?: string;
  template_id?: string;
  created_at: string;
}

export interface NotificationTemplate {
  id: string;
  name: string;
  subject: Record<string, string>;
  html_content: Record<string, string>;
  text_content?: Record<string, string>;
  category: string;
  variables: string[];
  is_active: boolean;
  usage_count: number;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export interface EmailNotificationConfig {
  enabled: boolean;
  configured: boolean;
  provider: string;
  settings?: Record<string, unknown>;
}
