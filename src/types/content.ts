
/**
 * Lullaby Clinic - Content Management Types
 * Types for CMS and content management
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

export interface ContentBlock {
  id: string;
  type: 'text' | 'image' | 'video' | 'gallery' | 'cta' | 'testimonial';
  content: unknown;
  settings?: Record<string, unknown>;
}

export interface Page {
  id: string;
  slug: string;
  title: string;
  description?: string;
  content: ContentBlock[];
  seo: import('./seo').SeoMeta;
  published: boolean;
  publishedAt?: string;
  updatedAt: string;
}
