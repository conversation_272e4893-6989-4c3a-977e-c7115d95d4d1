
/**
 * Lullaby Clinic - Common Type Definitions
 * Shared types used across the application
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

// User roles
export type UserRole = 'patient' | 'doctor' | 'admin' | 'staff';

// Admin roles
export type AdminRole = 'admin' | 'staff' | 'super_admin';

// Audit action types
export type AuditAction = 'CREATE' | 'UPDATE' | 'DELETE' | 'LOGIN' | 'LOGOUT' | 'VIEW' | 'EXPORT' | 'ERROR';

// Common status types
export type Status = 'active' | 'inactive' | 'pending' | 'suspended';

// Language codes
export type LanguageCode = 'th' | 'en' | 'zh';

// Priority levels
export type Priority = 'low' | 'medium' | 'high' | 'urgent';

// Generic API response
export interface ApiResponse<T = unknown> {
  data: T | null;
  error: string | null;
  success: boolean;
  message?: string;
}

// Pagination params
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Pagination response
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

// Search params
export interface SearchParams {
  query: string;
  filters?: Record<string, unknown>;
  pagination?: PaginationParams;
}

// File upload response
export interface FileUploadResponse {
  url: string;
  filename: string;
  size: number;
  type: string;
}

// Date range
export interface DateRange {
  startDate: string;
  endDate: string;
}

// Geographic location
export interface Location {
  country: string;
  city?: string;
  address?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

// Contact information
export interface ContactInfo {
  email: string;
  phone?: string;
  address?: string;
  website?: string;
  socialMedia?: {
    facebook?: string;
    instagram?: string;
    line?: string;
  };
}

// Operating hours
export interface OperatingHours {
  [key: string]: {
    open: string;
    close: string;
    isClosed: boolean;
  };
}

// Currency and pricing
export interface Price {
  amount: number;
  currency: string;
  displayText?: string;
}

// Translation object
export interface Translation {
  th: string;
  en: string;
  zh?: string;
}

// Rich text content
export interface RichTextContent {
  type: 'text' | 'html' | 'markdown';
  content: string;
}

// Meta information for SEO
export interface MetaInfo {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
}

// Audit trail
export interface AuditTrail {
  action: AuditAction;
  timestamp: string;
  userId?: string;
  details?: Record<string, unknown>;
  ipAddress?: string;
  userAgent?: string;
}

// Error details
export interface ErrorDetail {
  code: string;
  message: string;
  field?: string;
  details?: Record<string, unknown>;
}

// Loading state
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

// Form validation result
export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

// Generic key-value pair
export interface KeyValuePair<T = string> {
  key: string;
  value: T;
  label?: string;
}
