
/**
 * Lullaby Clinic - Analytics Types
 * Types for analytics tracking and user session management
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

export interface AnalyticsEvent {
  event: string;
  category: string;
  action: string;
  label?: string;
  value?: number;
  userId?: string;
}

export interface UserSession {
  sessionId: string;
  userId?: string;
  startTime: Date;
  endTime?: Date;
  pageViews: string[];
  events: AnalyticsEvent[];
}
