
/**
 * Lullaby Clinic - Search and Filter Types
 * Types for search functionality and filtering
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

export interface SearchFilters {
  query?: string;
  category?: string;
  difficulty?: string;
  priceRange?: [number, number];
  rating?: number;
  availability?: boolean;
}

export interface SortOption {
  label: string;
  value: string;
  direction: 'asc' | 'desc';
}
