
export interface DashboardStats {
  totalPatients: number;
  totalUsers: number;
  totalAppointments: number;
  totalRevenue: number;
  activePromotions: number;
  monthlyRevenue: number[];
  appointmentsByStatus: Record<string, number>;
  topServices: Array<{ name: string; count: number }>;
  recentActivity: Array<{
    id: string;
    type: string;
    description: string;
    timestamp: string;
  }>;
}

export interface AdminUserInput {
  email: string;
  role: 'admin' | 'staff' | 'super_admin';
  permissions?: Record<string, boolean>;
  department?: string;
  employee_id?: string;
}

export interface AdminProfileInput {
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  bio?: string;
  preferences?: Record<string, unknown>;
}

export interface AdminProfile {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  bio?: string;
  avatar_url?: string;
  preferences: Record<string, unknown>;
  created_at: string;
  updated_at: string;
}

export interface AdminUser {
  id: string;
  role: 'admin' | 'staff' | 'super_admin';
  permissions: Record<string, boolean>;
  department?: string;
  employee_id?: string;
  hire_date?: string;
  is_active: boolean;
  last_login_at?: string;
  created_at: string;
  updated_at: string;
}
