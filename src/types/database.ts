/**
 * Lullaby Clinic - Database Types
 * Generated from Supabase schema for complete type safety
 * 
 * @version 2.0.0
 * @generated 2024-12-19
 */

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      appointment_slots: {
        Row: {
          created_at: string | null
          doctor_id: string | null
          end_time: string
          id: string
          is_available: boolean | null
          recurring_rule: Json | null
          start_time: string
        }
        Insert: {
          created_at?: string | null
          doctor_id?: string | null
          end_time: string
          id?: string
          is_available?: boolean | null
          recurring_rule?: Json | null
          start_time: string
        }
        Update: {
          created_at?: string | null
          doctor_id?: string | null
          end_time?: string
          id?: string
          is_available?: boolean | null
          recurring_rule?: Json | null
          start_time?: string
        }
        Relationships: [
          {
            foreignKeyName: "appointment_slots_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "doctors"
            referencedColumns: ["id"]
          },
        ]
      }
      appointments: {
        Row: {
          after_photos: string[] | null
          appointment_date: string
          before_photos: string[] | null
          cancellation_reason: string | null
          cancelled_at: string | null
          checked_in_at: string | null
          completed_at: string | null
          confirmation_sent_at: string | null
          created_at: string | null
          deposit_amount: number | null
          doctor_id: string | null
          doctor_notes: string | null
          duration_minutes: number
          followup_date: string | null
          id: string
          next_appointment_recommended: boolean | null
          patient_id: string | null
          patient_notes: string | null
          prescription: string | null
          reminder_sent_at: string | null
          service_id: string | null
          slot_id: string | null
          status: Database["public"]["Enums"]["appointment_status"] | null
          total_amount: number
          treatment_plan: string | null
          updated_at: string | null
        }
        Insert: {
          after_photos?: string[] | null
          appointment_date: string
          before_photos?: string[] | null
          cancellation_reason?: string | null
          cancelled_at?: string | null
          checked_in_at?: string | null
          completed_at?: string | null
          confirmation_sent_at?: string | null
          created_at?: string | null
          deposit_amount?: number | null
          doctor_id?: string | null
          doctor_notes?: string | null
          duration_minutes?: number
          followup_date?: string | null
          id?: string
          next_appointment_recommended?: boolean | null
          patient_id?: string | null
          patient_notes?: string | null
          prescription?: string | null
          reminder_sent_at?: string | null
          service_id?: string | null
          slot_id?: string | null
          status?: Database["public"]["Enums"]["appointment_status"] | null
          total_amount: number
          treatment_plan?: string | null
          updated_at?: string | null
        }
        Update: {
          after_photos?: string[] | null
          appointment_date?: string
          before_photos?: string[] | null
          cancellation_reason?: string | null
          cancelled_at?: string | null
          checked_in_at?: string | null
          completed_at?: string | null
          confirmation_sent_at?: string | null
          created_at?: string | null
          deposit_amount?: number | null
          doctor_id?: string | null
          doctor_notes?: string | null
          duration_minutes?: number
          followup_date?: string | null
          id?: string
          next_appointment_recommended?: boolean | null
          patient_id?: string | null
          patient_notes?: string | null
          prescription?: string | null
          reminder_sent_at?: string | null
          service_id?: string | null
          slot_id?: string | null
          status?: Database["public"]["Enums"]["appointment_status"] | null
          total_amount?: number
          treatment_plan?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "appointments_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "doctors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_service_id_fkey"
            columns: ["service_id"]
            isOneToOne: false
            referencedRelation: "services"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_slot_id_fkey"
            columns: ["slot_id"]
            isOneToOne: false
            referencedRelation: "appointment_slots"
            referencedColumns: ["id"]
          },
        ]
      }
      blog_posts: {
        Row: {
          author_id: string | null
          category: string | null
          content: string
          created_at: string | null
          difficulty: Database["public"]["Enums"]["treatment_difficulty"] | null
          excerpt: string | null
          featured_image: string | null
          gallery_images: string[] | null
          id: string
          is_featured: boolean | null
          is_published: boolean | null
          language: string | null
          like_count: number | null
          published_at: string | null
          read_time_minutes: number | null
          seo_description: string | null
          seo_keywords: string[] | null
          seo_title: string | null
          slug: string
          tags: string[] | null
          title: string
          updated_at: string | null
          view_count: number | null
        }
        Insert: {
          author_id?: string | null
          category?: string | null
          content: string
          created_at?: string | null
          difficulty?:
            | Database["public"]["Enums"]["treatment_difficulty"]
            | null
          excerpt?: string | null
          featured_image?: string | null
          gallery_images?: string[] | null
          id?: string
          is_featured?: boolean | null
          is_published?: boolean | null
          language?: string | null
          like_count?: number | null
          published_at?: string | null
          read_time_minutes?: number | null
          seo_description?: string | null
          seo_keywords?: string[] | null
          seo_title?: string | null
          slug: string
          tags?: string[] | null
          title: string
          updated_at?: string | null
          view_count?: number | null
        }
        Update: {
          author_id?: string | null
          category?: string | null
          content?: string
          created_at?: string | null
          difficulty?:
            | Database["public"]["Enums"]["treatment_difficulty"]
            | null
          excerpt?: string | null
          featured_image?: string | null
          gallery_images?: string[] | null
          id?: string
          is_featured?: boolean | null
          is_published?: boolean | null
          language?: string | null
          like_count?: number | null
          published_at?: string | null
          read_time_minutes?: number | null
          seo_description?: string | null
          seo_keywords?: string[] | null
          seo_title?: string | null
          slug?: string
          tags?: string[] | null
          title?: string
          updated_at?: string | null
          view_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "blog_posts_author_id_fkey"
            columns: ["author_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      clinic_settings: {
        Row: {
          data_type: string | null
          description: string | null
          id: string
          is_public: boolean | null
          key: string
          updated_at: string | null
          updated_by: string | null
          value: string | null
        }
        Insert: {
          data_type?: string | null
          description?: string | null
          id?: string
          is_public?: boolean | null
          key: string
          updated_at?: string | null
          updated_by?: string | null
          value?: string | null
        }
        Update: {
          data_type?: string | null
          description?: string | null
          id?: string
          is_public?: boolean | null
          key?: string
          updated_at?: string | null
          updated_by?: string | null
          value?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "clinic_settings_updated_by_fkey"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      doctor_services: {
        Row: {
          created_at: string | null
          custom_price: number | null
          doctor_id: string | null
          id: string
          is_available: boolean | null
          service_id: string | null
        }
        Insert: {
          created_at?: string | null
          custom_price?: number | null
          doctor_id?: string | null
          id?: string
          is_available?: boolean | null
          service_id?: string | null
        }
        Update: {
          created_at?: string | null
          custom_price?: number | null
          doctor_id?: string | null
          id?: string
          is_available?: boolean | null
          service_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "doctor_services_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "doctors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "doctor_services_service_id_fkey"
            columns: ["service_id"]
            isOneToOne: false
            referencedRelation: "services"
            referencedColumns: ["id"]
          },
        ]
      }
      doctors: {
        Row: {
          bio: string | null
          consultation_fee: number | null
          created_at: string | null
          experience_years: number | null
          id: string
          image_url: string | null
          is_available: boolean | null
          languages_spoken: string[] | null
          license_number: string
          qualification: string
          rating: number | null
          specialization: string
          total_reviews: number | null
          updated_at: string | null
          working_hours: Json | null
        }
        Insert: {
          bio?: string | null
          consultation_fee?: number | null
          created_at?: string | null
          experience_years?: number | null
          id: string
          image_url?: string | null
          is_available?: boolean | null
          languages_spoken?: string[] | null
          license_number: string
          qualification: string
          rating?: number | null
          specialization: string
          total_reviews?: number | null
          updated_at?: string | null
          working_hours?: Json | null
        }
        Update: {
          bio?: string | null
          consultation_fee?: number | null
          created_at?: string | null
          experience_years?: number | null
          id?: string
          image_url?: string | null
          is_available?: boolean | null
          languages_spoken?: string[] | null
          license_number?: string
          qualification?: string
          rating?: number | null
          specialization?: string
          total_reviews?: number | null
          updated_at?: string | null
          working_hours?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "doctors_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      invoices: {
        Row: {
          appointment_id: string | null
          created_at: string | null
          currency: string | null
          discount_amount: number | null
          due_date: string | null
          id: string
          invoice_number: string
          is_paid: boolean | null
          issue_date: string
          notes: string | null
          payment_id: string | null
          pdf_url: string | null
          subtotal: number
          tax_amount: number | null
          total_amount: number
        }
        Insert: {
          appointment_id?: string | null
          created_at?: string | null
          currency?: string | null
          discount_amount?: number | null
          due_date?: string | null
          id?: string
          invoice_number: string
          is_paid?: boolean | null
          issue_date?: string
          notes?: string | null
          payment_id?: string | null
          pdf_url?: string | null
          subtotal: number
          tax_amount?: number | null
          total_amount: number
        }
        Update: {
          appointment_id?: string | null
          created_at?: string | null
          currency?: string | null
          discount_amount?: number | null
          due_date?: string | null
          id?: string
          invoice_number?: string
          is_paid?: boolean | null
          issue_date?: string
          notes?: string | null
          payment_id?: string | null
          pdf_url?: string | null
          subtotal?: number
          tax_amount?: number | null
          total_amount?: number
        }
        Relationships: [
          {
            foreignKeyName: "invoices_appointment_id_fkey"
            columns: ["appointment_id"]
            isOneToOne: false
            referencedRelation: "appointments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_payment_id_fkey"
            columns: ["payment_id"]
            isOneToOne: false
            referencedRelation: "payments"
            referencedColumns: ["id"]
          },
        ]
      }
      newsletter_subscribers: {
        Row: {
          email: string
          first_name: string | null
          id: string
          is_active: boolean | null
          language: string | null
          last_name: string | null
          preferences: Json | null
          source: string | null
          subscribed_at: string | null
          subscriber_tags: string[] | null
          unsubscribed_at: string | null
        }
        Insert: {
          email: string
          first_name?: string | null
          id?: string
          is_active?: boolean | null
          language?: string | null
          last_name?: string | null
          preferences?: Json | null
          source?: string | null
          subscribed_at?: string | null
          subscriber_tags?: string[] | null
          unsubscribed_at?: string | null
        }
        Update: {
          email?: string
          first_name?: string | null
          id?: string
          is_active?: boolean | null
          language?: string | null
          last_name?: string | null
          preferences?: Json | null
          source?: string | null
          subscribed_at?: string | null
          subscriber_tags?: string[] | null
          unsubscribed_at?: string | null
        }
        Relationships: []
      }
      notifications: {
        Row: {
          appointment_id: string | null
          channel: string
          content: string
          created_at: string | null
          delivery_status: string | null
          error_message: string | null
          id: string
          metadata: Json | null
          recipient_id: string | null
          sent_at: string | null
          subject: string | null
          template_id: string | null
          type: Database["public"]["Enums"]["notification_type"]
        }
        Insert: {
          appointment_id?: string | null
          channel: string
          content: string
          created_at?: string | null
          delivery_status?: string | null
          error_message?: string | null
          id?: string
          metadata?: Json | null
          recipient_id?: string | null
          sent_at?: string | null
          subject?: string | null
          template_id?: string | null
          type: Database["public"]["Enums"]["notification_type"]
        }
        Update: {
          appointment_id?: string | null
          channel?: string
          content?: string
          created_at?: string | null
          delivery_status?: string | null
          error_message?: string | null
          id?: string
          metadata?: Json | null
          recipient_id?: string | null
          sent_at?: string | null
          subject?: string | null
          template_id?: string | null
          type?: Database["public"]["Enums"]["notification_type"]
        }
        Relationships: [
          {
            foreignKeyName: "notifications_appointment_id_fkey"
            columns: ["appointment_id"]
            isOneToOne: false
            referencedRelation: "appointments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_recipient_id_fkey"
            columns: ["recipient_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      payments: {
        Row: {
          amount: number
          appointment_id: string | null
          created_at: string | null
          id: string
          metadata: Json | null
          patient_id: string | null
          payment_date: string | null
          payment_method: Database["public"]["Enums"]["payment_method"]
          payment_status: Database["public"]["Enums"]["payment_status"] | null
          receipt_url: string | null
          refund_amount: number | null
          refund_date: string | null
          refund_reason: string | null
          stripe_payment_intent_id: string | null
          transaction_id: string | null
          updated_at: string | null
        }
        Insert: {
          amount: number
          appointment_id?: string | null
          created_at?: string | null
          id?: string
          metadata?: Json | null
          patient_id?: string | null
          payment_date?: string | null
          payment_method: Database["public"]["Enums"]["payment_method"]
          payment_status?: Database["public"]["Enums"]["payment_status"] | null
          receipt_url?: string | null
          refund_amount?: number | null
          refund_date?: string | null
          refund_reason?: string | null
          stripe_payment_intent_id?: string | null
          transaction_id?: string | null
          updated_at?: string | null
        }
        Update: {
          amount?: number
          appointment_id?: string | null
          created_at?: string | null
          id?: string
          metadata?: Json | null
          patient_id?: string | null
          payment_date?: string | null
          payment_method?: Database["public"]["Enums"]["payment_method"]
          payment_status?: Database["public"]["Enums"]["payment_status"] | null
          receipt_url?: string | null
          refund_amount?: number | null
          refund_date?: string | null
          refund_reason?: string | null
          stripe_payment_intent_id?: string | null
          transaction_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "payments_appointment_id_fkey"
            columns: ["appointment_id"]
            isOneToOne: false
            referencedRelation: "appointments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payments_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      promotion_usage: {
        Row: {
          appointment_id: string | null
          discount_amount: number
          id: string
          patient_id: string | null
          promotion_id: string | null
          used_at: string | null
        }
        Insert: {
          appointment_id?: string | null
          discount_amount: number
          id?: string
          patient_id?: string | null
          promotion_id?: string | null
          used_at?: string | null
        }
        Update: {
          appointment_id?: string | null
          discount_amount?: number
          id?: string
          patient_id?: string | null
          promotion_id?: string | null
          used_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "promotion_usage_appointment_id_fkey"
            columns: ["appointment_id"]
            isOneToOne: false
            referencedRelation: "appointments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "promotion_usage_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "promotion_usage_promotion_id_fkey"
            columns: ["promotion_id"]
            isOneToOne: false
            referencedRelation: "promotions"
            referencedColumns: ["id"]
          },
        ]
      }
      promotions: {
        Row: {
          applicable_services: string[] | null
          created_at: string | null
          description: string
          discount_type: string
          discount_value: number
          end_date: string
          id: string
          image_url: string | null
          is_active: boolean | null
          is_featured: boolean | null
          max_discount_amount: number | null
          min_purchase_amount: number | null
          promo_code: string | null
          start_date: string
          terms_conditions: string | null
          title: string
          updated_at: string | null
          usage_count: number | null
          usage_limit: number | null
        }
        Insert: {
          applicable_services?: string[] | null
          created_at?: string | null
          description: string
          discount_type: string
          discount_value: number
          end_date: string
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          is_featured?: boolean | null
          max_discount_amount?: number | null
          min_purchase_amount?: number | null
          promo_code?: string | null
          start_date: string
          terms_conditions?: string | null
          title: string
          updated_at?: string | null
          usage_count?: number | null
          usage_limit?: number | null
        }
        Update: {
          applicable_services?: string[] | null
          created_at?: string | null
          description?: string
          discount_type?: string
          discount_value?: number
          end_date?: string
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          is_featured?: boolean | null
          max_discount_amount?: number | null
          min_purchase_amount?: number | null
          promo_code?: string | null
          start_date?: string
          terms_conditions?: string | null
          title?: string
          updated_at?: string | null
          usage_count?: number | null
          usage_limit?: number | null
        }
        Relationships: []
      }
      reviews: {
        Row: {
          after_photo_url: string | null
          appointment_id: string | null
          before_photo_url: string | null
          comment: string | null
          created_at: string | null
          doctor_id: string | null
          helpful_count: number | null
          id: string
          is_anonymous: boolean | null
          is_approved: boolean | null
          is_featured: boolean | null
          patient_id: string | null
          rating: number
          service_id: string | null
          title: string | null
          updated_at: string | null
        }
        Insert: {
          after_photo_url?: string | null
          appointment_id?: string | null
          before_photo_url?: string | null
          comment?: string | null
          created_at?: string | null
          doctor_id?: string | null
          helpful_count?: number | null
          id?: string
          is_anonymous?: boolean | null
          is_approved?: boolean | null
          is_featured?: boolean | null
          patient_id?: string | null
          rating: number
          service_id?: string | null
          title?: string | null
          updated_at?: string | null
        }
        Update: {
          after_photo_url?: string | null
          appointment_id?: string | null
          before_photo_url?: string | null
          comment?: string | null
          created_at?: string | null
          doctor_id?: string | null
          helpful_count?: number | null
          id?: string
          is_anonymous?: boolean | null
          is_approved?: boolean | null
          is_featured?: boolean | null
          patient_id?: string | null
          rating?: number
          service_id?: string | null
          title?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "reviews_appointment_id_fkey"
            columns: ["appointment_id"]
            isOneToOne: false
            referencedRelation: "appointments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reviews_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "doctors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reviews_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reviews_service_id_fkey"
            columns: ["service_id"]
            isOneToOne: false
            referencedRelation: "services"
            referencedColumns: ["id"]
          },
        ]
      }
      service_categories: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          image_url: string | null
          is_active: boolean | null
          name: string
          slug: string
          sort_order: number | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          name: string
          slug: string
          sort_order?: number | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          name?: string
          slug?: string
          sort_order?: number | null
        }
        Relationships: []
      }
      services: {
        Row: {
          aftercare_instructions: string | null
          base_price: number
          benefits: string[] | null
          category_id: string | null
          contraindications: string | null
          created_at: string | null
          description: string
          difficulty: Database["public"]["Enums"]["treatment_difficulty"] | null
          discounted_price: number | null
          duration_minutes: number
          gallery_images: string[] | null
          id: string
          image_url: string | null
          is_active: boolean | null
          is_featured: boolean | null
          is_popular: boolean | null
          name: string
          preparation_instructions: string | null
          procedures: string[] | null
          seo_description: string | null
          seo_keywords: string[] | null
          seo_title: string | null
          short_description: string | null
          slug: string
          sort_order: number | null
          updated_at: string | null
        }
        Insert: {
          aftercare_instructions?: string | null
          base_price: number
          benefits?: string[] | null
          category_id?: string | null
          contraindications?: string | null
          created_at?: string | null
          description: string
          difficulty?:
            | Database["public"]["Enums"]["treatment_difficulty"]
            | null
          discounted_price?: number | null
          duration_minutes?: number
          gallery_images?: string[] | null
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          is_featured?: boolean | null
          is_popular?: boolean | null
          name: string
          preparation_instructions?: string | null
          procedures?: string[] | null
          seo_description?: string | null
          seo_keywords?: string[] | null
          seo_title?: string | null
          short_description?: string | null
          slug: string
          sort_order?: number | null
          updated_at?: string | null
        }
        Update: {
          aftercare_instructions?: string | null
          base_price?: number
          benefits?: string[] | null
          category_id?: string | null
          contraindications?: string | null
          created_at?: string | null
          description?: string
          difficulty?:
            | Database["public"]["Enums"]["treatment_difficulty"]
            | null
          discounted_price?: number | null
          duration_minutes?: number
          gallery_images?: string[] | null
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          is_featured?: boolean | null
          is_popular?: boolean | null
          name?: string
          preparation_instructions?: string | null
          procedures?: string[] | null
          seo_description?: string | null
          seo_keywords?: string[] | null
          seo_title?: string | null
          short_description?: string | null
          slug?: string
          sort_order?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "services_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "service_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      user_profiles: {
        Row: {
          address: string | null
          allergies: string | null
          city: string | null
          country: string | null
          created_at: string | null
          current_medications: string | null
          date_of_birth: string | null
          email: string
          emergency_contact_name: string | null
          emergency_contact_phone: string | null
          first_name: string
          gender: string | null
          id: string
          is_active: boolean | null
          last_login: string | null
          last_name: string
          marketing_consent: boolean | null
          medical_history: string | null
          phone: string | null
          preferred_language: string | null
          privacy_consent: boolean
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string | null
        }
        Insert: {
          address?: string | null
          allergies?: string | null
          city?: string | null
          country?: string | null
          created_at?: string | null
          current_medications?: string | null
          date_of_birth?: string | null
          email: string
          emergency_contact_name?: string | null
          emergency_contact_phone?: string | null
          first_name: string
          gender?: string | null
          id: string
          is_active?: boolean | null
          last_login?: string | null
          last_name: string
          marketing_consent?: boolean | null
          medical_history?: string | null
          phone?: string | null
          preferred_language?: string | null
          privacy_consent?: boolean
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
        }
        Update: {
          address?: string | null
          allergies?: string | null
          city?: string | null
          country?: string | null
          created_at?: string | null
          current_medications?: string | null
          date_of_birth?: string | null
          email?: string
          emergency_contact_name?: string | null
          emergency_contact_phone?: string | null
          first_name?: string
          gender?: string | null
          id?: string
          is_active?: boolean | null
          last_login?: string | null
          last_name?: string
          marketing_consent?: boolean | null
          medical_history?: string | null
          phone?: string | null
          preferred_language?: string | null
          privacy_consent?: boolean
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      appointment_status:
        | "scheduled"
        | "confirmed"
        | "in_progress"
        | "completed"
        | "cancelled"
        | "no_show"
      notification_type:
        | "appointment_confirmation"
        | "appointment_reminder"
        | "payment_confirmation"
        | "treatment_followup"
        | "marketing"
      payment_method: "cash" | "card" | "bank_transfer" | "insurance"
      payment_status: "pending" | "paid" | "failed" | "refunded" | "partial"
      treatment_difficulty: "beginner" | "intermediate" | "advanced"
      user_role: "patient" | "doctor" | "admin" | "staff"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

// Export commonly used type aliases for convenience
export type UserProfile = Tables<'user_profiles'>
export type UserProfileInsert = TablesInsert<'user_profiles'>
export type UserProfileUpdate = TablesUpdate<'user_profiles'>

export type Doctor = Tables<'doctors'>
export type DoctorInsert = TablesInsert<'doctors'>
export type DoctorUpdate = TablesUpdate<'doctors'>

export type Service = Tables<'services'>
export type ServiceInsert = TablesInsert<'services'>
export type ServiceUpdate = TablesUpdate<'services'>

export type Appointment = Tables<'appointments'>
export type AppointmentInsert = TablesInsert<'appointments'>
export type AppointmentUpdate = TablesUpdate<'appointments'>

export type Payment = Tables<'payments'>
export type PaymentInsert = TablesInsert<'payments'>
export type PaymentUpdate = TablesUpdate<'payments'>

export type Review = Tables<'reviews'>
export type ReviewInsert = TablesInsert<'reviews'>
export type ReviewUpdate = TablesUpdate<'reviews'>

export type BlogPost = Tables<'blog_posts'>
export type BlogPostInsert = TablesInsert<'blog_posts'>
export type BlogPostUpdate = TablesUpdate<'blog_posts'>

export type Promotion = Tables<'promotions'>
export type PromotionInsert = TablesInsert<'promotions'>
export type PromotionUpdate = TablesUpdate<'promotions'>

export type ServiceCategory = Tables<'service_categories'>
export type ServiceCategoryInsert = TablesInsert<'service_categories'>
export type ServiceCategoryUpdate = TablesUpdate<'service_categories'>

export type NewsletterSubscriber = Tables<'newsletter_subscribers'>
export type NewsletterSubscriberInsert = TablesInsert<'newsletter_subscribers'>
export type NewsletterSubscriberUpdate = TablesUpdate<'newsletter_subscribers'>

// Enum type aliases
export type UserRole = Enums<'user_role'>
export type AppointmentStatus = Enums<'appointment_status'>
export type PaymentStatus = Enums<'payment_status'>
export type PaymentMethod = Enums<'payment_method'>
export type NotificationType = Enums<'notification_type'>
export type TreatmentDifficulty = Enums<'treatment_difficulty'>

export const Constants = {
  public: {
    Enums: {
      appointment_status: [
        "scheduled",
        "confirmed",
        "in_progress",
        "completed",
        "cancelled",
        "no_show",
      ],
      notification_type: [
        "appointment_confirmation",
        "appointment_reminder", 
        "payment_confirmation",
        "treatment_followup",
        "marketing",
      ],
      payment_method: ["cash", "card", "bank_transfer", "insurance"],
      payment_status: ["pending", "paid", "failed", "refunded", "partial"],
      treatment_difficulty: ["beginner", "intermediate", "advanced"],
      user_role: ["patient", "doctor", "admin", "staff"],
    },
  },
} as const