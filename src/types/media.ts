
/**
 * Lullaby Clinic - Media Types
 * Types for images, videos, and media handling
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

export interface ImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  placeholder?: string;
  loading?: 'lazy' | 'eager';
  className?: string;
}

export interface BeforeAfterItem {
  id: string;
  beforeImage: string;
  afterImage: string;
  treatment: string;
  description: string;
  category: string;
}
