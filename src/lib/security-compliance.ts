/**
 * Lullaby Clinic - Security & HIPAA Compliance Utilities
 * Comprehensive security measures for healthcare data protection
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import { auth, db } from './supabase';
import type { UserProfile, AuditAction } from '@/types';

// HIPAA compliance constants
export const HIPAA_CONFIG = {
  DATA_RETENTION_YEARS: 6,
  PASSWORD_MIN_LENGTH: 12,
  SESSION_TIMEOUT_MINUTES: 30,
  MAX_LOGIN_ATTEMPTS: 5,
  ACCOUNT_LOCKOUT_MINUTES: 30,
  AUDIT_RETENTION_YEARS: 7,
  ENCRYPTION_ALGORITHM: 'AES-256-GCM',
  BACKUP_RETENTION_DAYS: 30
};

// Sensitive data fields that require special handling
export const SENSITIVE_FIELDS = [
  'medical_history',
  'allergies',
  'current_medications',
  'prescription',
  'doctor_notes',
  'treatment_plan',
  'before_photos',
  'after_photos',
  'phone',
  'email',
  'address',
  'emergency_contact_name',
  'emergency_contact_phone'
];

// PHI (Protected Health Information) categories
export const PHI_CATEGORIES = {
  IDENTIFIERS: [
    'first_name',
    'last_name',
    'email',
    'phone',
    'address',
    'city',
    'country'
  ],
  HEALTH_DATA: [
    'medical_history',
    'allergies',
    'current_medications',
    'prescription',
    'doctor_notes',
    'treatment_plan'
  ],
  BIOMETRIC: [
    'before_photos',
    'after_photos'
  ],
  FINANCIAL: [
    'payment_method',
    'transaction_id',
    'stripe_payment_intent_id'
  ]
};

// Security utility functions
export class SecurityComplianceService {
  // Audit logging for HIPAA compliance
  static async logSecurityEvent(
    action: AuditAction,
    tableName: string,
    recordId: string,
    oldValues?: Record<string, unknown>,
    newValues?: Record<string, unknown>,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    try {
      const { data: { user } } = await auth.getUser();
      
      await db.auditLogs().insert({
        table_name: tableName,
        record_id: recordId,
        action,
        old_values: oldValues,
        new_values: newValues,
        user_id: user?.id,
        ip_address: ipAddress,
        user_agent: userAgent
      });
    } catch (error) {
      console.error('Failed to log security event:', error);
      // Don't throw error - audit logging failures shouldn't break app functionality
    }
  }

  // Data anonymization for PHI
  static anonymizePatientData(data: Record<string, unknown>): Record<string, unknown> {
    if (!data) return data;

    const anonymized = { ...data };

    // Replace PHI identifiers with anonymized values
    PHI_CATEGORIES.IDENTIFIERS.forEach(field => {
      if (anonymized[field]) {
        switch (field) {
          case 'first_name':
          case 'last_name':
            anonymized[field] = '****';
            break;
          case 'email':
            anonymized[field] = '****@****.com';
            break;
          case 'phone':
            anonymized[field] = '***-***-****';
            break;
          case 'address':
          case 'city':
          case 'country':
            anonymized[field] = '****';
            break;
        }
      }
    });

    // Anonymize health data
    PHI_CATEGORIES.HEALTH_DATA.forEach(field => {
      if (anonymized[field]) {
        anonymized[field] = '[PROTECTED HEALTH INFORMATION]';
      }
    });

    // Remove biometric data
    PHI_CATEGORIES.BIOMETRIC.forEach(field => {
      if (anonymized[field]) {
        anonymized[field] = '[PROTECTED BIOMETRIC DATA]';
      }
    });

    // Anonymize financial data
    PHI_CATEGORIES.FINANCIAL.forEach(field => {
      if (anonymized[field]) {
        anonymized[field] = '[PROTECTED FINANCIAL DATA]';
      }
    });

    return anonymized;
  }

  // Validate password strength for HIPAA compliance
  static validatePasswordStrength(password: string): {
    isValid: boolean;
    score: number;
    requirements: {
      length: boolean;
      uppercase: boolean;
      lowercase: boolean;
      numbers: boolean;
      symbols: boolean;
      noCommonWords: boolean;
    };
    suggestions: string[];
  } {
    const requirements = {
      length: password.length >= HIPAA_CONFIG.PASSWORD_MIN_LENGTH,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      numbers: /\d/.test(password),
      symbols: /[!@#$%^&*()_+\-[\]{};':"|,.<>/?]/.test(password),
      noCommonWords: !this.containsCommonWords(password)
    };

    const score = Object.values(requirements).filter(Boolean).length;
    const isValid = score >= 5 && requirements.length;

    const suggestions: string[] = [];
    if (!requirements.length) suggestions.push(`Use at least ${HIPAA_CONFIG.PASSWORD_MIN_LENGTH} characters`);
    if (!requirements.uppercase) suggestions.push('Add uppercase letters');
    if (!requirements.lowercase) suggestions.push('Add lowercase letters');
    if (!requirements.numbers) suggestions.push('Add numbers');
    if (!requirements.symbols) suggestions.push('Add special characters');
    if (!requirements.noCommonWords) suggestions.push('Avoid common words');

    return {
      isValid,
      score,
      requirements,
      suggestions
    };
  }

  // Check for common words in password
  private static containsCommonWords(password: string): boolean {
    const commonWords = [
      'password', 'clinic', 'lullaby', 'admin', 'user', 'login',
      '123456', 'qwerty', 'abc123', 'password123', 'admin123'
    ];
    
    const lowerPassword = password.toLowerCase();
    return commonWords.some(word => lowerPassword.includes(word));
  }

  // Data access logging for HIPAA audit trails
  static async logDataAccess(
    tableName: string,
    recordId: string,
    accessType: 'READ' | 'WRITE' | 'DELETE',
    userId?: string,
    reason?: string
  ): Promise<void> {
    try {
      const { data: { user } } = await auth.getUser();
      
      await db.auditLogs().insert({
        table_name: tableName,
        record_id: recordId,
        action: accessType,
        new_values: { 
          access_type: accessType, 
          reason,
          timestamp: new Date().toISOString()
        },
        user_id: userId || user?.id
      });
    } catch (error) {
      console.error('Failed to log data access:', error);
    }
  }

  // Session management for HIPAA compliance
  static async validateSession(): Promise<{
    isValid: boolean;
    timeRemaining?: number;
    shouldRefresh?: boolean;
  }> {
    try {
      const { data: { session } } = await auth.getSession();
      
      if (!session) {
        return { isValid: false };
      }

      const expiresAt = session.expires_at ? new Date(session.expires_at * 1000) : null;
      const now = new Date();
      
      if (!expiresAt || expiresAt <= now) {
        return { isValid: false };
      }

      const timeRemaining = expiresAt.getTime() - now.getTime();
      const shouldRefresh = timeRemaining < (5 * 60 * 1000); // Refresh if less than 5 minutes

      return {
        isValid: true,
        timeRemaining,
        shouldRefresh
      };
    } catch (error) {
      console.error('Session validation error:', error);
      return { isValid: false };
    }
  }

  // Data encryption utilities (client-side)
  static async encryptSensitiveData(data: string, key?: string): Promise<string> {
    try {
      // In a real implementation, use Web Crypto API for encryption
      // This is a simplified example
      if (typeof window !== 'undefined' && window.crypto && window.crypto.subtle) {
        // Use browser's Web Crypto API
        const encoder = new TextEncoder();
        const dataBuffer = encoder.encode(data);
        
        // Generate a key if not provided
        if (!key) {
          const cryptoKey = await window.crypto.subtle.generateKey(
            { name: 'AES-GCM', length: 256 },
            true,
            ['encrypt', 'decrypt']
          );
          
          // In real implementation, store this key securely
          key = await window.crypto.subtle.exportKey('raw', cryptoKey);
        }
        
        // This is a simplified example - in production, use proper encryption
        return btoa(data); // Base64 encoding as placeholder
      }
      
      // Fallback for server-side or older browsers
      return btoa(data);
    } catch (error) {
      console.error('Encryption error:', error);
      return data; // Return original data if encryption fails
    }
  }

  // Data decryption utilities (client-side)
  static async decryptSensitiveData(encryptedData: string, key?: string): Promise<string> {
    try {
      // This is a simplified example - in production, use proper decryption
      return atob(encryptedData);
    } catch (error) {
      console.error('Decryption error:', error);
      return encryptedData; // Return original data if decryption fails
    }
  }

  // User role validation
  static async validateUserRole(
    requiredRole: string | string[],
    userId?: string
  ): Promise<{ hasAccess: boolean; userRole?: string }> {
    try {
      const { data: { user } } = await auth.getUser();
      const targetUserId = userId || user?.id;
      
      if (!targetUserId) {
        return { hasAccess: false };
      }

      const { data: profile } = await db.userProfiles()
        .select('role')
        .eq('id', targetUserId)
        .single();

      if (!profile) {
        return { hasAccess: false };
      }

      const allowedRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
      const hasAccess = allowedRoles.includes(profile.role);

      return { hasAccess, userRole: profile.role };
    } catch (error) {
      console.error('Role validation error:', error);
      return { hasAccess: false };
    }
  }

  // Rate limiting for API calls
  static rateLimiter = {
    attempts: new Map<string, { count: number; firstAttempt: number }>(),

    checkRateLimit(identifier: string, maxAttempts: number = 5, windowMs: number = 60000): boolean {
      const now = Date.now();
      const userAttempts = this.attempts.get(identifier);

      if (!userAttempts) {
        this.attempts.set(identifier, { count: 1, firstAttempt: now });
        return true;
      }

      // Reset if window has passed
      if (now - userAttempts.firstAttempt > windowMs) {
        this.attempts.set(identifier, { count: 1, firstAttempt: now });
        return true;
      }

      // Check if limit exceeded
      if (userAttempts.count >= maxAttempts) {
        return false;
      }

      // Increment attempts
      userAttempts.count++;
      return true;
    },

    resetRateLimit(identifier: string): void {
      this.attempts.delete(identifier);
    }
  };

  // Input sanitization for XSS prevention
  static sanitizeInput(input: string): string {
    if (typeof input !== 'string') return '';
    
    return input
      .replace(/[<>]/g, '') // Remove < and > characters
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, '') // Remove event handlers
      .trim();
  }

  // SQL injection prevention (for raw queries)
  static sanitizeForDatabase(input: string): string {
    if (typeof input !== 'string') return '';
    
    return input
      .replace(/'/g, "''") // Escape single quotes
      .replace(/;/g, '') // Remove semicolons
      .replace(/--/g, '') // Remove SQL comments
      .replace(/\/\*/g, '') // Remove SQL block comments start
      .replace(/\*\//g, '') // Remove SQL block comments end
      .trim();
  }

  // Generate secure random tokens
  static generateSecureToken(length: number = 32): string {
    if (typeof window !== 'undefined' && window.crypto) {
      const array = new Uint8Array(length);
      window.crypto.getRandomValues(array);
      return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    }
    
    // Fallback for server-side
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  // Data backup validation
  static async validateDataIntegrity(tableName: string, recordId: string): Promise<{
    isValid: boolean;
    checksum?: string;
    lastModified?: string;
  }> {
    try {
      // Get record with metadata
      const { data: record } = await db[tableName as keyof typeof db]()
        .select('*, updated_at')
        .eq('id', recordId)
        .single();

      if (!record) {
        return { isValid: false };
      }

      // Generate checksum (simplified)
      const recordString = JSON.stringify(record);
      const checksum = btoa(recordString).slice(0, 16);

      return {
        isValid: true,
        checksum,
        lastModified: record.updated_at
      };
    } catch (error) {
      console.error('Data integrity check failed:', error);
      return { isValid: false };
    }
  }

  // HIPAA-compliant data export
  static async exportPatientData(patientId: string): Promise<{
    success: boolean;
    data?: Record<string, unknown>;
    error?: string;
  }> {
    try {
      // Validate user has permission to export this data
      const { data: { user } } = await auth.getUser();
      if (!user || (user.id !== patientId && !(await this.validateUserRole(['admin', 'staff'], user.id)).hasAccess)) {
        return { success: false, error: 'Unauthorized access' };
      }

      // Get all patient data
      const [profile, appointments, payments, reviews] = await Promise.all([
        db.userProfiles().select('*').eq('id', patientId).single(),
        db.appointments().select('*').eq('patient_id', patientId),
        db.payments().select('*').eq('patient_id', patientId),
        db.reviews().select('*').eq('patient_id', patientId)
      ]);

      const exportData = {
        profile: profile.data,
        appointments: appointments.data,
        payments: payments.data,
        reviews: reviews.data,
        exportedAt: new Date().toISOString(),
        exportedBy: user.id
      };

      // Log data export
      await this.logSecurityEvent('UPDATE', 'user_profiles', patientId, null, {
        action: 'data_export',
        exported_at: new Date().toISOString()
      });

      return { success: true, data: exportData };
    } catch (error) {
      console.error('Data export failed:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Export failed' 
      };
    }
  }

  // HIPAA-compliant data deletion
  static async deletePatientData(patientId: string, reason: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // Validate user has permission
      const { data: { user } } = await auth.getUser();
      if (!user || !(await this.validateUserRole(['admin'], user.id)).hasAccess) {
        return { success: false, error: 'Unauthorized access' };
      }

      // Log deletion request
      await this.logSecurityEvent('DELETE', 'user_profiles', patientId, null, {
        action: 'data_deletion_requested',
        reason,
        requested_by: user.id,
        requested_at: new Date().toISOString()
      });

      // In a real implementation, this would trigger a secure deletion process
      // that follows HIPAA guidelines for data destruction
      
      console.log(`Data deletion requested for patient ${patientId}. Reason: ${reason}`);
      
      return { success: true };
    } catch (error) {
      console.error('Data deletion failed:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Deletion failed' 
      };
    }
  }
}

// Export security compliance utilities
export default SecurityComplianceService;