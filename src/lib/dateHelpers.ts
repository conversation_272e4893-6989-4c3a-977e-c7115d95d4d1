export function generateMockDates(index: number): { before: Date; after: Date } {
  const base = new Date();
  const daysAgo = (index + 1) * 30 + Math.floor(Math.random() * 60);
  const treatmentDuration = 14 + Math.floor(Math.random() * 90);
  
  const before = new Date(base.getTime() - daysAgo * 24 * 60 * 60 * 1000);
  const after = new Date(before.getTime() + treatmentDuration * 24 * 60 * 60 * 1000);
  
  return { before, after };
}

export function formatDateRange(beforeDate: string, afterDate: string): string {
  const before = new Date(beforeDate);
  const after = new Date(afterDate);
  
  const beforeFormatted = before.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
  
  const afterFormatted = after.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
  
  return `${beforeFormatted} → ${afterFormatted}`;
}

export function calculateTreatmentDuration(beforeDate: string, afterDate: string): string {
  const before = new Date(beforeDate);
  const after = new Date(afterDate);
  
  const diffTime = Math.abs(after.getTime() - before.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays < 7) {
    return `${diffDays} days`;
  } else if (diffDays < 30) {
    const weeks = Math.floor(diffDays / 7);
    return `${weeks} week${weeks > 1 ? 's' : ''}`;
  } else {
    const months = Math.floor(diffDays / 30);
    return `${months} month${months > 1 ? 's' : ''}`;
  }
}

export function isRecentTreatment(afterDate: string, daysThreshold: number = 30): boolean {
  const after = new Date(afterDate);
  const now = new Date();
  const diffTime = now.getTime() - after.getTime();
  const diffDays = diffTime / (1000 * 60 * 60 * 24);
  
  return diffDays <= daysThreshold;
}
