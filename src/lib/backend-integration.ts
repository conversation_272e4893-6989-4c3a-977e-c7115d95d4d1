/**
 * Lullaby Clinic - Backend Integration via Supabase
 * Simple integration that connects frontend to backend through Supabase
 * 
 * @version 1.0.0
 * @created 2024-06-24
 */

import { supabase } from './supabase/client';
import type { User } from '@supabase/supabase-js';

// Backend integration configuration
export const BACKEND_CONFIG = {
  // Backend API URL (Payload CMS)
  API_URL: import.meta.env.VITE_PAYLOAD_API_URL || 'http://localhost:3000',
  // Whether to sync with backend
  ENABLE_BACKEND_SYNC: !!import.meta.env.VITE_PAYLOAD_API_URL,
  // Timeout for backend requests
  TIMEOUT: 5000,
} as const;

/**
 * Backend integration service
 * Handles communication between frontend and backend through Supabase
 */
export class BackendIntegrationService {
  private isBackendAvailable = false;

  constructor() {
    this.checkBackendAvailability();
  }

  /**
   * Check if backend is available
   */
  private async checkBackendAvailability(): Promise<void> {
    if (!BACKEND_CONFIG.ENABLE_BACKEND_SYNC) {
      this.isBackendAvailable = false;
      return;
    }

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), BACKEND_CONFIG.TIMEOUT);

      const response = await fetch(`${BACKEND_CONFIG.API_URL}/health`, {
        method: 'GET',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      this.isBackendAvailable = response.ok;
    } catch (error) {
      console.warn('Backend not available, using Supabase only:', error);
      this.isBackendAvailable = false;
    }
  }

  /**
   * Get backend availability status
   */
  public getBackendStatus(): { available: boolean; url: string } {
    return {
      available: this.isBackendAvailable,
      url: BACKEND_CONFIG.API_URL,
    };
  }

  /**
   * Sync user data to backend when user signs in
   */
  public async syncUserToBackend(user: User): Promise<void> {
    if (!this.isBackendAvailable || !BACKEND_CONFIG.ENABLE_BACKEND_SYNC) {
      return;
    }

    try {
      // Get user profile from Supabase
      const { data: profile } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();

      // Prepare user data for backend
      const userData = {
        email: user.email,
        supabase_id: user.id,
        first_name: profile?.first_name || user.user_metadata?.first_name || '',
        last_name: profile?.last_name || user.user_metadata?.last_name || '',
        phone: profile?.phone || user.user_metadata?.phone || '',
        role: 'patient', // Default role
        is_active: true,
      };

      // Send to backend
      const response = await fetch(`${BACKEND_CONFIG.API_URL}/api/users/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
        signal: AbortSignal.timeout(BACKEND_CONFIG.TIMEOUT),
      });

      if (!response.ok) {
        throw new Error(`Backend sync failed: ${response.statusText}`);
      }

      console.log('User synced to backend successfully');
    } catch (error) {
      console.warn('Failed to sync user to backend:', error);
      // Don't throw error - this is optional functionality
    }
  }

  /**
   * Notify backend of appointment creation
   */
  public async notifyAppointmentCreated(appointmentData: any): Promise<void> {
    if (!this.isBackendAvailable || !BACKEND_CONFIG.ENABLE_BACKEND_SYNC) {
      return;
    }

    try {
      const response = await fetch(`${BACKEND_CONFIG.API_URL}/api/appointments/notify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(appointmentData),
        signal: AbortSignal.timeout(BACKEND_CONFIG.TIMEOUT),
      });

      if (!response.ok) {
        throw new Error(`Backend notification failed: ${response.statusText}`);
      }

      console.log('Appointment notification sent to backend');
    } catch (error) {
      console.warn('Failed to notify backend of appointment:', error);
    }
  }

  /**
   * Get content from backend (with Supabase fallback)
   */
  public async getContent(type: 'doctors' | 'services' | 'blog-posts', params?: any): Promise<any> {
    // Always use Supabase as primary data source
    try {
      let query;
      
      switch (type) {
        case 'doctors':
          query = supabase
            .from('doctors')
            .select(`
              *,
              user_profiles(first_name, last_name, email, phone)
            `);
          break;
        
        case 'services':
          query = supabase
            .from('services')
            .select('*');
          break;
        
        case 'blog-posts':
          query = supabase
            .from('blog_posts')
            .select('*')
            .eq('is_published', true);
          break;
        
        default:
          throw new Error(`Unknown content type: ${type}`);
      }

      // Apply filters if provided
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            query = query.eq(key, value);
          }
        });
      }

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      return {
        success: true,
        data,
        source: 'supabase',
      };
    } catch (error) {
      console.error(`Failed to get ${type} from Supabase:`, error);
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
        source: 'error',
      };
    }
  }

  /**
   * Upload media through Supabase storage
   */
  public async uploadMedia(file: File, bucket = 'media'): Promise<any> {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `uploads/${fileName}`;

      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(filePath, file);

      if (error) {
        throw error;
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from(bucket)
        .getPublicUrl(filePath);

      return {
        success: true,
        data: {
          id: data.path,
          filename: fileName,
          url: publicUrl,
          size: file.size,
          type: file.type,
        },
      };
    } catch (error) {
      console.error('Media upload failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed',
      };
    }
  }
}

// Export singleton instance
export const backendIntegration = new BackendIntegrationService();

// Set up auth state listener for user sync
supabase.auth.onAuthStateChange(async (event, session) => {
  if (event === 'SIGNED_IN' && session?.user) {
    // Sync user to backend when they sign in
    await backendIntegration.syncUserToBackend(session.user);
  }
});

// Export utility functions
export const getBackendStatus = () => backendIntegration.getBackendStatus();
export const getContent = (type: 'doctors' | 'services' | 'blog-posts', params?: any) => 
  backendIntegration.getContent(type, params);
export const uploadMedia = (file: File, bucket?: string) => 
  backendIntegration.uploadMedia(file, bucket);
export const notifyAppointmentCreated = (appointmentData: any) => 
  backendIntegration.notifyAppointmentCreated(appointmentData);

export default backendIntegration;
