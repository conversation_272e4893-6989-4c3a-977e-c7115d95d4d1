/**
 * Lullaby Clinic - Social Authentication Service
 * Facebook and Google OAuth integration with Supabase
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import { supabase } from './supabase';
import type { Provider } from '@supabase/supabase-js';

export interface SocialAuthOptions {
  redirectTo?: string;
  scopes?: string;
}

export interface SocialAuthResult {
  data: unknown;
  error: unknown;
}

/**
 * Social Authentication Service
 */
export class SocialAuthService {
  /**
   * Sign in with Google
   */
  static async signInWithGoogle(options: SocialAuthOptions = {}): Promise<SocialAuthResult> {
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: options.redirectTo || `${window.location.origin}/auth/callback`,
          scopes: options.scopes || 'email profile',
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      });

      return { data, error };
    } catch (error) {
      console.error('Google sign-in error:', error);
      return { data: null, error };
    }
  }

  /**
   * Sign in with Facebook
   */
  static async signInWithFacebook(options: SocialAuthOptions = {}): Promise<SocialAuthResult> {
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'facebook',
        options: {
          redirectTo: options.redirectTo || `${window.location.origin}/auth/callback`,
          scopes: options.scopes || 'email public_profile',
        },
      });

      return { data, error };
    } catch (error) {
      console.error('Facebook sign-in error:', error);
      return { data: null, error };
    }
  }

  /**
   * Generic OAuth sign-in method
   */
  static async signInWithProvider(
    provider: Provider,
    options: SocialAuthOptions = {}
  ): Promise<SocialAuthResult> {
    switch (provider) {
      case 'google':
        return this.signInWithGoogle(options);
      case 'facebook':
        return this.signInWithFacebook(options);
      default:
        return {
          data: null,
          error: new Error(`Unsupported provider: ${provider}`)
        };
    }
  }

  /**
   * Handle OAuth callback
   * Call this on your callback page to handle the OAuth response
   */
  static async handleOAuthCallback(): Promise<SocialAuthResult> {
    try {
      const { data, error } = await supabase.auth.getSession();
      
      if (error) {
        console.error('OAuth callback error:', error);
        return { data: null, error };
      }

      // If we have a session, the OAuth was successful
      if (data.session) {
        const user = data.session.user;
        
        // Check if user profile exists, if not create one
        const { data: profile, error: profileError } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (profileError && profileError.code === 'PGRST116') {
          // Profile doesn't exist, create one from OAuth data
          const newProfile = {
            id: user.id,
            email: user.email,
            first_name: user.user_metadata?.first_name || user.user_metadata?.given_name || '',
            last_name: user.user_metadata?.last_name || user.user_metadata?.family_name || '',
            full_name: user.user_metadata?.full_name || user.user_metadata?.name || '',
            avatar_url: user.user_metadata?.avatar_url || user.user_metadata?.picture || null,
            provider: user.app_metadata?.provider || 'unknown',
            role: 'patient',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };

          const { error: insertError } = await supabase
            .from('user_profiles')
            .insert(newProfile);

          if (insertError) {
            console.error('Profile creation error:', insertError);
            return { data: data.session, error: insertError };
          }
        }

        return { data: data.session, error: null };
      }

      return { data: null, error: new Error('No session found') };
    } catch (error) {
      console.error('OAuth callback handling error:', error);
      return { data: null, error };
    }
  }

  /**
   * Get current user's social provider info
   */
  static async getUserProviderInfo(): Promise<{ provider?: string; providerId?: string }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return {};
      }

      const provider = user.app_metadata?.provider;
      const providerId = user.app_metadata?.provider_id || user.user_metadata?.provider_id;

      return { provider, providerId };
    } catch (error) {
      console.error('Error getting provider info:', error);
      return {};
    }
  }

  /**
   * Link additional social provider to existing account
   */
  static async linkProvider(provider: Provider): Promise<SocialAuthResult> {
    try {
      const { data, error } = await supabase.auth.linkIdentity({
        provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback?linking=true`,
        },
      });

      return { data, error };
    } catch (error) {
      console.error('Provider linking error:', error);
      return { data: null, error };
    }
  }

  /**
   * Unlink social provider from account
   */
  static async unlinkProvider(provider: Provider): Promise<SocialAuthResult> {
    try {
      const { data, error } = await supabase.auth.unlinkIdentity({
        provider,
      });

      return { data, error };
    } catch (error) {
      console.error('Provider unlinking error:', error);
      return { data: null, error };
    }
  }
}

export default SocialAuthService;