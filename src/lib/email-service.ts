
/**
 * Lullaby Clinic - Email Service
 * Service for sending various types of emails
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import type { Appointment, UserProfile } from '@/types/database';

interface EmailConfig {
  enabled: boolean;
  configured: boolean;
  provider: string;
  settings?: Record<string, unknown>;
}

interface EmailResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

// Mock email service for development
class EmailService {
  private config: EmailConfig = {
    enabled: true,
    configured: true,
    provider: 'Console'
  };

  async sendAppointmentConfirmation(
    appointment: Appointment & {
      patient?: { email: string; first_name: string; last_name: string };
      doctor?: { user_profile: { first_name: string; last_name: string } };
      service?: { name: string };
    },
    language: 'th' | 'en' = 'th'
  ): Promise<EmailResult> {
    console.log(`Sending appointment confirmation email in ${language}:`, {
      to: appointment.patient?.email,
      appointmentId: appointment.id,
      service: appointment.service?.name,
      doctor: `${appointment.doctor?.user_profile?.first_name} ${appointment.doctor?.user_profile?.last_name}`
    });

    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 500));

    return {
      success: true,
      messageId: `conf_${appointment.id}_${Date.now()}`
    };
  }

  async sendAppointmentReminder(
    appointment: Appointment & {
      patient?: { email: string; first_name: string; last_name: string };
      doctor?: { user_profile: { first_name: string; last_name: string } };
      service?: { name: string };
    },
    language: 'th' | 'en' = 'th'
  ): Promise<EmailResult> {
    console.log(`Sending appointment reminder email in ${language}:`, {
      to: appointment.patient?.email,
      appointmentId: appointment.id,
      appointmentDate: appointment.appointment_date
    });

    await new Promise(resolve => setTimeout(resolve, 500));

    return {
      success: true,
      messageId: `reminder_${appointment.id}_${Date.now()}`
    };
  }

  async sendAppointmentCancellation(
    appointment: Appointment & {
      patient?: { email: string; first_name: string; last_name: string };
      doctor?: { user_profile: { first_name: string; last_name: string } };
      service?: { name: string };
    },
    language: 'th' | 'en' = 'th'
  ): Promise<EmailResult> {
    console.log(`Sending appointment cancellation email in ${language}:`, {
      to: appointment.patient?.email,
      appointmentId: appointment.id,
      reason: appointment.cancellation_reason
    });

    await new Promise(resolve => setTimeout(resolve, 500));

    return {
      success: true,
      messageId: `cancel_${appointment.id}_${Date.now()}`
    };
  }

  async sendWelcomeEmail(
    user: UserProfile,
    language: 'th' | 'en' = 'th'
  ): Promise<EmailResult> {
    console.log(`Sending welcome email in ${language}:`, {
      to: user.email,
      name: `${user.first_name} ${user.last_name}`
    });

    await new Promise(resolve => setTimeout(resolve, 500));

    return {
      success: true,
      messageId: `welcome_${user.id}_${Date.now()}`
    };
  }

  async sendPasswordResetEmail(
    email: string,
    resetToken: string,
    language: 'th' | 'en' = 'th'
  ): Promise<EmailResult> {
    console.log(`Sending password reset email in ${language}:`, {
      to: email,
      resetToken
    });

    await new Promise(resolve => setTimeout(resolve, 500));

    return {
      success: true,
      messageId: `reset_${email}_${Date.now()}`
    };
  }

  async sendNewsletterEmail(
    recipients: string[],
    subject: string,
    content: string,
    language: 'th' | 'en' = 'th'
  ): Promise<EmailResult> {
    console.log(`Sending newsletter email in ${language}:`, {
      recipients: recipients.length,
      subject
    });

    await new Promise(resolve => setTimeout(resolve, 1000));

    return {
      success: true,
      messageId: `newsletter_${Date.now()}`
    };
  }

  getConfig(): EmailConfig {
    return this.config;
  }

  async testEmailConfiguration(): Promise<EmailResult> {
    console.log('Testing email configuration...');
    
    await new Promise(resolve => setTimeout(resolve, 500));

    return {
      success: true,
      messageId: `test_${Date.now()}`
    };
  }
}

// Export singleton instance
export const emailService = new EmailService();
