
/**
 * Lullaby Clinic - Admin Services
 * Administrative service functions for dashboard operations
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import { supabase } from './supabase/client';
import type { DashboardStats, AdminUserInput, AdminProfile } from '@/types/admin';
import type { UserRole } from '@/types/database';

export class AdminServices {
  
  async getDashboardStats(): Promise<DashboardStats> {
    try {
      // Get total patients
      const { count: totalPatients } = await supabase
        .from('user_profiles')
        .select('*', { count: 'exact', head: true })
        .eq('role', 'patient');

      // Get total users
      const { count: totalUsers } = await supabase
        .from('user_profiles')
        .select('*', { count: 'exact', head: true });

      // Get total appointments
      const { count: totalAppointments } = await supabase
        .from('appointments')
        .select('*', { count: 'exact', head: true });

      // Get total revenue from paid payments
      const { data: payments } = await supabase
        .from('payments')
        .select('amount')
        .eq('payment_status', 'paid');

      const totalRevenue = payments?.reduce((sum, payment) => sum + Number(payment.amount), 0) || 0;

      // Get active promotions
      const { count: activePromotions } = await supabase
        .from('promotions')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true)
        .gte('end_date', new Date().toISOString());

      return {
        totalPatients: totalPatients || 0,
        totalUsers: totalUsers || 0,
        totalAppointments: totalAppointments || 0,
        totalRevenue,
        activePromotions: activePromotions || 0,
        monthlyRevenue: Array(12).fill(0), // Placeholder
        appointmentsByStatus: {}, // Placeholder
        topServices: [], // Placeholder
        recentActivity: [] // Placeholder
      };
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      throw error;
    }
  }

  async createAdminUser(userData: AdminUserInput): Promise<{ success: boolean; error?: string }> {
    try {
      // First, create the user in Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: userData.email,
        password: 'temp-password-' + Math.random().toString(36).slice(-8),
        email_confirm: true,
        user_metadata: {
          role: userData.role,
          permissions: userData.permissions || {},
          department: userData.department,
          employee_id: userData.employee_id
        }
      });

      if (authError || !authData.user) {
        return { success: false, error: authError?.message || 'Failed to create user' };
      }

      // Create user profile
      const { error: profileError } = await supabase
        .from('user_profiles')
        .insert({
          id: authData.user.id,
          email: userData.email,
          first_name: '',
          last_name: '',
          role: userData.role as UserRole,
          is_active: true
        });

      if (profileError) {
        // Clean up auth user if profile creation fails
        await supabase.auth.admin.deleteUser(authData.user.id);
        return { success: false, error: profileError.message };
      }

      // Create admin user record using raw SQL query since admin_users might not be in the type system
      const { error: adminError } = await supabase
        .rpc('create_admin_user_record', {
          user_id: authData.user.id,
          user_role: userData.role,
          user_permissions: userData.permissions || {},
          user_department: userData.department,
          user_employee_id: userData.employee_id
        });

      if (adminError) {
        // Clean up if admin record creation fails
        await supabase.auth.admin.deleteUser(authData.user.id);
        await supabase.from('user_profiles').delete().eq('id', authData.user.id);
        return { success: false, error: adminError.message };
      }

      return { success: true };
    } catch (error) {
      console.error('Error creating admin user:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async updateAdminProfile(userId: string, profileData: Partial<AdminProfile>): Promise<{ success: boolean; error?: string }> {
    try {
      // Use raw SQL query for admin_profiles table
      const { error } = await supabase
        .rpc('update_admin_profile', {
          user_id: userId,
          profile_data: profileData
        });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      console.error('Error updating admin profile:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async getAdminUsers() {
    try {
      // Use raw SQL query to get admin users
      const { data, error } = await supabase
        .rpc('get_admin_users_with_profiles');

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching admin users:', error);
      return { data: null, error };
    }
  }

  async toggleAdminUserStatus(userId: string, isActive: boolean): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .rpc('toggle_admin_user_status', {
          user_id: userId,
          is_active: isActive
        });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      console.error('Error toggling admin user status:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }
}

export const adminServices = new AdminServices();
