
/**
 * Lullaby Clinic - Authentication Service
 * Comprehensive authentication and user management
 * 
 * @version 2.0.0
 * @updated 2024-12-19
 */

import { supabase } from './supabase/client';
import type { User, AuthError, Session } from '@supabase/supabase-js';
import type { UserProfile, UserProfileUpdate } from '@/types/database';

export interface SignUpData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  phone?: string;
  preferred_language?: string;
  marketing_consent?: boolean;
}

export interface SignInData {
  email: string;
  password: string;
}

export interface ResetPasswordData {
  email: string;
}

export interface UpdatePasswordData {
  password: string;
}

export interface ProfileUpdateData {
  first_name?: string;
  last_name?: string;
  phone?: string;
  address?: string;
  medical_history?: string;
  allergies?: string;
  current_medications?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  preferred_language?: string;
  marketing_consent?: boolean;
}

export interface AuthResponse {
  user: User | null;
  session: Session | null;
  error: AuthError | null;
}

export class AuthService {
  /**
   * Sign up a new user
   */
  async signUp(userData: SignUpData): Promise<AuthResponse> {
    try {
      const { data, error } = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          data: {
            first_name: userData.first_name,
            last_name: userData.last_name,
            phone: userData.phone || '',
            preferred_language: userData.preferred_language || 'th',
            marketing_consent: userData.marketing_consent || false,
            privacy_consent: true
          }
        }
      });

      if (error) {
        console.error('SignUp error:', error);
        return { user: null, session: null, error };
      }

      return { user: data.user, session: data.session, error: null };
    } catch (error) {
      console.error('Unexpected signup error:', error);
      return { 
        user: null, 
        session: null, 
        error: { message: 'An unexpected error occurred during signup' } 
      };
    }
  }

  /**
   * Sign in user
   */
  async signIn(credentials: SignInData): Promise<AuthResponse> {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (error) {
        console.error('SignIn error:', error);
        return { user: null, session: null, error };
      }

      // Update last login timestamp
      if (data.user) {
        await this.updateLastLogin(data.user.id);
      }

      return { user: data.user, session: data.session, error: null };
    } catch (error) {
      console.error('Unexpected signin error:', error);
      return { 
        user: null, 
        session: null, 
        error: { message: 'An unexpected error occurred during signin' } 
      };
    }
  }

  /**
   * Sign out user
   */
  async signOut(): Promise<{ error: AuthError | null }> {
    try {
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        console.error('SignOut error:', error);
        return { error };
      }

      return { error: null };
    } catch (error) {
      console.error('Unexpected signout error:', error);
      return { error: { message: 'An unexpected error occurred during signout' } as AuthError };
    }
  }

  /**
   * Reset password
   */
  async resetPassword(data: ResetPasswordData): Promise<{ error: AuthError | null }> {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(data.email, {
        redirectTo: `${window.location.origin}/auth?type=recovery`,
      });

      if (error) {
        console.error('Reset password error:', error);
        return { error };
      }

      return { error: null };
    } catch (error) {
      console.error('Unexpected reset password error:', error);
      return { error: { message: 'An unexpected error occurred during password reset' } as AuthError };
    }
  }

  /**
   * Update password
   */
  async updatePassword(data: UpdatePasswordData): Promise<{ error: AuthError | null }> {
    try {
      const { error } = await supabase.auth.updateUser({
        password: data.password
      });

      if (error) {
        console.error('Update password error:', error);
        return { error };
      }

      return { error: null };
    } catch (error) {
      console.error('Unexpected update password error:', error);
      return { error: { message: 'An unexpected error occurred during password update' } as AuthError };
    }
  }

  /**
   * Get current user profile
   */
  async getUserProfile(userId: string): Promise<{ data: UserProfile | null; error: Error | null }> {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Get user profile error:', error);
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Unexpected get user profile error:', error);
      return { data: null, error };
    }
  }

  /**
   * Update user profile
   */
  async updateUserProfile(userId: string, profileData: ProfileUpdateData): Promise<{ data: UserProfile | null; error: Error | null }> {
    try {
      // Prepare the update data with proper types
      const updateData: Partial<UserProfileUpdate> = {
        ...profileData,
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('user_profiles')
        .update(updateData)
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        console.error('Update user profile error:', error);
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Unexpected update user profile error:', error);
      return { data: null, error };
    }
  }

  /**
   * Update last login timestamp
   */
  private async updateLastLogin(userId: string): Promise<void> {
    try {
      await supabase
        .from('user_profiles')
        .update({ last_login: new Date().toISOString() })
        .eq('id', userId);
    } catch (error) {
      console.error('Error updating last login:', error);
      // Don't throw error as this is not critical
    }
  }

  /**
   * Check if user is admin
   */
  async isAdmin(userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .rpc('is_admin_user', { user_id: userId });

      if (error) {
        console.error('Error checking admin status:', error);
        return false;
      }

      return data || false;
    } catch (error) {
      console.error('Unexpected error checking admin status:', error);
      return false;
    }
  }

  /**
   * Get current session
   */
  async getSession() {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        console.error('Get session error:', error);
        return { session: null, error };
      }

      return { session, error: null };
    } catch (error) {
      console.error('Unexpected get session error:', error);
      return { session: null, error };
    }
  }

  /**
   * Get current user
   */
  async getCurrentUser() {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      
      if (error) {
        console.error('Get current user error:', error);
        return { user: null, error };
      }

      return { user, error: null };
    } catch (error) {
      console.error('Unexpected get current user error:', error);
      return { user: null, error };
    }
  }
}

// Export singleton instance
export const authService = new AuthService();

// Export utility functions
export const getCurrentUser = () => authService.getCurrentUser();
export const getSession = () => authService.getSession();
export const signIn = (credentials: SignInData) => authService.signIn(credentials);
export const signUp = (userData: SignUpData) => authService.signUp(userData);
export const signOut = () => authService.signOut();
export const resetPassword = (data: ResetPasswordData) => authService.resetPassword(data);
export const updatePassword = (data: UpdatePasswordData) => authService.updatePassword(data);
export const getUserProfile = (userId: string) => authService.getUserProfile(userId);
export const updateUserProfile = (userId: string, data: ProfileUpdateData) => authService.updateUserProfile(userId, data);
export const isAdmin = (userId: string) => authService.isAdmin(userId);
