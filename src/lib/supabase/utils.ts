
import { auth, storage, db } from './index';

// Utility functions
export const utils = {
  // Check if user is authenticated
  isAuthenticated: async () => {
    const { data: { user } } = await auth.getUser();
    return !!user;
  },
  
  // Get current user profile
  getCurrentUserProfile: async () => {
    const { data: { user } } = await auth.getUser();
    if (!user) return null;
    
    const { data, error } = await db.userProfiles()
      .select('*')
      .eq('id', user.id)
      .single();
    
    return { data, error };
  },
  
  // Check user role
  getUserRole: async () => {
    const { data } = await utils.getCurrentUserProfile();
    return data?.role || null;
  },
  
  // Upload file with error handling
  uploadFile: async (
    bucket: keyof typeof storage,
    path: string,
    file: File,
    options?: { upsert?: boolean }
  ) => {
    try {
      const { data, error } = await storage[bucket].upload(path, file, {
        cacheControl: '3600',
        upsert: options?.upsert || false
      });
      
      if (error) throw error;
      
      // Get public URL
      const { data: { publicUrl } } = storage[bucket].getPublicUrl(path);
      
      return { data: { ...data, publicUrl }, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },
  
  // Generate secure filename
  generateSecureFilename: (originalName: string, userId: string) => {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2);
    const extension = originalName.split('.').pop();
    return `${userId}/${timestamp}-${random}.${extension}`;
  },
  
  // Format currency
  formatCurrency: (amount: number, currency: string = 'THB') => {
    return new Intl.NumberFormat('th-TH', {
      style: 'currency',
      currency: currency
    }).format(amount);
  },
  
  // Format date for Thai locale
  formatDate: (date: string | Date, options?: Intl.DateTimeFormatOptions) => {
    const defaultOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      ...options
    };
    
    return new Intl.DateTimeFormat('th-TH', defaultOptions).format(
      typeof date === 'string' ? new Date(date) : date
    );
  },
  
  // Format time
  formatTime: (date: string | Date) => {
    return new Intl.DateTimeFormat('th-TH', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }).format(typeof date === 'string' ? new Date(date) : date);
  }
};
