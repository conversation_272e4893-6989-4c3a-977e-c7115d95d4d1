
import { supabase } from './client';

// Storage helpers
export const storage = {
  // Patient photos (before/after)
  patientPhotos: supabase.storage.from('patient-photos'),
  
  // Service images
  serviceImages: supabase.storage.from('service-images'),
  
  // Blog images
  blogImages: supabase.storage.from('blog-images'),
  
  // <PERSON> profiles
  doctorProfiles: supabase.storage.from('doctor-profiles'),
  
  // Documents (receipts, invoices)
  documents: supabase.storage.from('documents'),
  
  // Gallery images (before/after showcase)
  galleryImages: supabase.storage.from('gallery-images')
};

export const uploadFile = async (
  bucket: string,
  path: string,
  file: File
): Promise<{ data: any; error: any }> => {
  return await supabase.storage.from(bucket).upload(path, file);
};

export const deleteFile = async (
  bucket: string,
  path: string
): Promise<{ data: any; error: any }> => {
  return await supabase.storage.from(bucket).remove([path]);
};

export const getPublicUrl = (bucket: string, path: string): string => {
  const { data } = supabase.storage.from(bucket).getPublicUrl(path);
  return data.publicUrl;
};

export interface GalleryImageUpload {
  file: File;
  type: 'before' | 'after';
  category: string;
  treatmentId?: string;
}

export async function uploadGalleryImage(
  upload: GalleryImageUpload,
  onProgress?: (progress: number) => void
): Promise<string> {
  const fileExt = upload.file.name.split('.').pop();
  const fileName = `${upload.type}-${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
  const filePath = `gallery/${upload.type}/${upload.category}/${fileName}`;

  const { data, error } = await supabase.storage
    .from('gallery-images')
    .upload(filePath, upload.file, {
      cacheControl: '3600',
      upsert: false
    });

  if (error) {
    throw new Error(`Failed to upload image: ${error.message}`);
  }

  return data.path;
}

export async function uploadMultipleGalleryImages(
  uploads: GalleryImageUpload[],
  onProgress?: (progress: number) => void
): Promise<string[]> {
  const uploadPromises = uploads.map(async (upload, index) => {
    try {
      const path = await uploadGalleryImage(upload);
      if (onProgress) {
        onProgress(((index + 1) / uploads.length) * 100);
      }
      return path;
    } catch (error) {
      console.error(`Failed to upload image ${index + 1}:`, error);
      throw error;
    }
  });

  return Promise.all(uploadPromises);
}

export function getGalleryImageUrl(imagePath: string): string {
  if (!imagePath) return '/placeholder.svg';
  
  if (imagePath.startsWith('http')) {
    return imagePath;
  }

  return getPublicUrl('gallery-images', imagePath);
}

export async function deleteGalleryImage(imagePath: string): Promise<void> {
  const { error } = await supabase.storage
    .from('gallery-images')
    .remove([imagePath]);

  if (error) {
    throw new Error(`Failed to delete image: ${error.message}`);
  }
}

export async function listGalleryImages(
  folder?: string
): Promise<{ name: string; path: string; url: string }[]> {
  const { data, error } = await supabase.storage
    .from('gallery-images')
    .list(folder || 'gallery', {
      limit: 100,
      offset: 0
    });

  if (error) {
    throw new Error(`Failed to list images: ${error.message}`);
  }

  return (data || []).map(file => ({
    name: file.name,
    path: folder ? `${folder}/${file.name}` : `gallery/${file.name}`,
    url: getGalleryImageUrl(folder ? `${folder}/${file.name}` : `gallery/${file.name}`)
  }));
}

export function validateImageFile(file: File): { valid: boolean; error?: string } {
  const maxSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Invalid file type. Please upload JPEG, PNG, or WebP images.'
    };
  }

  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'File size too large. Please upload images smaller than 10MB.'
    };
  }

  return { valid: true };
}

export async function optimizeImage(file: File, maxWidth = 1200, quality = 0.8): Promise<File> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
      canvas.width = img.width * ratio;
      canvas.height = img.height * ratio;

      ctx?.drawImage(img, 0, 0, canvas.width, canvas.height);

      canvas.toBlob(
        (blob) => {
          if (blob) {
            const optimizedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            });
            resolve(optimizedFile);
          } else {
            reject(new Error('Failed to optimize image'));
          }
        },
        file.type,
        quality
      );
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}
