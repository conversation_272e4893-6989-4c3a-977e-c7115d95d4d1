
import { auth, db } from './index';

// Error handling wrapper
export const withErrorHandling = async <T>(
  operation: () => Promise<{ data: T; error: Error | null }>,
  context?: string
) => {
  try {
    const result = await operation();
    
    if (result.error) {
      console.error(`Supabase error${context ? ` in ${context}` : ''}:`, result.error);
      
      // Log to audit if it's a critical operation
      if (context && ['payment', 'appointment', 'user'].some(critical => context.includes(critical))) {
        await db.auditLogs().insert({
          table_name: context,
          record_id: 'unknown',
          action: 'ERROR',
          new_values: { error: result.error.message },
          user_id: (await auth.getUser()).data.user?.id
        });
      }
    }
    
    return result;
  } catch (error) {
    console.error(`Unexpected error${context ? ` in ${context}` : ''}:`, error);
    return { data: null, error };
  }
};
