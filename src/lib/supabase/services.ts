import { supabase } from '@/lib/supabase/client';

export interface Service {
  id: string;
  service_id: string;
  name: string;
  name_en?: string;
  name_zh?: string;
  price: number;
  category: 'botox' | 'botox-package' | 'filler' | 'zone';
  subcategory?: string;
  brand?: string;
  country?: string;
  units?: number;
  zone?: string;
  products?: Array<{ name: string; price: number }>;
  mock_image_prompt: string;
  description?: string;
  description_en?: string;
  description_zh?: string;
  is_featured: boolean;
  is_popular: boolean;
  display_order: number;
  created_at: string;
  updated_at: string;
}

export interface ServiceFilters {
  category?: string;
  is_featured?: boolean;
  is_popular?: boolean;
  search?: string;
}

export const servicesApi = {
  // Get all services with optional filtering
  async getServices(filters: ServiceFilters = {}): Promise<Service[]> {
    try {
      let query = supabase
        .from('services')
        .select('*')
        .order('display_order', { ascending: true });

      if (filters.category && filters.category !== 'all') {
        if (filters.category === 'botox-all') {
          query = query.in('category', ['botox', 'botox-package']);
        } else {
          query = query.eq('category', filters.category);
        }
      }

      if (filters.is_featured) {
        query = query.eq('is_featured', true);
      }

      if (filters.is_popular) {
        query = query.eq('is_popular', true);
      }

      if (filters.search) {
        query = query.or(`name.ilike.%${filters.search}%,name_en.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching services:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error in getServices:', error);
      throw error;
    }
  },

  // Get service by ID
  async getServiceById(id: string): Promise<Service | null> {
    try {
      const { data, error } = await supabase
        .from('services')
        .select('*')
        .eq('service_id', id)
        .single();

      if (error) {
        console.error('Error fetching service:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in getServiceById:', error);
      throw error;
    }
  },

  // Get services by category
  async getServicesByCategory(category: string): Promise<Service[]> {
    try {
      let query = supabase
        .from('services')
        .select('*')
        .order('display_order', { ascending: true });

      if (category === 'botox-all') {
        query = query.in('category', ['botox', 'botox-package']);
      } else {
        query = query.eq('category', category);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching services by category:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error in getServicesByCategory:', error);
      throw error;
    }
  },

  // Get featured services
  async getFeaturedServices(): Promise<Service[]> {
    try {
      const { data, error } = await supabase
        .from('services')
        .select('*')
        .eq('is_featured', true)
        .order('display_order', { ascending: true });

      if (error) {
        console.error('Error fetching featured services:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error in getFeaturedServices:', error);
      throw error;
    }
  },

  // Get popular services
  async getPopularServices(): Promise<Service[]> {
    try {
      const { data, error } = await supabase
        .from('services')
        .select('*')
        .eq('is_popular', true)
        .order('display_order', { ascending: true });

      if (error) {
        console.error('Error fetching popular services:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error in getPopularServices:', error);
      throw error;
    }
  },

  // Create new service (admin only)
  async createService(service: Omit<Service, 'id' | 'created_at' | 'updated_at'>): Promise<Service> {
    try {
      const { data, error } = await supabase
        .from('services')
        .insert([service])
        .select()
        .single();

      if (error) {
        console.error('Error creating service:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in createService:', error);
      throw error;
    }
  },

  // Update service (admin only)
  async updateService(id: string, updates: Partial<Service>): Promise<Service> {
    try {
      const { data, error } = await supabase
        .from('services')
        .update(updates)
        .eq('service_id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating service:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in updateService:', error);
      throw error;
    }
  },

  // Delete service (admin only)
  async deleteService(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('services')
        .delete()
        .eq('service_id', id);

      if (error) {
        console.error('Error deleting service:', error);
        throw error;
      }
    } catch (error) {
      console.error('Error in deleteService:', error);
      throw error;
    }
  },

  // Get service categories
  async getServiceCategories(): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from('services')
        .select('category')
        .order('category');

      if (error) {
        console.error('Error fetching service categories:', error);
        throw error;
      }

      const categories = [...new Set(data?.map(item => item.category) || [])];
      return categories;
    } catch (error) {
      console.error('Error in getServiceCategories:', error);
      throw error;
    }
  }
};

export default servicesApi;
