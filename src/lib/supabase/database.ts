
import { supabase } from './client';

// Database helpers with type safety
export const db = {
  // User profiles
  userProfiles: () => supabase.from('user_profiles'),
  doctors: () => supabase.from('doctors'),
  
  // Admin
  adminUsers: () => supabase.from('admin_users'),
  adminProfiles: () => supabase.from('admin_profiles'),
  
  // Services
  serviceCategories: () => supabase.from('service_categories'),
  services: () => supabase.from('services'),
  doctorServices: () => supabase.from('doctor_services'),
  
  // Appointments
  appointments: () => supabase.from('appointments'),
  appointmentSlots: () => supabase.from('appointment_slots'),
  
  // Payments
  payments: () => supabase.from('payments'),
  invoices: () => supabase.from('invoices'),
  
  // Reviews
  reviews: () => supabase.from('reviews'),
  
  // Content
  blogPosts: () => supabase.from('blog_posts'),
  blogCategories: () => supabase.from('blog_categories'),
  blogTags: () => supabase.from('blog_tags'),
  newsletterSubscribers: () => supabase.from('newsletter_subscribers'),
  
  // CMS
  cmsPages: () => supabase.from('cms_pages'),
  cmsPageCategories: () => supabase.from('cms_page_categories'),
  cmsPageTags: () => supabase.from('cms_page_tags'),
  cmsMedia: () => supabase.from('cms_media'),
  
  // Marketing
  promotions: () => supabase.from('promotions'),
  promotionUsage: () => supabase.from('promotion_usage'),
  marketingCampaigns: () => supabase.from('marketing_campaigns'),
  campaignRecipients: () => supabase.from('campaign_recipients'),
  emailTemplates: () => supabase.from('email_templates'),
  
  // Customer Management
  customerSegments: () => supabase.from('customer_segments'),
  segmentMemberships: () => supabase.from('segment_memberships'),
  customerInteractions: () => supabase.from('customer_interactions'),
  customerNotes: () => supabase.from('customer_notes'),
  
  // Analytics & KPIs
  analyticsEvents: () => supabase.from('analytics_events'),
  websiteMetrics: () => supabase.from('website_metrics'),
  kpiMetrics: () => supabase.from('kpi_metrics'),
  
  // Inventory
  inventoryItems: () => supabase.from('inventory_items'),
  inventoryTransactions: () => supabase.from('inventory_transactions'),
  
  // Notifications
  notifications: () => supabase.from('notifications'),
  
  // Settings
  clinicSettings: () => supabase.from('clinic_settings'),
  auditLogs: () => supabase.from('audit_logs')
};
