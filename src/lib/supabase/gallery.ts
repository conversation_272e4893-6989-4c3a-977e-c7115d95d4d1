import { supabase } from './client';

export interface BeforeAfterGalleryItem {
  id: string;
  title: string;
  description: string | null;
  category: string;
  treatment: string;
  duration: string | null;
  before_image_url: string;
  after_image_url: string;
  before_date: string;
  after_date: string;
  patient_age: number | null;
  patient_gender: 'male' | 'female' | null;
  difficulty: 'easy' | 'moderate' | 'complex';
  rating: number;
  featured: boolean;
  tags: string[];
  doctor_id: string | null;
  service_id: string | null;
  is_approved: boolean;
  is_active: boolean;
  view_count: number;
  like_count: number;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface GalleryFilters {
  category?: string;
  difficulty?: 'easy' | 'moderate' | 'complex';
  featured?: boolean;
  minRating?: number;
  dateRange?: {
    start: string;
    end: string;
  };
  tags?: string[];
}

export interface GallerySortOptions {
  field: 'rating' | 'created_at' | 'view_count' | 'like_count' | 'sort_order';
  direction: 'asc' | 'desc';
}

export async function fetchGalleryItems(
  filters?: GalleryFilters,
  sort?: GallerySortOptions,
  limit?: number,
  offset?: number
): Promise<{ data: BeforeAfterGalleryItem[]; count: number }> {
  let query = supabase
    .from('before_after_gallery')
    .select('*', { count: 'exact' })
    .eq('is_approved', true)
    .eq('is_active', true);

  if (filters?.category && filters.category !== 'all') {
    query = query.eq('category', filters.category);
  }

  if (filters?.difficulty) {
    query = query.eq('difficulty', filters.difficulty);
  }

  if (filters?.featured !== undefined) {
    query = query.eq('featured', filters.featured);
  }

  if (filters?.minRating) {
    query = query.gte('rating', filters.minRating);
  }

  if (filters?.dateRange) {
    query = query
      .gte('before_date', filters.dateRange.start)
      .lte('after_date', filters.dateRange.end);
  }

  if (filters?.tags && filters.tags.length > 0) {
    query = query.overlaps('tags', filters.tags);
  }

  if (sort) {
    query = query.order(sort.field, { ascending: sort.direction === 'asc' });
  } else {
    query = query.order('sort_order', { ascending: false })
                 .order('created_at', { ascending: false });
  }

  if (limit) {
    query = query.limit(limit);
  }

  if (offset) {
    query = query.range(offset, offset + (limit || 10) - 1);
  }

  const { data, error, count } = await query;

  if (error) {
    throw new Error(`Failed to fetch gallery items: ${error.message}`);
  }

  return {
    data: data || [],
    count: count || 0
  };
}

export async function fetchGalleryItemById(id: string): Promise<BeforeAfterGalleryItem | null> {
  const { data, error } = await supabase
    .from('before_after_gallery')
    .select('*')
    .eq('id', id)
    .eq('is_approved', true)
    .eq('is_active', true)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      return null;
    }
    throw new Error(`Failed to fetch gallery item: ${error.message}`);
  }

  return data;
}

export async function incrementViewCount(id: string): Promise<void> {
  const { error } = await supabase
    .from('before_after_gallery')
    .update({ view_count: supabase.raw('view_count + 1') })
    .eq('id', id);

  if (error) {
    console.error('Failed to increment view count:', error);
  }
}

export async function incrementLikeCount(id: string): Promise<void> {
  const { error } = await supabase
    .from('before_after_gallery')
    .update({ like_count: supabase.raw('like_count + 1') })
    .eq('id', id);

  if (error) {
    throw new Error(`Failed to increment like count: ${error.message}`);
  }
}

export async function getGalleryCategories(): Promise<string[]> {
  const { data, error } = await supabase
    .from('before_after_gallery')
    .select('category')
    .eq('is_approved', true)
    .eq('is_active', true);

  if (error) {
    throw new Error(`Failed to fetch categories: ${error.message}`);
  }

  const categories = [...new Set(data?.map(item => item.category) || [])];
  return categories.sort();
}

export async function getGalleryTags(): Promise<string[]> {
  const { data, error } = await supabase
    .from('before_after_gallery')
    .select('tags')
    .eq('is_approved', true)
    .eq('is_active', true);

  if (error) {
    throw new Error(`Failed to fetch tags: ${error.message}`);
  }

  const allTags = data?.flatMap(item => item.tags || []) || [];
  const uniqueTags = [...new Set(allTags)];
  return uniqueTags.sort();
}

export async function getGalleryStats(): Promise<{
  totalItems: number;
  totalViews: number;
  totalLikes: number;
  averageRating: number;
  categoryCounts: Record<string, number>;
}> {
  const { data, error } = await supabase
    .from('before_after_gallery')
    .select('category, view_count, like_count, rating')
    .eq('is_approved', true)
    .eq('is_active', true);

  if (error) {
    throw new Error(`Failed to fetch gallery stats: ${error.message}`);
  }

  const totalItems = data?.length || 0;
  const totalViews = data?.reduce((sum, item) => sum + (item.view_count || 0), 0) || 0;
  const totalLikes = data?.reduce((sum, item) => sum + (item.like_count || 0), 0) || 0;
  const averageRating = totalItems > 0 
    ? (data?.reduce((sum, item) => sum + item.rating, 0) || 0) / totalItems 
    : 0;

  const categoryCounts: Record<string, number> = {};
  data?.forEach(item => {
    categoryCounts[item.category] = (categoryCounts[item.category] || 0) + 1;
  });

  return {
    totalItems,
    totalViews,
    totalLikes,
    averageRating: Math.round(averageRating * 100) / 100,
    categoryCounts
  };
}

export async function createGalleryItem(item: Omit<BeforeAfterGalleryItem, 'id' | 'created_at' | 'updated_at' | 'view_count' | 'like_count'>): Promise<BeforeAfterGalleryItem> {
  const { data, error } = await supabase
    .from('before_after_gallery')
    .insert([item])
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create gallery item: ${error.message}`);
  }

  return data;
}

export async function updateGalleryItem(id: string, updates: Partial<BeforeAfterGalleryItem>): Promise<BeforeAfterGalleryItem> {
  const { data, error } = await supabase
    .from('before_after_gallery')
    .update(updates)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to update gallery item: ${error.message}`);
  }

  return data;
}

export async function deleteGalleryItem(id: string): Promise<void> {
  const { error } = await supabase
    .from('before_after_gallery')
    .delete()
    .eq('id', id);

  if (error) {
    throw new Error(`Failed to delete gallery item: ${error.message}`);
  }
}
