
import { supabase } from './client';

// Real-time subscriptions
export const realtime = {
  // Subscribe to appointment changes
  appointments: (callback: (payload: { new: Record<string, unknown>; old: Record<string, unknown>; eventType: string }) => void) => {
    return supabase
      .channel('appointments-changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'appointments'
      }, callback)
      .subscribe();
  },
  
  // Subscribe to new reviews
  reviews: (callback: (payload: { new: Record<string, unknown>; old: Record<string, unknown>; eventType: string }) => void) => {
    return supabase
      .channel('reviews-changes')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'reviews'
      }, callback)
      .subscribe();
  },
  
  // Subscribe to payment status changes
  payments: (callback: (payload: { new: Record<string, unknown>; old: Record<string, unknown>; eventType: string }) => void) => {
    return supabase
      .channel('payments-changes')
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'payments'
      }, callback)
      .subscribe();
  }
};
