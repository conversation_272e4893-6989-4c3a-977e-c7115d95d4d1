
import { Database } from '@/types/database';

// Type exports for convenience
export type { Database } from '@/types/database';
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row'];
export type Inserts<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert'];
export type Updates<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update'];

// Common type aliases
export type UserProfile = Tables<'user_profiles'>;
export type Doctor = Tables<'doctors'>;
export type Service = Tables<'services'>;
export type Appointment = Tables<'appointments'>;
export type Payment = Tables<'payments'>;
export type Review = Tables<'reviews'>;
export type BlogPost = Tables<'blog_posts'>;
export type Promotion = Tables<'promotions'>;
