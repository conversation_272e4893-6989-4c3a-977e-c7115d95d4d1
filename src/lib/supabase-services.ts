/**
 * Lullaby Clinic - Supabase Service Functions
 * High-level service functions for common database operations
 * 
 * @version 1.0.0
 * @created 2024-12-19
 */

import { 
  supabase, 
  db, 
  storage, 
  auth, 
  withErrorHandling,
  type UserProfile,
  type Doctor,
  type Service,
  type ServiceCategory,
  type Appointment,
  type Payment,
  type Review,
  type BlogPost,
  type Promotion,
  type NewsletterSubscriber
} from './supabase';

import { emailService } from './email-service';

import type {
  CreateAppointmentInput,
  BookingAvailability,
  BookingSlot,
  BookingRequest,
  ServiceWithDetails,
  DoctorWithDetails,
  AppointmentWithDetails,
  NewsletterSubscriptionInput,
  ContactFormInput,
  DashboardStats,
  ApiResponse,
  PaginatedResponse
} from '@/types';

// =============================================
// AUTHENTICATION SERVICES
// =============================================

export const authService = {
  // Sign up new patient
  signUp: async (email: string, password: string, userData: Partial<UserProfile>) => {
    return withErrorHandling(async () => {
      const { data: authData, error: authError } = await auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: userData.first_name,
            last_name: userData.last_name,
            phone: userData.phone,
            preferred_language: userData.preferred_language,
            marketing_consent: userData.marketing_consent,
            privacy_consent: userData.privacy_consent,
            date_of_birth: userData.date_of_birth,
            gender: userData.gender,
            country: userData.country
          }
        }
      });

      if (authError) return { data: null, error: authError };

      // Profile is automatically created by database trigger
      // Just return the auth data
      return { data: authData, error: null };
    }, 'auth.signUp');
  },

  // Sign in
  signIn: async (email: string, password: string) => {
    return withErrorHandling(async () => {
      const { data, error } = await auth.signInWithPassword({
        email,
        password
      });

      if (data.user) {
        // Update last login
        await db.userProfiles()
          .update({ last_login: new Date().toISOString() })
          .eq('id', data.user.id);
      }

      return { data, error };
    }, 'auth.signIn');
  },

  // Sign out
  signOut: async () => {
    return withErrorHandling(async () => {
      return await auth.signOut();
    }, 'auth.signOut');
  },

  // Reset password
  resetPassword: async (email: string) => {
    return withErrorHandling(async () => {
      return await auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      });
    }, 'auth.resetPassword');
  },

  // Update password
  updatePassword: async (password: string) => {
    return withErrorHandling(async () => {
      return await auth.updateUser({ password });
    }, 'auth.updatePassword');
  }
};

// =============================================
// USER PROFILE SERVICES
// =============================================

export const userService = {
  // Get user profile with details
  getProfile: async (userId: string) => {
    return withErrorHandling(async () => {
      return await db.userProfiles()
        .select('*')
        .eq('id', userId)
        .single();
    }, 'user.getProfile');
  },

  // Update user profile
  updateProfile: async (userId: string, updates: Partial<UserProfile>) => {
    return withErrorHandling(async () => {
      return await db.userProfiles()
        .update(updates)
        .eq('id', userId)
        .select()
        .single();
    }, 'user.updateProfile');
  },

  // Get user appointments
  getUserAppointments: async (userId: string, status?: string) => {
    return withErrorHandling(async () => {
      let query = db.appointments()
        .select(`
          *,
          doctor:doctors(*,
            user_profile:user_profiles(*)
          ),
          service:services(*),
          payment:payments(*)
        `)
        .eq('patient_id', userId)
        .order('appointment_date', { ascending: false });

      if (status) {
        query = query.eq('status', status);
      }

      return await query;
    }, 'user.getUserAppointments');
  }
};

// =============================================
// SERVICE MANAGEMENT
// =============================================

export const serviceService = {
  // Get all service categories
  getCategories: async () => {
    return withErrorHandling(async () => {
      return await db.serviceCategories()
        .select('*')
        .eq('is_active', true)
        .order('sort_order');
    }, 'service.getCategories');
  },

  // Get services with details
  getServices: async (categoryId?: string) => {
    return withErrorHandling(async () => {
      let query = db.services()
        .select(`
          *,
          category:service_categories(*),
          reviews:reviews(rating),
          doctors:doctor_services(
            doctor:doctors(*)
          )
        `)
        .eq('is_active', true);

      if (categoryId) {
        query = query.eq('category_id', categoryId);
      }

      const { data, error } = await query.order('sort_order');

      if (data) {
        // Calculate average ratings
        const servicesWithRatings = data.map(service => ({
          ...service,
          average_rating: service.reviews?.length 
            ? service.reviews.reduce((acc, r) => acc + r.rating, 0) / service.reviews.length
            : 0,
          review_count: service.reviews?.length || 0
        }));

        return { data: servicesWithRatings, error };
      }

      return { data, error };
    }, 'service.getServices');
  },

  // Get service by slug
  getServiceBySlug: async (slug: string): Promise<{ data: ServiceWithDetails | null; error: Error | null }> => {
    return withErrorHandling(async () => {
      return await db.services()
        .select(`
          *,
          category:service_categories(*),
          reviews:reviews(
            *,
            patient:user_profiles(first_name, last_name)
          ),
          doctors:doctor_services(
            *,
            doctor:doctors(
              *,
              user_profile:user_profiles(*)
            )
          )
        `)
        .eq('slug', slug)
        .eq('is_active', true)
        .single();
    }, 'service.getServiceBySlug');
  },

  // Search services
  searchServices: async (query: string, filters?: {
    category?: string;
    difficulty?: string;
    priceRange?: [number, number];
  }) => {
    return withErrorHandling(async () => {
      let dbQuery = db.services()
        .select(`
          *,
          category:service_categories(*)
        `)
        .eq('is_active', true);

      if (query) {
        dbQuery = dbQuery.or(`name.ilike.%${query}%,description.ilike.%${query}%`);
      }

      if (filters?.category) {
        dbQuery = dbQuery.eq('category_id', filters.category);
      }

      if (filters?.difficulty) {
        dbQuery = dbQuery.eq('difficulty', filters.difficulty);
      }

      if (filters?.priceRange) {
        dbQuery = dbQuery
          .gte('base_price', filters.priceRange[0])
          .lte('base_price', filters.priceRange[1]);
      }

      return await dbQuery.order('sort_order');
    }, 'service.searchServices');
  }
};

// =============================================
// DOCTOR SERVICES
// =============================================

export const doctorService = {
  // Get all doctors
  getDoctors: async () => {
    return withErrorHandling(async () => {
      return await db.doctors()
        .select(`
          *,
          user_profile:user_profiles(*),
          services:doctor_services(
            *,
            service:services(*)
          ),
          reviews:reviews(rating)
        `)
        .eq('is_available', true)
        .order('experience_years', { ascending: false });
    }, 'doctor.getDoctors');
  },

  // Get doctor availability
  getDoctorAvailability: async (
    doctorId: string, 
    startDate: string, 
    endDate: string
  ): Promise<{ data: BookingAvailability[] | null; error: Error | null }> => {
    return withErrorHandling(async () => {
      const { data: slots, error } = await db.appointmentSlots()
        .select('*')
        .eq('doctor_id', doctorId)
        .eq('is_available', true)
        .gte('start_time', startDate)
        .lte('end_time', endDate)
        .order('start_time');

      if (error) return { data: null, error };

      // Group slots by date
      const availabilityMap = new Map<string, BookingSlot[]>();
      
      slots?.forEach(slot => {
        const date = slot.start_time.split('T')[0];
        if (!availabilityMap.has(date)) {
          availabilityMap.set(date, []);
        }
        
        availabilityMap.get(date)!.push({
          datetime: slot.start_time,
          available: slot.is_available,
          doctor_id: slot.doctor_id,
          duration_minutes: 60, // Default duration
          price: 0 // Will be set from service
        });
      });

      const availability: BookingAvailability[] = Array.from(availabilityMap.entries())
        .map(([date, slots]) => ({ date, slots }));

      return { data: availability, error: null };
    }, 'doctor.getDoctorAvailability');
  }
};

// =============================================
// APPOINTMENT SERVICES
// =============================================

export const appointmentService = {
  // Create new appointment
  createAppointment: async (appointmentData: CreateAppointmentInput) => {
    return withErrorHandling(async () => {
      const { data: { user } } = await auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Create appointment
      const { data: appointment, error: appointmentError } = await db.appointments()
        .insert({
          ...appointmentData,
          patient_id: user.id,
          status: 'scheduled'
        })
        .select()
        .single();

      if (appointmentError) return { data: null, error: appointmentError };

      // Create initial payment record if deposit required
      if (appointmentData.deposit_amount && appointmentData.deposit_amount > 0) {
        await db.payments()
          .insert({
            appointment_id: appointment.id,
            patient_id: user.id,
            amount: appointmentData.deposit_amount,
            payment_method: 'card', // Default, will be updated
            payment_status: 'pending'
          });
      }

      // Send confirmation email automatically
      try {
        const { data: appointmentWithDetails } = await appointmentService.getAppointmentDetails(appointment.id);
        if (appointmentWithDetails) {
          const language = appointmentWithDetails.patient?.preferred_language === 'en' ? 'en' : 'th';
          await emailService.sendAppointmentConfirmation(appointmentWithDetails, language);
        }
      } catch (emailError) {
        console.error('Failed to send confirmation email:', emailError);
        // Don't fail the appointment creation if email fails
      }

      return { data: appointment, error: null };
    }, 'appointment.createAppointment');
  },

  // Update appointment status
  updateAppointmentStatus: async (appointmentId: string, status: string, notes?: string) => {
    return withErrorHandling(async () => {
      const updates: Record<string, unknown> = { status };
      
      if (status === 'confirmed') {
        updates.confirmation_sent_at = new Date().toISOString();
      } else if (status === 'completed') {
        updates.completed_at = new Date().toISOString();
      } else if (status === 'cancelled') {
        updates.cancelled_at = new Date().toISOString();
        if (notes) updates.cancellation_reason = notes;
      }

      const { data: updatedAppointment, error: updateError } = await db.appointments()
        .update(updates)
        .eq('id', appointmentId)
        .select()
        .single();

      if (updateError) return { data: null, error: updateError };

      // Send cancellation email if appointment was cancelled
      if (status === 'cancelled') {
        try {
          const { data: appointmentWithDetails } = await appointmentService.getAppointmentDetails(appointmentId);
          if (appointmentWithDetails) {
            const language = appointmentWithDetails.patient?.preferred_language === 'en' ? 'en' : 'th';
            await emailService.sendAppointmentCancellation(appointmentWithDetails, language);
          }
        } catch (emailError) {
          console.error('Failed to send cancellation email:', emailError);
          // Don't fail the status update if email fails
        }
      }

      return { data: updatedAppointment, error: null };
    }, 'appointment.updateAppointmentStatus');
  },

  // Get appointment with full details
  getAppointmentDetails: async (appointmentId: string): Promise<{ data: AppointmentWithDetails | null; error: Error | null }> => {
    return withErrorHandling(async () => {
      return await db.appointments()
        .select(`
          *,
          patient:user_profiles(*),
          doctor:doctors(
            *,
            user_profile:user_profiles(*)
          ),
          service:services(*),
          payment:payments(*),
          review:reviews(*)
        `)
        .eq('id', appointmentId)
        .single();
    }, 'appointment.getAppointmentDetails');
  }
};

// =============================================
// PAYMENT SERVICES
// =============================================

export const paymentService = {
  // Create payment intent (for Stripe integration)
  createPaymentIntent: async (appointmentId: string, amount: number) => {
    return withErrorHandling(async () => {
      // This would integrate with Stripe or other payment processor
      // For now, create payment record
      const { data: { user } } = await auth.getUser();
      if (!user) throw new Error('User not authenticated');

      return await db.payments()
        .insert({
          appointment_id: appointmentId,
          patient_id: user.id,
          amount,
          payment_method: 'card',
          payment_status: 'pending'
        })
        .select()
        .single();
    }, 'payment.createPaymentIntent');
  },

  // Update payment status
  updatePaymentStatus: async (paymentId: string, status: string, transactionId?: string) => {
    return withErrorHandling(async () => {
      const updates: Record<string, unknown> = { payment_status: status };
      
      if (status === 'paid') {
        updates.payment_date = new Date().toISOString();
      }
      
      if (transactionId) {
        updates.transaction_id = transactionId;
      }

      return await db.payments()
        .update(updates)
        .eq('id', paymentId)
        .select()
        .single();
    }, 'payment.updatePaymentStatus');
  }
};

// =============================================
// REVIEW SERVICES
// =============================================

export const reviewService = {
  // Create review
  createReview: async (reviewData: {
    appointment_id: string;
    doctor_id: string;
    service_id: string;
    rating: number;
    title?: string;
    comment?: string;
    is_anonymous?: boolean;
  }) => {
    return withErrorHandling(async () => {
      const { data: { user } } = await auth.getUser();
      if (!user) throw new Error('User not authenticated');

      return await db.reviews()
        .insert({
          ...reviewData,
          patient_id: user.id,
          is_approved: false // Requires admin approval
        })
        .select()
        .single();
    }, 'review.createReview');
  },

  // Get reviews for service
  getServiceReviews: async (serviceId: string, limit: number = 10) => {
    return withErrorHandling(async () => {
      return await db.reviews()
        .select(`
          *,
          patient:user_profiles(first_name, last_name),
          doctor:doctors(
            user_profile:user_profiles(first_name, last_name)
          )
        `)
        .eq('service_id', serviceId)
        .eq('is_approved', true)
        .order('created_at', { ascending: false })
        .limit(limit);
    }, 'review.getServiceReviews');
  }
};

// =============================================
// NEWSLETTER SERVICES
// =============================================

export const newsletterService = {
  // Subscribe to newsletter
  subscribe: async (subscriptionData: NewsletterSubscriptionInput) => {
    return withErrorHandling(async () => {
      return await db.newsletterSubscribers()
        .upsert({
          ...subscriptionData,
          is_active: true,
          subscribed_at: new Date().toISOString()
        })
        .select()
        .single();
    }, 'newsletter.subscribe');
  },

  // Unsubscribe
  unsubscribe: async (email: string) => {
    return withErrorHandling(async () => {
      return await db.newsletterSubscribers()
        .update({
          is_active: false,
          unsubscribed_at: new Date().toISOString()
        })
        .eq('email', email)
        .select()
        .single();
    }, 'newsletter.unsubscribe');
  }
};

// =============================================
// CONTENT SERVICES
// =============================================

export const contentService = {
  // Get blog posts
  getBlogPosts: async (filters?: {
    category?: string;
    difficulty?: string;
    language?: string;
    featured?: boolean;
    limit?: number;
  }) => {
    return withErrorHandling(async () => {
      let query = db.blogPosts()
        .select(`
          *,
          author:user_profiles(first_name, last_name)
        `)
        .eq('is_published', true);

      if (filters?.category) {
        query = query.eq('category', filters.category);
      }

      if (filters?.difficulty) {
        query = query.eq('difficulty', filters.difficulty);
      }

      if (filters?.language) {
        query = query.eq('language', filters.language);
      }

      if (filters?.featured) {
        query = query.eq('is_featured', true);
      }

      return await query
        .order('published_at', { ascending: false })
        .limit(filters?.limit || 50);
    }, 'content.getBlogPosts');
  },

  // Get blog post by slug
  getBlogPostBySlug: async (slug: string) => {
    return withErrorHandling(async () => {
      const { data, error } = await db.blogPosts()
        .select(`
          *,
          author:user_profiles(first_name, last_name)
        `)
        .eq('slug', slug)
        .eq('is_published', true)
        .single();

      // Increment view count
      if (data) {
        await db.blogPosts()
          .update({ view_count: (data.view_count || 0) + 1 })
          .eq('id', data.id);
      }

      return { data, error };
    }, 'content.getBlogPostBySlug');
  },

  // Search blog posts
  searchBlogPosts: async (query: string, filters?: {
    category?: string;
    difficulty?: string;
  }) => {
    return withErrorHandling(async () => {
      let dbQuery = db.blogPosts()
        .select('*')
        .eq('is_published', true);

      if (query) {
        dbQuery = dbQuery.or(`title.ilike.%${query}%,excerpt.ilike.%${query}%,content.ilike.%${query}%`);
      }

      if (filters?.category) {
        dbQuery = dbQuery.eq('category', filters.category);
      }

      if (filters?.difficulty) {
        dbQuery = dbQuery.eq('difficulty', filters.difficulty);
      }

      return await dbQuery.order('published_at', { ascending: false });
    }, 'content.searchBlogPosts');
  }
};

// =============================================
// PROMOTION SERVICES
// =============================================

export const promotionService = {
  // Get active promotions
  getActivePromotions: async () => {
    return withErrorHandling(async () => {
      const now = new Date().toISOString();
      
      return await db.promotions()
        .select('*')
        .eq('is_active', true)
        .lte('start_date', now)
        .gte('end_date', now)
        .order('is_featured', { ascending: false });
    }, 'promotion.getActivePromotions');
  },

  // Validate promo code
  validatePromoCode: async (promoCode: string, serviceIds?: string[]) => {
    return withErrorHandling(async () => {
      const now = new Date().toISOString();
      
      const { data: promotion, error } = await db.promotions()
        .select('*')
        .eq('promo_code', promoCode)
        .eq('is_active', true)
        .lte('start_date', now)
        .gte('end_date', now)
        .single();

      if (error || !promotion) {
        return { data: null, error: 'Invalid promo code' };
      }

      // Check usage limit
      if (promotion.usage_limit && promotion.usage_count >= promotion.usage_limit) {
        return { data: null, error: 'Promo code usage limit exceeded' };
      }

      // Check applicable services
      if (serviceIds && promotion.applicable_services) {
        const hasApplicableService = serviceIds.some(id => 
          promotion.applicable_services?.includes(id)
        );
        
        if (!hasApplicableService) {
          return { data: null, error: 'Promo code not applicable to selected services' };
        }
      }

      return { data: promotion, error: null };
    }, 'promotion.validatePromoCode');
  }
};

// =============================================
// DASHBOARD SERVICES
// =============================================

export const dashboardService = {
  // Get dashboard statistics
  getStats: async (): Promise<{ data: DashboardStats | null; error: Error | null }> => {
    return withErrorHandling(async () => {
      const { data: { user } } = await auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Get user role to determine what stats to show
      const { data: profile } = await userService.getProfile(user.id);
      
      if (profile?.role === 'patient') {
        // Patient dashboard stats
        const [appointments, reviews] = await Promise.all([
          db.appointments().select('status').eq('patient_id', user.id),
          db.reviews().select('rating').eq('patient_id', user.id)
        ]);

        const stats: DashboardStats = {
          total_appointments: appointments.data?.length || 0,
          upcoming_appointments: appointments.data?.filter(a => a.status === 'scheduled' || a.status === 'confirmed').length || 0,
          completed_appointments: appointments.data?.filter(a => a.status === 'completed').length || 0,
          total_patients: 0, // Not applicable for patient
          total_revenue: 0, // Not applicable for patient
          average_rating: 0, // Not applicable for patient
          pending_reviews: appointments.data?.filter(a => a.status === 'completed').length || 0 - (reviews.data?.length || 0),
          newsletter_subscribers: 0 // Not applicable for patient
        };

        return { data: stats, error: null };
      } else {
        // Admin/Doctor dashboard stats
        const [appointments, patients, payments, reviews, subscribers] = await Promise.all([
          db.appointments().select('status, total_amount'),
          db.userProfiles().select('id').eq('role', 'patient'),
          db.payments().select('amount').eq('payment_status', 'paid'),
          db.reviews().select('rating, is_approved'),
          db.newsletterSubscribers().select('id').eq('is_active', true)
        ]);

        const stats: DashboardStats = {
          total_appointments: appointments.data?.length || 0,
          upcoming_appointments: appointments.data?.filter(a => a.status === 'scheduled' || a.status === 'confirmed').length || 0,
          completed_appointments: appointments.data?.filter(a => a.status === 'completed').length || 0,
          total_patients: patients.data?.length || 0,
          total_revenue: payments.data?.reduce((sum, p) => sum + (p.amount || 0), 0) || 0,
          average_rating: reviews.data?.length 
            ? reviews.data.reduce((sum, r) => sum + (r.rating || 0), 0) / reviews.data.length 
            : 0,
          pending_reviews: reviews.data?.filter(r => !r.is_approved).length || 0,
          newsletter_subscribers: subscribers.data?.length || 0
        };

        return { data: stats, error: null };
      }
    }, 'dashboard.getStats');
  }
};

// Services are already exported individually above