{"project": {"name": "Lullaby Clinic Homepage", "version": "1.0.0", "description": "Next.js 15.3.3 medical clinic website with multilingual support", "tech_stack": {"framework": "Next.js 14", "language": "TypeScript", "styling": "TailwindCSS v3", "ui_library": "shadcn/ui", "build_tool": "Vite", "database": "Supabase", "i18n": "next-i18next", "testing": "Playwright"}, "supported_languages": ["th", "en", "zh"], "primary_color": "#EF476F"}, "structure": {"src": {"main_files": ["main.tsx", "App.tsx", "vite-env.d.ts"], "pages": ["Index.tsx", "NotFound.tsx"], "components": {"business_logic": ["Navigation.tsx", "HeroSection.tsx", "ServicesSection.tsx", "BeforeAfterGallery.tsx", "TestimonialsSection.tsx", "BlogSection.tsx", "Footer.tsx", "LanguageSelector.tsx", "FontDemo.tsx"], "ui": {"form_inputs": ["button.tsx", "input.tsx", "textarea.tsx", "checkbox.tsx", "radio-group.tsx", "select.tsx", "form.tsx", "label.tsx", "switch.tsx", "slider.tsx", "input-otp.tsx"], "layout_navigation": ["card.tsx", "sheet.tsx", "dialog.tsx", "alert-dialog.tsx", "drawer.tsx", "popover.tsx", "hover-card.tsx", "tooltip.tsx", "navigation-menu.tsx", "menubar.tsx", "breadcrumb.tsx", "pagination.tsx", "sidebar.tsx"], "data_display": ["table.tsx", "accordion.tsx", "tabs.tsx", "calendar.tsx", "carousel.tsx", "chart.tsx", "progress.tsx", "badge.tsx", "avatar.tsx", "separator.tsx", "skeleton.tsx"], "feedback": ["alert.tsx", "toast.tsx", "toaster.tsx", "command.tsx", "context-menu.tsx", "dropdown-menu.tsx"], "utility": ["scroll-area.tsx", "resizable.tsx", "collapsible.tsx", "toggle.tsx", "toggle-group.tsx", "aspect-ratio.tsx", "sonner.tsx"]}}, "contexts": ["LanguageContext.tsx"], "hooks": ["use-mobile.tsx", "use-toast.ts"], "utils": ["lib/utils.ts", "utils/translations.ts"]}, "public": ["logo.svg"], "config_files": ["package.json", "tailwind.config.ts", "tsconfig.json", "vite.config.ts", ".cursorrules", "components.json"]}, "key_features": {"Navigation.tsx": {"purpose": "Header navigation with logo, menu, language selector", "logo_usage": "Uses /logo.svg", "features": ["Multi-language menu", "Mobile hamburger", "Contact info", "Booking CTA"]}, "HeroSection.tsx": {"purpose": "Main banner with booking CTA", "features": ["Background banner", "Booking modal", "Responsive design"]}, "ServicesSection.tsx": {"purpose": "Medical services showcase", "features": ["Service cards", "Descriptions", "Hover effects"]}, "LanguageContext.tsx": {"purpose": "Internationalization management", "languages": ["th", "en", "zh"], "fallback": "en"}}, "development_rules": {"code_style": {"indent": "2 spaces", "max_line_length": 100, "quotes": "single", "semicolons": "always", "component_naming": "PascalCase", "function_naming": "camelCase"}, "imports": {"alias": "@/", "style": "absolute_only"}, "ui_guidelines": {"cards": "rounded-xl + shadow-lg", "spacing": "Tailwind multiples of 2", "motion": "framer-motion, 200ms ease-in-out"}}, "quick_commands": {"development": "npm run dev", "build": "npm run build", "preview": "npm run preview", "lint": "npm run lint", "explore": "npm run explore", "structure": "npm run explore:structure", "components": "npm run explore:components"}, "search_patterns": {"find_component": "find ./src/components -name '*.tsx' | grep -i", "find_ui_component": "find ./src/components/ui -name '*.tsx' | grep -i", "find_by_content": "find ./src -name '*.tsx' -o -name '*.ts' | xargs grep -l", "list_pages": "find ./src/pages -name '*.tsx'", "list_contexts": "find ./src/contexts -name '*.tsx'", "list_hooks": "find ./src/hooks -name '*.ts*'"}, "common_file_locations": {"logo": "public/logo.svg", "main_app": "src/App.tsx", "entry_point": "src/main.tsx", "homepage": "src/pages/Index.tsx", "navigation": "src/components/Navigation.tsx", "language_context": "src/contexts/LanguageContext.tsx", "translations": "src/utils/translations.ts", "tailwind_config": "tailwind.config.ts", "typescript_config": "tsconfig.json"}}