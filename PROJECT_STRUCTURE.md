# Lullaby Clinic - Project Structure Index

This document provides a comprehensive overview of the project structure to avoid repeated exploration and improve development efficiency.

## Project Overview
- **Framework**: Next.js 15.3.3 with TypeScript
- **Styling**: TailwindCSS v3
- **UI Components**: shadcn/ui
- **Build Tool**: Vite
- **Package Manager**: npm/bun

## Root Directory Structure

```
lullaby-homepage-checks/
├── public/                 # Static assets
│   └── logo.svg           # Main clinic logo (SVG format)
├── src/                   # Source code
├── dist/                  # Build output
├── node_modules/          # Dependencies
├── .cursorrules          # Project-specific AI rules
├── .gitignore            # Git ignore patterns
├── package.json          # Dependencies and scripts
├── tailwind.config.ts    # Tailwind CSS configuration
├── tsconfig.json         # TypeScript configuration
├── vite.config.ts        # Vite build configuration
└── README.md             # Project documentation
```

## Source Code Structure (`src/`)

### Main Application Files
- `main.tsx` - Application entry point
- `App.tsx` - Root App component
- `vite-env.d.ts` - Vite environment types

### Pages (`src/pages/`)
- `Index.tsx` - Homepage/landing page
- `NotFound.tsx` - 404 error page

### Components (`src/components/`)

#### Main Components (Business Logic)
- `Navigation.tsx` - Header navigation with logo, menu, language selector
- `HeroSection.tsx` - Main banner with booking CTA
- `ServicesSection.tsx` - Medical services showcase
- `BeforeAfterGallery.tsx` - Before/after treatment photos
- `TestimonialsSection.tsx` - Customer reviews and testimonials
- `BlogSection.tsx` - Blog posts and articles
- `Footer.tsx` - Site footer with links and brand logos
- `LanguageSelector.tsx` - Language switching component
- `FontDemo.tsx` - Typography demonstration component

#### UI Components (`src/components/ui/`)
**Form & Input Components:**
- `button.tsx` - Button variants and styles
- `input.tsx` - Text input fields
- `textarea.tsx` - Multi-line text input
- `checkbox.tsx` - Checkbox input
- `radio-group.tsx` - Radio button groups
- `select.tsx` - Dropdown select
- `form.tsx` - Form wrapper and validation
- `label.tsx` - Form labels
- `switch.tsx` - Toggle switch
- `slider.tsx` - Range slider
- `input-otp.tsx` - OTP input fields

**Layout & Navigation:**
- `card.tsx` - Card containers
- `sheet.tsx` - Side panels
- `dialog.tsx` - Modal dialogs
- `alert-dialog.tsx` - Confirmation dialogs
- `drawer.tsx` - Mobile-friendly drawers
- `popover.tsx` - Floating content
- `hover-card.tsx` - Hover-triggered cards
- `tooltip.tsx` - Hover tooltips
- `navigation-menu.tsx` - Complex navigation
- `menubar.tsx` - Menu bar component
- `breadcrumb.tsx` - Breadcrumb navigation
- `pagination.tsx` - Page navigation
- `sidebar.tsx` - Sidebar navigation

**Data Display:**
- `table.tsx` - Data tables
- `accordion.tsx` - Collapsible content
- `tabs.tsx` - Tab navigation
- `calendar.tsx` - Date picker
- `carousel.tsx` - Image/content carousel
- `chart.tsx` - Data visualization
- `progress.tsx` - Progress indicators
- `badge.tsx` - Status badges
- `avatar.tsx` - User avatars
- `separator.tsx` - Visual dividers
- `skeleton.tsx` - Loading placeholders

**Feedback & Interaction:**
- `alert.tsx` - Alert messages
- `toast.tsx` - Toast notifications
- `toaster.tsx` - Toast container
- `use-toast.ts` - Toast hook
- `command.tsx` - Command palette
- `context-menu.tsx` - Right-click menus
- `dropdown-menu.tsx` - Dropdown menus

**Utility Components:**
- `scroll-area.tsx` - Custom scrollbars
- `resizable.tsx` - Resizable panels
- `collapsible.tsx` - Collapsible sections
- `toggle.tsx` - Toggle buttons
- `toggle-group.tsx` - Toggle button groups
- `aspect-ratio.tsx` - Aspect ratio containers
- `sonner.tsx` - Advanced toast system

### Contexts (`src/contexts/`)
- `LanguageContext.tsx` - Internationalization context (th/en/zh support)

### Hooks (`src/hooks/`)
- `use-mobile.tsx` - Mobile device detection
- `use-toast.ts` - Toast notification management

### Utilities (`src/lib/` & `src/utils/`)
- `utils.ts` - General utility functions (lib)
- `translations.ts` - Translation management (utils)

## Key Features by Component

### Navigation.tsx
- Logo display (uses `/logo.svg`)
- Multi-language menu (Thai, English, Chinese)
- Mobile responsive hamburger menu
- Contact information in header
- Booking CTA button

### HeroSection.tsx
- Main banner with background
- Booking modal integration
- Call-to-action buttons
- Responsive design

### ServicesSection.tsx
- Medical service cards
- Service descriptions
- Interactive hover effects

### LanguageContext.tsx
- Supports: Thai (th), English (en), Chinese (zh)
- Fallback language: English
- Translation key management

## Configuration Files

### Styling & Build
- `tailwind.config.ts` - Custom colors, fonts, animations
- `postcss.config.js` - PostCSS processing
- `components.json` - shadcn/ui configuration

### TypeScript
- `tsconfig.json` - Main TS config
- `tsconfig.app.json` - App-specific TS config
- `tsconfig.node.json` - Node.js TS config

### Development Rules
- `.cursorrules` - AI assistant project rules
- Primary color: `#EF476F`
- Font families: Kanit (Thai), Poppins (English/Chinese)
- Component naming: PascalCase
- Import alias: `@/` for src directory

## Asset Management
- Static assets in `public/` directory
- Logo: `public/logo.svg` (vector format)
- Images served from root path (e.g., `/logo.svg`)

## Development Workflow
1. Components follow atomic design principles
2. All text content uses translation keys
3. Responsive-first design approach
4. TypeScript strict mode enabled
5. ESLint for code quality

## Quick Reference Commands
```bash
# Install dependencies
npm install

# Development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Lint code
npm run lint
```

---

**Last Updated**: Generated automatically
**Maintainer**: Development Team
**Purpose**: Reduce exploration time and improve development efficiency