#!/bin/bash

# Lullaby Clinic - Status Update Script
# Usage: ./scripts/update-status.sh [phase] [status] [description]

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Default values
PHASE=${1:-""}
STATUS=${2:-""}
DESCRIPTION=${3:-""}
DATE=$(date "+%B %d, %Y")

echo -e "${BLUE}🔄 Lullaby Clinic - Status Update Tool${NC}"
echo -e "${BLUE}======================================${NC}"

# If no arguments provided, show current status
if [ -z "$PHASE" ]; then
    echo -e "\n${GREEN}📊 Current Implementation Status:${NC}"
    echo ""
    
    # Count completed phases
    COMPLETED_PHASES=$(grep -c "✅ Complete" STATUS.md 2>/dev/null || echo "0")
    PENDING_PHASES=$(grep -c "⏳ Pending" STATUS.md 2>/dev/null || echo "0")
    TOTAL_PHASES=$((COMPLETED_PHASES + PENDING_PHASES))
    
    echo -e "📈 Progress: ${GREEN}${COMPLETED_PHASES}${NC}/${TOTAL_PHASES} phases complete"
    echo ""
    
    # Show phase status
    echo -e "${GREEN}Completed Phases:${NC}"
    grep "✅ Complete" STATUS.md | sed 's/|.*//' | sed 's/\*\*//' | sed 's/\*\*//' | while read line; do
        echo -e "  ✅ $line"
    done
    
    echo ""
    echo -e "${YELLOW}Pending Phases:${NC}"
    grep "⏳ Pending" STATUS.md | sed 's/|.*//' | sed 's/\*\*//' | sed 's/\*\*//' | while read line; do
        echo -e "  ⏳ $line"
    done
    
    echo ""
    echo -e "${BLUE}Usage:${NC}"
    echo -e "  ${YELLOW}./scripts/update-status.sh${NC} [phase] [status] [description]"
    echo ""
    echo -e "${BLUE}Examples:${NC}"
    echo -e "  ${YELLOW}./scripts/update-status.sh${NC} 4 'In Progress' 'Setting up Supabase'"
    echo -e "  ${YELLOW}./scripts/update-status.sh${NC} 4 'Complete' 'Backend integration finished'"
    echo ""
    exit 0
fi

# Update status function
update_status() {
    local phase=$1
    local status=$2
    local desc=$3
    
    echo -e "\n${GREEN}🔄 Updating Phase $phase status...${NC}"
    
    # Create backup
    cp STATUS.md STATUS.md.bak
    
    # Status emoji mapping
    case $status in
        "Complete"|"complete"|"✅")
            STATUS_EMOJI="✅ Complete"
            STATUS_COLOR="${GREEN}"
            ;;
        "In Progress"|"in-progress"|"🔄")
            STATUS_EMOJI="🔄 In Progress"
            STATUS_COLOR="${YELLOW}"
            ;;
        "Pending"|"pending"|"⏳")
            STATUS_EMOJI="⏳ Pending"
            STATUS_COLOR="${YELLOW}"
            ;;
        "Blocked"|"blocked"|"🚫")
            STATUS_EMOJI="🚫 Blocked"
            STATUS_COLOR="${RED}"
            ;;
        *)
            STATUS_EMOJI="$status"
            STATUS_COLOR="${NC}"
            ;;
    esac
    
    # Update the phase status in the table
    sed -i.tmp "s/\*\*Phase $phase\*\* | [^|]* |/\*\*Phase $phase\*\* | $STATUS_EMOJI |/" STATUS.md
    
    # Update last modified date
    sed -i.tmp "s/\*\*Last Updated\*\*:.*/\*\*Last Updated\*\*: $DATE/" STATUS.md
    
    # Add to change log if complete
    if [[ "$status" == "Complete" || "$status" == "complete" || "$status" == "✅" ]]; then
        # Get current version
        CURRENT_VERSION=$(grep "Current Version" STATUS.md | grep -o "v[0-9]*\.[0-9]*" | head -1)
        if [[ -z "$CURRENT_VERSION" ]]; then
            NEW_VERSION="v${phase}.0"
        else
            MAJOR=$(echo $CURRENT_VERSION | cut -d'.' -f1 | sed 's/v//')
            if [ "$phase" -gt "$MAJOR" ]; then
                NEW_VERSION="v${phase}.0"
            else
                MINOR=$(echo $CURRENT_VERSION | cut -d'.' -f2)
                NEW_MINOR=$((MINOR + 1))
                NEW_VERSION="v${MAJOR}.${NEW_MINOR}"
            fi
        fi
        
        # Update current version
        sed -i.tmp "s/\*\*Current Version\*\*:.*/\*\*Current Version\*\*: $NEW_VERSION (Phase $phase Complete)/" STATUS.md
        
        # Add to change log
        CHANGELOG_ENTRY="### **$NEW_VERSION - Phase $phase Complete** ($DATE)"
        if [ -n "$desc" ]; then
            CHANGELOG_ENTRY="$CHANGELOG_ENTRY\n- ✅ $desc"
        fi
        
        # Insert into change log section
        sed -i.tmp "/## 🔄 \*\*Change Log\*\*/a\\
\\
$CHANGELOG_ENTRY" STATUS.md
    fi
    
    # Clean up temp files
    rm -f STATUS.md.tmp STATUS.md.bak
    
    echo -e "${STATUS_COLOR}✅ Phase $phase status updated to: $STATUS_EMOJI${NC}"
    if [ -n "$desc" ]; then
        echo -e "📝 Description: $desc"
    fi
}

# Git integration function
git_commit_status() {
    echo -e "\n${BLUE}📝 Git Integration${NC}"
    read -p "Commit status update to git? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        git add STATUS.md
        git commit -m "docs: update phase $PHASE status to $STATUS

- Phase $PHASE: $STATUS
- Description: $DESCRIPTION
- Updated: $DATE"
        echo -e "${GREEN}✅ Status update committed to git${NC}"
    fi
}

# Progress tracking
show_progress() {
    echo -e "\n${BLUE}📊 Implementation Progress${NC}"
    
    # Calculate completion percentage
    TOTAL_FEATURES=$(grep -c "- \[" STATUS.md 2>/dev/null || echo "0")
    COMPLETED_FEATURES=$(grep -c "- \[x\]" STATUS.md 2>/dev/null || echo "0")
    
    if [ "$TOTAL_FEATURES" -gt 0 ]; then
        PERCENTAGE=$((COMPLETED_FEATURES * 100 / TOTAL_FEATURES))
        echo -e "Progress: ${GREEN}${COMPLETED_FEATURES}${NC}/${TOTAL_FEATURES} features (${GREEN}${PERCENTAGE}%${NC})"
        
        # Visual progress bar
        FILLED=$((PERCENTAGE / 5))
        EMPTY=$((20 - FILLED))
        printf "["
        for i in $(seq 1 $FILLED); do printf "█"; done
        for i in $(seq 1 $EMPTY); do printf "░"; done
        printf "] ${PERCENTAGE}%%\n"
    fi
    
    echo ""
}

# Project metrics
show_metrics() {
    echo -e "\n${BLUE}📈 Project Metrics${NC}"
    
    # Count files
    COMPONENTS=$(find src/components -name "*.tsx" 2>/dev/null | wc -l | tr -d ' ')
    TOTAL_FILES=$(find src -name "*.tsx" -o -name "*.ts" 2>/dev/null | wc -l | tr -d ' ')
    
    echo -e "📁 Components: ${GREEN}${COMPONENTS}${NC}"
    echo -e "📄 Total Files: ${GREEN}${TOTAL_FILES}${NC}"
    
    # Build status
    if npm run build &>/dev/null; then
        echo -e "🔨 Build Status: ${GREEN}✅ Passing${NC}"
    else
        echo -e "🔨 Build Status: ${RED}❌ Failing${NC}"
    fi
    
    # Bundle size
    if [ -d "dist" ]; then
        BUNDLE_SIZE=$(du -sh dist 2>/dev/null | cut -f1 || echo "Unknown")
        echo -e "📦 Bundle Size: ${GREEN}${BUNDLE_SIZE}${NC}"
    fi
    
    echo ""
}

# Main execution
echo -e "\n${GREEN}🎯 Updating Phase $PHASE${NC}"
echo -e "Status: ${YELLOW}$STATUS${NC}"
if [ -n "$DESCRIPTION" ]; then
    echo -e "Description: $DESCRIPTION"
fi

# Confirm update
read -p "Proceed with status update? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    update_status "$PHASE" "$STATUS" "$DESCRIPTION"
    show_progress
    show_metrics
    git_commit_status
    
    echo -e "\n${GREEN}🎉 Status update complete!${NC}"
    echo -e "📄 View updated status: ${YELLOW}cat STATUS.md${NC}"
else
    echo -e "${YELLOW}Status update cancelled.${NC}"
fi

echo ""