# Lullaby Clinic - Production Deployment Guide

This guide covers deploying the Lullaby Clinic medical practice management system to production.

## Prerequisites

Before deploying, ensure you have:
- Node.js 18+ installed
- A Supabase project with production database
- Production environment variables configured
- Domain name (optional, for custom domains)

## Quick Start

### 1. Environment Variables

Copy `.env.example` to `.env.production` and configure:

```bash
# Required - Supabase Configuration
VITE_SUPABASE_URL=your_production_supabase_url
VITE_SUPABASE_ANON_KEY=your_production_anon_key

# Application Environment
VITE_APP_ENVIRONMENT=production
VITE_APP_VERSION=1.0.0

# Analytics (Optional)
VITE_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX

# Email Services (Optional)
VITE_EMAILJS_SERVICE_ID=your_service_id
VITE_EMAILJS_TEMPLATE_ID=your_template_id
VITE_EMAILJS_USER_ID=your_user_id
```

### 2. Build Application

```bash
npm run build
```

### 3. Deploy to Platform

Choose one of the deployment options below:

## Deployment Options

### Option A: Vercel (Recommended)

1. **Install Vercel CLI**:
   ```bash
   npm i -g vercel
   ```

2. **Login to Vercel**:
   ```bash
   vercel login
   ```

3. **Deploy**:
   ```bash
   vercel --prod
   ```

4. **Configure Environment Variables** in Vercel dashboard:
   - Go to Project Settings → Environment Variables
   - Add all variables from `.env.production`

**GitHub Integration**: The project includes GitHub Actions for automatic deployment on push to `main` branch.

### Option B: Netlify

1. **Install Netlify CLI**:
   ```bash
   npm i -g netlify-cli
   ```

2. **Login to Netlify**:
   ```bash
   netlify login
   ```

3. **Deploy**:
   ```bash
   netlify deploy --prod --dir=dist
   ```

4. **Configure Environment Variables** in Netlify dashboard:
   - Go to Site Settings → Environment Variables
   - Add all variables from `.env.production`

### Option C: Docker

1. **Build Docker Image**:
   ```bash
   docker build -t lullaby-clinic .
   ```

2. **Run Container**:
   ```bash
   docker run -d -p 8080:8080 --name lullaby-clinic-app lullaby-clinic
   ```

3. **Using Docker Compose**:
   ```bash
   docker-compose up -d
   ```

For production with monitoring:
```bash
docker-compose --profile monitoring --profile logging up -d
```

## Post-Deployment Setup

### 1. Health Check

Verify the deployment is working:
```bash
curl -f https://your-domain.com/health
```

Expected response: `healthy`

### 2. Supabase Configuration

1. **Update Supabase URLs**:
   - Add your production domain to Supabase Auth settings
   - Configure redirect URLs for authentication

2. **Database Setup**:
   - Ensure all migrations are applied
   - Verify row-level security policies
   - Test authentication flows

### 3. Domain & SSL Setup

#### Custom Domain (Optional)

**For Vercel**:
1. Go to Project Settings → Domains
2. Add your custom domain
3. Configure DNS records as instructed

**For Netlify**:
1. Go to Site Settings → Domain Management
2. Add custom domain
3. Configure DNS records

#### SSL Certificate

Both Vercel and Netlify provide automatic SSL certificates. For Docker deployments, use Let's Encrypt or your hosting provider's SSL.

## Monitoring & Analytics

### Application Monitoring

The deployment includes health checks and performance monitoring:

- **Health Endpoint**: `/health`
- **Performance Metrics**: Built-in React performance hooks
- **Error Tracking**: Console errors are captured in production

### Google Analytics (Optional)

If configured, Google Analytics will track:
- Page views
- User interactions
- Conversion events (appointment bookings)

### Server Monitoring (Docker)

For Docker deployments with monitoring profile:
- **Prometheus**: http://your-domain:9090
- **Logs**: Aggregated via Loki

## Security Considerations

### HIPAA Compliance

The application includes security measures for medical data:

- **CSP Headers**: Restrict resource loading
- **HTTPS Only**: All connections encrypted
- **Secure Cookies**: Authentication cookies secured
- **Data Encryption**: All data encrypted in transit and at rest

### Security Headers

All deployments include:
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `X-Content-Type-Options: nosniff`
- `Strict-Transport-Security`
- Content Security Policy

## Performance Optimization

### Caching Strategy

- **Static Assets**: 1 year cache with immutable flag
- **HTML Files**: No cache (always fresh)
- **API Responses**: Cached by Supabase

### Bundle Optimization

The build includes:
- Code splitting by route and vendor
- Terser minification
- CSS optimization
- Asset compression

## CI/CD Pipeline

### GitHub Actions

The project includes automated deployment via GitHub Actions:

**Triggers**:
- Push to `main` branch
- Pull requests for testing

**Pipeline**:
1. Security scanning
2. Build and test
3. Deploy to Vercel (production)
4. Deploy to Netlify (staging)
5. Docker image build
6. Health checks
7. Performance testing

### Required Secrets

Configure these secrets in GitHub repository settings:

```bash
# Vercel
VERCEL_TOKEN=your_vercel_token
VERCEL_ORG_ID=your_org_id
VERCEL_PROJECT_ID=your_project_id

# Netlify
NETLIFY_AUTH_TOKEN=your_netlify_token
NETLIFY_SITE_ID=your_site_id

# Environment Variables
VITE_SUPABASE_URL=your_production_url
VITE_SUPABASE_ANON_KEY=your_production_key

# Optional
LHCI_GITHUB_APP_TOKEN=lighthouse_token
```

## Troubleshooting

### Common Issues

1. **Build Failures**:
   - Check Node.js version (requires 18+)
   - Verify all dependencies are installed
   - Check TypeScript errors

2. **Authentication Issues**:
   - Verify Supabase URL configuration
   - Check redirect URLs in Supabase Auth settings
   - Confirm environment variables are set

3. **Performance Issues**:
   - Check bundle size warnings
   - Verify image optimization
   - Review network requests

### Debug Mode

Enable debug mode by setting:
```bash
VITE_DEBUG=true
```

This will:
- Enable verbose logging
- Show performance metrics
- Display error details

## Maintenance

### Regular Updates

1. **Dependencies**: Update monthly
   ```bash
   npm update
   ```

2. **Security Patches**: Apply immediately
   ```bash
   npm audit fix
   ```

3. **Supabase Updates**: Monitor Supabase changelog

### Backup Strategy

- **Database**: Supabase provides automatic backups
- **Application**: Source code in Git repository
- **Assets**: Stored in CDN with version control

## Support

For deployment issues:
1. Check this guide first
2. Review application logs
3. Check Supabase dashboard for errors
4. Contact hosting provider support if needed

---

**Next Steps**: Choose your deployment method and follow the corresponding section above.