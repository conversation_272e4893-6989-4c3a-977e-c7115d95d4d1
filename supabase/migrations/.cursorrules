# Lullaby Clinic – Agent Context Rules (.rules)

These rules apply to all code edits, completions, and generation in this project. The Agent should treat this context as persistent across all actions.

## Project Tech Stack
- Next.js 15.3.3
- TypeScript strict mode
- TailwindCSS v3
- Supabase (Postgres + Storage)
- next-i18next (multilingual support for th, en, zh)
- Playwright (E2E)

## Project Goals
- Responsive UI for mobile and desktop
- SEO-ready with localized meta
- Multilingual (Thai, English, Chinese)
- Fully accessible (WCAG AA)
- LCP under 90 on mobile

## Development Rules
1. Use atomic UI components under `src/components/`
2. Group features in `src/features/<name>`
3. Dynamic content must be Supabase-driven
4. Locale text must come from i18n JSON
5. No `any`; use `strictNullChecks`
6. Catch all errors, return typed objects or Result pattern
7. Testing follows: Vitest → React Testing Library → Playwright

## Code Style
- 2 space indent
- Max line length: 100 characters
- Use single quotes `'`
- Always use semicolons
- Component names: PascalCase, match filename
- Functions & variables: camelCase
- CSS order: Tailwind utilities first, conditionals second
- Imports: use `@/` alias, absolute only
- Commits: use Conventional Commit style (e.g., `feat:`, `fix:`)

## Internationalization (i18n)
- Locale files in `public/locales/{th|en|zh}/`
- Use `t('key')` for translations
- All URLs must support `/[locale]/` prefix
- Fallback language: `en`

## UI/UX Guidelines
- Primary color: `#EF476F`
- Cards: `rounded-xl` + `shadow-lg` if interactive
- Spacing: use Tailwind multiples of 2
- Motion: use `framer-motion`, 200ms ease-in-out

## References
- https://tailwindcss.com/docs
- https://nextjs.org/docs
- https://supabase.com/docs
- https://next-i18next.com
- https://playwright.dev
