-- Create services table with mock image prompts
CREATE TABLE IF NOT EXISTS services (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  service_id TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  name_en TEXT,
  name_zh TEXT,
  price INTEGER NOT NULL,
  category TEXT NOT NULL CHECK (category IN ('botox', 'botox-package', 'filler', 'zone')),
  subcategory TEXT,
  brand TEXT,
  country TEXT,
  units INTEGER,
  zone TEXT,
  products JSONB,
  mock_image_prompt TEXT NOT NULL,
  description TEXT,
  description_en TEXT,
  description_zh TEXT,
  is_featured BOOLEAN DEFAULT false,
  is_popular BOOLEAN DEFAULT false,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_services_category ON services(category);
CREATE INDEX IF NOT EXISTS idx_services_service_id ON services(service_id);
CREATE INDEX IF NOT EXISTS idx_services_featured ON services(is_featured);
CREATE INDEX IF NOT EXISTS idx_services_popular ON services(is_popular);
CREATE INDEX IF NOT EXISTS idx_services_display_order ON services(display_order);

-- Enable RLS
ALTER TABLE services ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Services are viewable by everyone" ON services
  FOR SELECT USING (true);

CREATE POLICY "Services are insertable by authenticated users" ON services
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Services are updatable by authenticated users" ON services
  FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Services are deletable by authenticated users" ON services
  FOR DELETE USING (auth.role() = 'authenticated');

-- Insert Botox Injection Options with mock image prompts
INSERT INTO services (service_id, name, name_en, name_zh, price, category, mock_image_prompt, description, description_en, description_zh, display_order) VALUES
('b1', 'ริ้วรอย 1 จุด', 'Single Wrinkle Treatment', '单点皱纹治疗', 1990, 'botox', 'Minimalist flat-style icon of a single wrinkle line on smooth skin, with a small syringe icon pointing at it, soft pastel pink background, medical aesthetic style', 'บอทอกซ์เฉพาะจุดสำหรับริ้วรอยเส้นเดียว', 'Targeted Botox treatment for single wrinkle line', '针对单条皱纹的肉毒杆菌治疗', 1),
('b2', 'ลดรอยหน้าผาก', 'Forehead Lines Reduction', '减少额头皱纹', 1990, 'botox', 'Clean illustration of a smooth forehead with fading horizontal lines, syringe with sparkles, gradient background from white to soft pink', 'ลดริ้วรอยบนหน้าผากให้เรียบเนียน', 'Smooth forehead lines for a youthful appearance', '减少额头皱纹，恢复年轻外观', 2),
('b3', 'หางตา (ตีนกา)', 'Crow''s Feet Treatment', '鱼尾纹治疗', 1990, 'botox', 'Stylized eye corner with radiating fine lines fading away, delicate syringe icon, soft lavender background, minimalist medical design', 'ลดริ้วรอยหางตาให้ดูอ่อนเยาว์', 'Reduce crow''s feet for younger-looking eyes', '减少鱼尾纹，让眼部更年轻', 3),
('b4', 'ยกคิ้ว', 'Eyebrow Lift', '提眉', 1990, 'botox', 'Elegant eyebrow shape with upward arrow, before/after comparison silhouette, soft gradient background, professional aesthetic clinic style', 'ยกคิ้วให้ดูกระชับและอ่อนเยาว์', 'Lift eyebrows for a more youthful, alert appearance', '提升眉毛，打造年轻警觉的外观', 4),
('b5', 'ลดรอยย่นจมูก', 'Nose Wrinkle Reduction', '减少鼻部皱纹', 1990, 'botox', 'Side profile of nose with smooth lines, small wrinkles disappearing, medical syringe with precision tip, clean white to mint green background', 'ลดริ้วรอยย่นบริเวณจมูก', 'Smooth nose wrinkles for refined appearance', '减少鼻部皱纹，精致外观', 5),
('b6', 'รัดแกนจมูก', 'Nose Bridge Contouring', '鼻梁塑形', 2990, 'botox', 'Geometric nose bridge illustration with contouring lines, precision injection points, sophisticated medical aesthetic design, pearl white background', 'ปรับรูปทรงแกนจมูกให้สวยงาม', 'Contour nose bridge for enhanced facial harmony', '塑造鼻梁，增强面部和谐', 6),
('b7', 'เก็บปีกจมูก', 'Nose Wing Reduction', '收缩鼻翼', 1990, 'botox', 'Delicate nose wing illustration with subtle reduction arrows, medical precision points, soft coral background, minimalist clinic aesthetic', 'ลดขนาดปีกจมูกให้เหมาะสมกับใบหน้า', 'Reduce nose wing size for facial balance', '缩小鼻翼，平衡面部比例', 7),
('b8', 'ยกมุมปาก', 'Mouth Corner Lift', '提升嘴角', 1990, 'botox', 'Stylized lips with upward curved corners, happy expression transformation, gentle injection points, warm peach gradient background', 'ยกมุมปากให้ดูยิ้มแย้มแจ่มใส', 'Lift mouth corners for a naturally happy expression', '提升嘴角，自然愉悦表情', 8),
('b9', 'ยิ้มเห็นฟัน', 'Gummy Smile Treatment', '露龈笑治疗', 3990, 'botox', 'Beautiful smile with perfect teeth visibility, gum line adjustment illustration, dental aesthetic design, bright white to soft blue background', 'ปรับสัดส่วนการยิ้มให้เห็นฟันในสัดส่วนที่เหมาะสม', 'Perfect smile proportion with ideal teeth visibility', '完美笑容比例，理想的牙齿显露', 9),
('b10', 'เหมาหน้าด้านบน', 'Upper Face Package', '上半脸套餐', 3990, 'botox', 'Upper facial area diagram with treatment zones highlighted, comprehensive coverage illustration, professional medical chart style, gradient blue background', 'บอทอกซ์เหมาหน้าด้านบนครบทุกจุด', 'Comprehensive upper face Botox treatment', '上半脸全面肉毒杆菌治疗', 10),
('b11', 'หน้าบาน (กรามใหญ่)', 'Jaw Slimming', '瘦脸针', 3990, 'botox', 'Face silhouette transformation from wide to slim jawline, before/after comparison, contouring effect illustration, elegant rose gold background', 'ลดขนาดกรามให้หน้าเรียวสวย', 'Slim jawline for a more refined face shape', '瘦脸塑形，精致面部轮廓', 11),
('b12', 'ลิฟท์กรอบหน้า', 'Face Frame Lift', '面部轮廓提升', 3990, 'botox', 'Facial contour with lifting vectors, tightening effect visualization, anti-aging transformation, luxury spa aesthetic, champagne gradient background', 'ยกกระชับกรอบหน้าให้ดูอ่อนเยาว์', 'Lift and tighten facial contours for youthful appearance', '提升面部轮廓，年轻外观', 12),
('b13', 'รักแร้ 100 units', 'Underarm 100 Units', '腋下100单位', 8990, 'botox', 'Clean underarm area illustration with sweat reduction symbols, comfort and confidence theme, fresh mint and white color scheme', 'บอทอกซ์รักแร้ 100 ยูนิต ลดเหงื่อ', '100 units underarm Botox for sweat reduction', '100单位腋下肉毒杆菌，减少出汗', 13),
('b14', 'รักแร้ 200 units', 'Underarm 200 Units', '腋下200单位', 14990, 'botox', 'Enhanced underarm treatment illustration with maximum effectiveness symbols, premium package design, cool blue and silver gradient', 'บอทอกซ์รักแร้ 200 ยูนิต ผลลัพธ์สูงสุด', '200 units underarm Botox for maximum effectiveness', '200单位腋下肉毒杆菌，最大效果', 14),
('b15', 'แขน/น่อง 200 units', 'Arms/Calves 200 Units', '手臂/小腿200单位', 14990, 'botox', 'Arm and calf muscle illustration with slimming effect, body contouring visualization, athletic and elegant design, soft purple gradient', 'บอทอกซ์แขนและน่อง 200 ยูนิต เพื่อรูปร่างสวย', '200 units arms/calves Botox for body contouring', '200单位手臂/小腿肉毒杆菌塑形', 15),
('b16', 'รอยบุ๋มที่คาง', 'Chin Dimple Treatment', '下巴凹陷治疗', 1990, 'botox', 'Smooth chin profile with dimple correction, facial harmony illustration, precision treatment point, elegant cream and gold background', 'ปรับรอยบุ๋มที่คางให้เรียบเนียน', 'Smooth chin dimples for refined facial profile', '平滑下巴凹陷，精致面部轮廓', 16);

-- Insert Botox Brand Packages with mock image prompts
INSERT INTO services (service_id, name, name_en, name_zh, price, category, brand, units, mock_image_prompt, description, description_en, description_zh, display_order) VALUES
('bp1', 'Hugel', 'Hugel Botox', 'Hugel肉毒杆菌', 3990, 'botox-package', 'Hugel', 50, 'Premium Hugel brand logo with 50 units badge, Korean medical technology symbols, professional packaging design, clean white and blue corporate colors', 'บอทอกซ์ยี่ห้อ Hugel 50 ยูนิต คุณภาพเกาหลี', 'Hugel brand Botox 50 units, Korean quality', 'Hugel品牌肉毒杆菌50单位，韩国品质', 17),
('bp2', 'Neuronox', 'Neuronox Botox', 'Neuronox肉毒杆菌', 8990, 'botox-package', 'Neuronox', 100, 'Neuronox brand packaging with 100 units highlight, advanced Korean biotechnology design, medical grade quality symbols, sophisticated navy and silver theme', 'บอทอกซ์ยี่ห้อ Neuronox 100 ยูนิต เทคโนโลยีเกาหลี', 'Neuronox brand Botox 100 units, Korean technology', 'Neuronox品牌肉毒杆菌100单位，韩国技术', 18),
('bp3', 'Allergan', 'Allergan Botox', 'Allergan肉毒杆菌', 17990, 'botox-package', 'Allergan', 100, 'Premium Allergan brand with gold accents, 100 units premium badge, American FDA approved symbols, luxury medical aesthetic design, royal blue and gold colors', 'บอทอกซ์ยี่ห้อ Allergan 100 ยูนิต มาตรฐาน FDA', 'Allergan brand Botox 100 units, FDA approved', 'Allergan品牌肉毒杆菌100单位，FDA认证', 19),
('bp4', 'Xeomin', 'Xeomin Botox', 'Xeomin肉毒杆菌', 17990, 'botox-package', 'Xeomin', 100, 'Xeomin brand with German precision symbols, 100 units quality badge, European medical standards icons, clean minimalist design, forest green and white palette', 'บอทอกซ์ยี่ห้อ Xeomin 100 ยูนิต คุณภาพเยอรมัน', 'Xeomin brand Botox 100 units, German quality', 'Xeomin品牌肉毒杆菌100单位，德国品质', 20);

-- Insert Dermal Fillers with mock image prompts
INSERT INTO services (service_id, name, name_en, name_zh, price, category, brand, country, mock_image_prompt, description, description_en, description_zh, display_order) VALUES
('f1', 'Juvederm Ultra Plus XC', 'Juvederm Ultra Plus XC', 'Juvederm Ultra Plus XC', 12990, 'filler', 'Juvederm', 'USA', 'Juvederm Ultra Plus XC syringe with USA flag badge, hyaluronic acid molecular structure, premium American medical device design, sophisticated blue and silver branding', 'ฟิลเลอร์ Juvederm Ultra Plus XC จากอเมริกา', 'Juvederm Ultra Plus XC filler from USA', '美国Juvederm Ultra Plus XC填充剂', 21),
('f2', 'Juvederm Vobella', 'Juvederm Vobella', 'Juvederm Vobella', 13990, 'filler', 'Juvederm', 'USA', 'Juvederm Vobella elegant packaging with lip enhancement focus, American quality symbols, delicate feminine design, soft pink and pearl white aesthetic', 'ฟิลเลอร์ Juvederm Vobella สำหรับริมฝีปาก', 'Juvederm Vobella filler for lip enhancement', 'Juvederm Vobella唇部填充剂', 22),
('f3', 'Juvederm Volift', 'Juvederm Volift', 'Juvederm Volift', 15990, 'filler', 'Juvederm', 'USA', 'Juvederm Volift with facial lifting arrows, anti-aging transformation symbols, premium USA medical technology, elegant purple and gold design theme', 'ฟิลเลอร์ Juvederm Volift ยกกระชับใบหน้า', 'Juvederm Volift filler for facial lifting', 'Juvederm Volift面部提升填充剂', 23),
('f4', 'Juvederm Voluma', 'Juvederm Voluma', 'Juvederm Voluma', 16990, 'filler', 'Juvederm', 'USA', 'Juvederm Voluma with volume enhancement visualization, cheek contouring illustration, premium American brand design, rich burgundy and gold luxury aesthetic', 'ฟิลเลอร์ Juvederm Voluma เพิ่มปริมาตรใบหน้า', 'Juvederm Voluma filler for facial volume', 'Juvederm Voluma面部丰盈填充剂', 24),
('f5', 'Restylane Vital Light', 'Restylane Vital Light', 'Restylane Vital Light', 14990, 'filler', 'Restylane', 'Sweden', 'Restylane Vital Light with Swedish flag badge, skin hydration symbols, Nordic medical excellence design, cool blue and white Scandinavian aesthetic', 'ฟิลเลอร์ Restylane Vital Light จากสวีเดน', 'Restylane Vital Light filler from Sweden', '瑞典Restylane Vital Light填充剂', 25),
('f6', 'Restylane Perlane Lyft', 'Restylane Perlane Lyft', 'Restylane Perlane Lyft', 14990, 'filler', 'Restylane', 'Sweden', 'Restylane Perlane Lyft with lifting effect arrows, Swedish medical innovation symbols, professional Nordic design, emerald green and silver palette', 'ฟิลเลอร์ Restylane Perlane Lyft ยกกระชับ', 'Restylane Perlane Lyft lifting filler', 'Restylane Perlane Lyft提升填充剂', 26),
('f7', 'Neuramis Deep', 'Neuramis Deep', 'Neuramis Deep', 8990, 'filler', 'Neuramis', 'Korea', 'Neuramis Deep with Korean flag badge, deep injection illustration, K-beauty medical technology symbols, modern Korean design, coral and white color scheme', 'ฟิลเลอร์ Neuramis Deep จากเกาหลี', 'Neuramis Deep filler from Korea', '韩国Neuramis Deep填充剂', 27);

-- Insert Top 10 Filler Zones with mock image prompts
INSERT INTO services (service_id, name, name_en, name_zh, price, category, zone, products, mock_image_prompt, description, description_en, description_zh, display_order) VALUES
('z1', 'หน้าผากแบน', 'Flat Forehead Treatment', '平额头治疗', 8990, 'zone', 'หน้าผากแบน', '[{"name": "Neuramis Deep", "price": 8990}, {"name": "Restylane Perlane Lyft", "price": 13990}, {"name": "Juvederm Vobella", "price": 13990}]', 'Forehead area with volume enhancement arrows, before/after profile comparison, facial harmony illustration, soft gradient background', 'เติมฟิลเลอร์หน้าผากให้ดูเต็มและสวยงาม', 'Forehead filler for enhanced volume and beauty', '额头填充，增强体积和美感', 28),
('z2', 'ขมับตอบ', 'Hollow Temples Treatment', '太阳穴凹陷治疗', 8990, 'zone', 'ขมับตอบ', '[{"name": "Neuramis Deep", "price": 8990}, {"name": "Restylane Perlane Lyft", "price": 13990}, {"name": "Juvederm Vobella", "price": 16990}]', 'Temple area with filling effect, facial contour improvement, side profile enhancement, elegant medical illustration', 'เติมฟิลเลอร์ขมับให้เต็มและกรอบหน้าสวย', 'Temple filler for fuller, beautiful facial frame', '太阳穴填充，丰满美丽面部轮廓', 29),
('z3', 'ใต้ตา', 'Under Eye Treatment', '眼下治疗', 14990, 'zone', 'ใต้ตา', '[{"name": "Restylane Vital Light", "price": 14990}]', 'Under-eye area with tear trough correction, youthful eye appearance, delicate injection technique illustration, soft lavender theme', 'เติมฟิลเลอร์ใต้ตาลดร่องลึก', 'Under-eye filler to reduce deep tear troughs', '眼下填充减少深泪沟', 30),
('z4', 'แก้มตอบ', 'Hollow Cheeks Treatment', '脸颊凹陷治疗', 8990, 'zone', 'แก้มตอบ', '[{"name": "Neuramis Deep", "price": 8990}, {"name": "Juvederm Voluma", "price": 16990}]', 'Cheek area with volume restoration, youthful fullness effect, facial rejuvenation illustration, warm peach gradient', 'เติมฟิลเลอร์แก้มให้เต็มและอ่อนเยาว์', 'Cheek filler for fullness and youthful appearance', '脸颊填充，丰满年轻外观', 31),
('z5', 'หน้าแก้มอิ่ม', 'Full Cheeks Enhancement', '丰满脸颊增强', 14990, 'zone', 'หน้าแก้มอิ่ม', '[{"name": "Juvederm Volift", "price": 15990}, {"name": "Restylane Perlane Lyft", "price": 14990}]', 'Full cheek enhancement with natural volume, beautiful facial proportions, Korean beauty standard illustration, rose gold aesthetic', 'เติมฟิลเลอร์หน้าแก้มให้อิ่มสวยตามสัดส่วน', 'Cheek filler for beautiful, proportional fullness', '脸颊填充，美丽比例丰满', 32),
('z6', 'ร่องแก้มลึก', 'Deep Nasolabial Folds', '深鼻唇沟', 8990, 'zone', 'ร่องแก้มลึก', '[{"name": "Neuramis Deep", "price": 8990}, {"name": "Juvederm Ultra Plus XC", "price": 12990}, {"name": "Restylane Perlane Lyft", "price": 13990}, {"name": "Juvederm Volift", "price": 15990}]', 'Nasolabial fold correction with smoothing effect, anti-aging transformation, facial rejuvenation focus, sophisticated medical design', 'เติมฟิลเลอร์ร่องแก้มลึกให้เรียบเนียน', 'Nasolabial fold filler for smooth appearance', '鼻唇沟填充，平滑外观', 33),
('z7', 'กรอบหน้าไม่ชัด', 'Undefined Face Frame', '面部轮廓不清晰', 16990, 'zone', 'กรอบหน้าไม่ชัด', '[{"name": "Juvederm Voluma", "price": 16990}]', 'Facial contouring with defined jawline, face frame enhancement, sculptural beauty effect, luxury gold and white design', 'เติมฟิลเลอร์กรอบหน้าให้ชัดเจนสวยงาม', 'Face frame filler for clear, beautiful definition', '面部轮廓填充，清晰美丽定义', 34),
('z8', 'ปากบาง', 'Thin Lips Treatment', '薄唇治疗', 8990, 'zone', 'ปากบาง', '[{"name": "Neuramis Deep", "price": 8990}, {"name": "Juvederm Ultra Plus XC", "price": 12990}, {"name": "Restylane Vital Light", "price": 14990}]', 'Lip enhancement with natural fullness, beautiful lip shape, sensual and elegant design, soft pink and coral palette', 'เติมฟิลเลอร์ปากให้อิ่มสวยเป็นธรรมชาติ', 'Lip filler for natural, beautiful fullness', '唇部填充，自然美丽丰满', 35),
('z9', 'คางสั้น', 'Short Chin Treatment', '短下巴治疗', 8990, 'zone', 'คางสั้น', '[{"name": "Neuramis Deep", "price": 8990}, {"name": "Juvederm Voluma", "price": 16990}]', 'Chin projection enhancement, facial profile improvement, balanced proportions illustration, sophisticated medical aesthetic', 'เติมฟิลเลอร์คางให้ยาวและสวยงาม', 'Chin filler for length and beauty enhancement', '下巴填充，增长和美化', 36),
('z10', 'ร่องน้ำหมากลึก', 'Deep Marionette Lines', '深木偶纹', 8990, 'zone', 'ร่องน้ำหมากลึก', '[{"name": "Neuramis Deep", "price": 8990}, {"name": "Juvederm Voluma", "price": 12990}]', 'Marionette lines correction with lifting effect, mouth corner enhancement, anti-aging facial treatment, elegant cream and gold theme', 'เติมฟิลเลอร์ร่องน้ำหมากให้เรียบเนียน', 'Marionette lines filler for smooth appearance', '木偶纹填充，平滑外观', 37);

-- Update timestamps trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_services_updated_at BEFORE UPDATE ON services
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
