-- Before & After Gallery Migration
-- Comprehensive gallery system with mock-up dates and Supabase storage integration
-- Created: December 25, 2024

-- Create before_after_gallery table
CREATE TABLE public.before_after_gallery (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  category VARCHAR(100) NOT NULL,
  treatment VARCHAR(200) NOT NULL,
  duration VARCHAR(100),
  before_image_url TEXT NOT NULL,
  after_image_url TEXT NOT NULL,
  before_date TIMESTAMP WITH TIME ZONE NOT NULL,
  after_date TIMESTAMP WITH TIME ZONE NOT NULL,
  patient_age INTEGER,
  patient_gender VARCHAR(20) CHECK (patient_gender IN ('male', 'female')),
  difficulty VARCHAR(20) NOT NULL DEFAULT 'moderate' CHECK (difficulty IN ('easy', 'moderate', 'complex')),
  rating DECIMAL(3,2) NOT NULL DEFAULT 4.5 CHECK (rating >= 1.0 AND rating <= 5.0),
  featured BOOLEAN DEFAULT false,
  tags TEXT[] DEFAULT '{}',
  doctor_id UUID REFERENCES doctors(id) ON DELETE SET NULL,
  service_id UUID REFERENCES services(id) ON DELETE SET NULL,
  is_approved BOOLEAN DEFAULT true,
  is_active BOOLEAN DEFAULT true,
  view_count INTEGER DEFAULT 0,
  like_count INTEGER DEFAULT 0,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_before_after_gallery_category ON before_after_gallery(category);
CREATE INDEX idx_before_after_gallery_difficulty ON before_after_gallery(difficulty);
CREATE INDEX idx_before_after_gallery_rating ON before_after_gallery(rating);
CREATE INDEX idx_before_after_gallery_featured ON before_after_gallery(featured);
CREATE INDEX idx_before_after_gallery_is_active ON before_after_gallery(is_active);
CREATE INDEX idx_before_after_gallery_before_date ON before_after_gallery(before_date);
CREATE INDEX idx_before_after_gallery_after_date ON before_after_gallery(after_date);
CREATE INDEX idx_before_after_gallery_created_at ON before_after_gallery(created_at);

-- Enable Row Level Security
ALTER TABLE before_after_gallery ENABLE ROW LEVEL SECURITY;

-- RLS Policies
-- Public read access for approved and active gallery items
CREATE POLICY "Gallery items are viewable by everyone" ON before_after_gallery
  FOR SELECT USING (is_approved = true AND is_active = true);

-- Admin/Doctor can manage all gallery items
CREATE POLICY "Admins can manage gallery items" ON before_after_gallery
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.id = auth.uid() 
      AND user_profiles.role IN ('admin', 'doctor')
    )
  );

-- Insert sample data with realistic mock-up dates
INSERT INTO before_after_gallery (
  title, description, category, treatment, duration,
  before_image_url, after_image_url, before_date, after_date,
  patient_age, patient_gender, difficulty, rating, featured, tags
) VALUES 
(
  'Acne Treatment Transformation',
  'Complete acne treatment with laser therapy and chemical peels for clear, smooth skin. Patient showed remarkable improvement in skin texture and clarity.',
  'acne',
  'Laser + Chemical Peel',
  '3 months',
  'gallery/before/acne-treatment-before-1.jpg',
  'gallery/after/acne-treatment-after-1.jpg',
  NOW() - INTERVAL '4 months',
  NOW() - INTERVAL '1 month',
  25,
  'female',
  'moderate',
  4.8,
  true,
  ARRAY['laser', 'chemical-peel', 'acne-scars', 'skin-texture']
),
(
  'Pigmentation Correction Success',
  'Advanced IPL treatment combined with vitamin C therapy for even skin tone. Significant reduction in melasma and sun damage.',
  'pigmentation',
  'IPL + Vitamin C Therapy',
  '2 months',
  'gallery/before/pigmentation-before-1.jpg',
  'gallery/after/pigmentation-after-1.jpg',
  NOW() - INTERVAL '3 months',
  NOW() - INTERVAL '3 weeks',
  35,
  'female',
  'easy',
  4.9,
  true,
  ARRAY['ipl', 'vitamin-c', 'melasma', 'sun-damage']
),
(
  'Anti-Aging Facial Rejuvenation',
  'Non-surgical facial rejuvenation with botox and dermal fillers. Natural-looking results with improved facial contours.',
  'anti-aging',
  'Botox + Dermal Fillers',
  '1 session',
  'gallery/before/anti-aging-before-1.jpg',
  'gallery/after/anti-aging-after-1.jpg',
  NOW() - INTERVAL '2 months',
  NOW() - INTERVAL '2 weeks',
  45,
  'female',
  'complex',
  5.0,
  true,
  ARRAY['botox', 'fillers', 'wrinkles', 'facial-contouring']
),
(
  'Acne Scar Reduction',
  'Fractional laser treatment for significant scar reduction and skin texture improvement. Multiple sessions for optimal results.',
  'scars',
  'Fractional Laser Resurfacing',
  '4 months',
  'gallery/before/scar-reduction-before-1.jpg',
  'gallery/after/scar-reduction-after-1.jpg',
  NOW() - INTERVAL '5 months',
  NOW() - INTERVAL '2 weeks',
  28,
  'male',
  'complex',
  4.7,
  false,
  ARRAY['fractional-laser', 'acne-scars', 'texture', 'resurfacing']
),
(
  'Dark Spot Removal',
  'Targeted Q-Switch laser treatment for precise dark spot removal. Excellent results with minimal downtime.',
  'pigmentation',
  'Q-Switch Laser',
  '6 weeks',
  'gallery/before/dark-spots-before-1.jpg',
  'gallery/after/dark-spots-after-1.jpg',
  NOW() - INTERVAL '2 months',
  NOW() - INTERVAL '1 week',
  40,
  'female',
  'easy',
  4.6,
  true,
  ARRAY['q-switch', 'dark-spots', 'sun-damage', 'laser']
),
(
  'Pore Minimization Treatment',
  'Advanced microneedling with PRP for pore reduction and skin refinement. Improved skin texture and reduced pore visibility.',
  'pores',
  'Microneedling + PRP',
  '3 months',
  'gallery/before/pore-treatment-before-1.jpg',
  'gallery/after/pore-treatment-after-1.jpg',
  NOW() - INTERVAL '4 months',
  NOW() - INTERVAL '3 weeks',
  30,
  'female',
  'moderate',
  4.5,
  false,
  ARRAY['microneedling', 'prp', 'pore-reduction', 'skin-texture']
),
(
  'Rosacea Treatment Success',
  'IPL therapy combined with topical treatments for rosacea management. Significant reduction in redness and inflammation.',
  'rosacea',
  'IPL + Medical Skincare',
  '2 months',
  'gallery/before/rosacea-before-1.jpg',
  'gallery/after/rosacea-after-1.jpg',
  NOW() - INTERVAL '3 months',
  NOW() - INTERVAL '2 weeks',
  38,
  'female',
  'moderate',
  4.4,
  false,
  ARRAY['ipl', 'rosacea', 'redness', 'inflammation']
),
(
  'Stretch Mark Reduction',
  'Fractional laser treatment for stretch mark improvement. Noticeable reduction in appearance and improved skin texture.',
  'stretch-marks',
  'Fractional Laser',
  '6 months',
  'gallery/before/stretch-marks-before-1.jpg',
  'gallery/after/stretch-marks-after-1.jpg',
  NOW() - INTERVAL '7 months',
  NOW() - INTERVAL '1 month',
  32,
  'female',
  'complex',
  4.3,
  false,
  ARRAY['fractional-laser', 'stretch-marks', 'skin-texture']
),
(
  'Skin Tightening Treatment',
  'Radiofrequency skin tightening for improved facial contours. Non-invasive treatment with natural-looking results.',
  'skin-tightening',
  'Radiofrequency Therapy',
  '3 months',
  'gallery/before/skin-tightening-before-1.jpg',
  'gallery/after/skin-tightening-after-1.jpg',
  NOW() - INTERVAL '4 months',
  NOW() - INTERVAL '2 weeks',
  42,
  'female',
  'moderate',
  4.6,
  true,
  ARRAY['radiofrequency', 'skin-tightening', 'facial-contouring']
),
(
  'Hyperpigmentation Treatment',
  'Chemical peel series combined with medical-grade skincare for hyperpigmentation treatment. Even skin tone achieved.',
  'pigmentation',
  'Chemical Peels + Medical Skincare',
  '4 months',
  'gallery/before/hyperpigmentation-before-1.jpg',
  'gallery/after/hyperpigmentation-after-1.jpg',
  NOW() - INTERVAL '5 months',
  NOW() - INTERVAL '3 weeks',
  29,
  'female',
  'moderate',
  4.7,
  false,
  ARRAY['chemical-peel', 'hyperpigmentation', 'skincare', 'even-tone']
);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for updated_at
CREATE TRIGGER update_before_after_gallery_updated_at 
    BEFORE UPDATE ON before_after_gallery 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create storage bucket for gallery images
INSERT INTO storage.buckets (id, name, public) 
VALUES ('gallery-images', 'gallery-images', true)
ON CONFLICT (id) DO NOTHING;

-- Storage policies for gallery images
CREATE POLICY "Gallery images are publicly accessible" ON storage.objects
  FOR SELECT USING (bucket_id = 'gallery-images');

CREATE POLICY "Admins can upload gallery images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'gallery-images' AND
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.id = auth.uid() 
      AND user_profiles.role IN ('admin', 'doctor')
    )
  );

CREATE POLICY "Admins can update gallery images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'gallery-images' AND
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.id = auth.uid() 
      AND user_profiles.role IN ('admin', 'doctor')
    )
  );

CREATE POLICY "Admins can delete gallery images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'gallery-images' AND
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.id = auth.uid() 
      AND user_profiles.role IN ('admin', 'doctor')
    )
  );
