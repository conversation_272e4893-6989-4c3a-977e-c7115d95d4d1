-- =============================================
-- Admin Dashboard Foundation Migration
-- Creates tables and functions for admin CMS/CRM system
-- =============================================

-- Create admin role enum
CREATE TYPE admin_role AS ENUM (
  'super_admin',    -- Full access to everything
  'admin',          -- Most features except user management
  'manager',        -- Operations and reports
  'staff',          -- Basic operations
  'content_editor', -- CMS only
  'analyst'         -- Analytics only
);

-- Create interaction priority enum
CREATE TYPE interaction_priority AS ENUM ('low', 'medium', 'high', 'urgent');

-- Create campaign status enum
CREATE TYPE campaign_status AS ENUM ('draft', 'scheduled', 'active', 'paused', 'completed', 'cancelled');

-- =============================================
-- ADMIN MANAGEMENT TABLES
-- =============================================

-- Admin users table
CREATE TABLE admin_users (
  id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  role admin_role NOT NULL DEFAULT 'staff',
  permissions jsonb DEFAULT '{}',
  department TEXT,
  employee_id TEXT UNIQUE,
  hire_date DATE,
  is_active BOOLEAN DEFAULT true,
  last_login_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Admin user profile extension
CREATE TABLE admin_profiles (
  id uuid PRIMARY KEY REFERENCES admin_users(id) ON DELETE CASCADE,
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  email TEXT NOT NULL UNIQUE,
  phone TEXT,
  avatar_url TEXT,
  bio TEXT,
  preferences jsonb DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- CONTENT MANAGEMENT SYSTEM TABLES
-- =============================================

-- CMS pages for dynamic content
CREATE TABLE cms_pages (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  slug TEXT UNIQUE NOT NULL,
  title jsonb NOT NULL, -- {"en": "Title", "th": "หัวข้อ", "zh": "标题"}
  content jsonb NOT NULL,
  excerpt jsonb,
  meta_title jsonb,
  meta_description jsonb,
  meta_keywords TEXT[],
  featured_image_url TEXT,
  is_published BOOLEAN DEFAULT false,
  publish_at TIMESTAMPTZ,
  page_type TEXT NOT NULL DEFAULT 'page', -- 'page', 'blog', 'service', 'landing'
  template TEXT DEFAULT 'default',
  sort_order INTEGER DEFAULT 0,
  view_count INTEGER DEFAULT 0,
  created_by uuid REFERENCES admin_users(id),
  updated_by uuid REFERENCES admin_users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- CMS media library
CREATE TABLE cms_media (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  filename TEXT NOT NULL,
  original_filename TEXT NOT NULL,
  storage_path TEXT NOT NULL,
  public_url TEXT,
  mime_type TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  width INTEGER,
  height INTEGER,
  alt_text jsonb, -- {"en": "Alt text", "th": "ข้อความทดแทน"}
  description jsonb,
  tags TEXT[],
  is_active BOOLEAN DEFAULT true,
  uploaded_by uuid REFERENCES admin_users(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Blog categories
CREATE TABLE blog_categories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name jsonb NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  description jsonb,
  color TEXT DEFAULT '#3B82F6',
  icon TEXT,
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Blog tags
CREATE TABLE blog_tags (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name jsonb NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  color TEXT DEFAULT '#6B7280',
  usage_count INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Many-to-many relationship for page categories
CREATE TABLE cms_page_categories (
  page_id uuid REFERENCES cms_pages(id) ON DELETE CASCADE,
  category_id uuid REFERENCES blog_categories(id) ON DELETE CASCADE,
  PRIMARY KEY (page_id, category_id)
);

-- Many-to-many relationship for page tags
CREATE TABLE cms_page_tags (
  page_id uuid REFERENCES cms_pages(id) ON DELETE CASCADE,
  tag_id uuid REFERENCES blog_tags(id) ON DELETE CASCADE,
  PRIMARY KEY (page_id, tag_id)
);

-- =============================================
-- CUSTOMER RELATIONSHIP MANAGEMENT TABLES
-- =============================================

-- Customer segments for targeted communications
CREATE TABLE customer_segments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  criteria jsonb NOT NULL, -- Query conditions for dynamic segments
  color TEXT DEFAULT '#3B82F6',
  icon TEXT,
  is_dynamic BOOLEAN DEFAULT true, -- true for rule-based, false for manual
  patient_count INTEGER DEFAULT 0,
  last_updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by uuid REFERENCES admin_users(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Manual segment membership (for non-dynamic segments)
CREATE TABLE segment_memberships (
  segment_id uuid REFERENCES customer_segments(id) ON DELETE CASCADE,
  patient_id uuid REFERENCES user_profiles(id) ON DELETE CASCADE,
  added_by uuid REFERENCES admin_users(id),
  added_at TIMESTAMPTZ DEFAULT NOW(),
  PRIMARY KEY (segment_id, patient_id)
);

-- Customer interactions and communications
CREATE TABLE customer_interactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  patient_id uuid REFERENCES user_profiles(id) ON DELETE CASCADE,
  interaction_type TEXT NOT NULL, -- 'call', 'email', 'sms', 'visit', 'complaint', 'feedback'
  direction TEXT NOT NULL DEFAULT 'outbound', -- 'inbound', 'outbound'
  subject TEXT,
  content TEXT,
  outcome TEXT,
  priority interaction_priority DEFAULT 'medium',
  status TEXT DEFAULT 'completed', -- 'scheduled', 'in_progress', 'completed', 'cancelled'
  scheduled_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  follow_up_date DATE,
  tags TEXT[],
  attachments jsonb DEFAULT '[]',
  created_by uuid REFERENCES admin_users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Customer notes and annotations
CREATE TABLE customer_notes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  patient_id uuid REFERENCES user_profiles(id) ON DELETE CASCADE,
  note_type TEXT DEFAULT 'general', -- 'general', 'medical', 'financial', 'preference'
  title TEXT,
  content TEXT NOT NULL,
  is_private BOOLEAN DEFAULT false, -- Only visible to creator and admins
  is_important BOOLEAN DEFAULT false,
  tags TEXT[],
  created_by uuid REFERENCES admin_users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- MARKETING AND COMMUNICATION TABLES
-- =============================================

-- Email templates
CREATE TABLE email_templates (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  category TEXT NOT NULL, -- 'appointment', 'marketing', 'notification', 'welcome'
  subject jsonb NOT NULL,
  html_content jsonb NOT NULL,
  text_content jsonb,
  variables TEXT[], -- Available template variables
  is_active BOOLEAN DEFAULT true,
  usage_count INTEGER DEFAULT 0,
  created_by uuid REFERENCES admin_users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Marketing campaigns
CREATE TABLE marketing_campaigns (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  campaign_type TEXT NOT NULL, -- 'email', 'sms', 'push', 'multi_channel'
  target_segment uuid REFERENCES customer_segments(id),
  email_template_id uuid REFERENCES email_templates(id),
  subject_line jsonb,
  content jsonb NOT NULL,
  sender_name TEXT,
  sender_email TEXT,
  scheduled_at TIMESTAMPTZ,
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  status campaign_status DEFAULT 'draft',
  total_recipients INTEGER DEFAULT 0,
  sent_count INTEGER DEFAULT 0,
  delivered_count INTEGER DEFAULT 0,
  opened_count INTEGER DEFAULT 0,
  clicked_count INTEGER DEFAULT 0,
  unsubscribed_count INTEGER DEFAULT 0,
  bounced_count INTEGER DEFAULT 0,
  budget_amount DECIMAL(10,2),
  actual_cost DECIMAL(10,2),
  roi_percentage DECIMAL(5,2),
  created_by uuid REFERENCES admin_users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Campaign recipients and their status
CREATE TABLE campaign_recipients (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  campaign_id uuid REFERENCES marketing_campaigns(id) ON DELETE CASCADE,
  patient_id uuid REFERENCES user_profiles(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  status TEXT DEFAULT 'pending', -- 'pending', 'sent', 'delivered', 'opened', 'clicked', 'bounced', 'unsubscribed'
  sent_at TIMESTAMPTZ,
  delivered_at TIMESTAMPTZ,
  opened_at TIMESTAMPTZ,
  clicked_at TIMESTAMPTZ,
  bounce_reason TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- ANALYTICS AND REPORTING TABLES
-- =============================================

-- Analytics events for tracking user behavior
CREATE TABLE analytics_events (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type TEXT NOT NULL, -- 'page_view', 'appointment_book', 'form_submit', 'email_open', etc.
  event_category TEXT NOT NULL, -- 'website', 'email', 'appointment', 'payment'
  event_data jsonb NOT NULL,
  patient_id uuid REFERENCES user_profiles(id),
  session_id TEXT,
  page_url TEXT,
  referrer_url TEXT,
  ip_address INET,
  user_agent TEXT,
  device_type TEXT, -- 'desktop', 'mobile', 'tablet'
  browser TEXT,
  os TEXT,
  country TEXT,
  city TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Website metrics aggregation
CREATE TABLE website_metrics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  date DATE NOT NULL,
  page_path TEXT NOT NULL,
  page_views INTEGER DEFAULT 0,
  unique_visitors INTEGER DEFAULT 0,
  bounce_rate DECIMAL(5,2) DEFAULT 0,
  avg_session_duration INTEGER DEFAULT 0, -- in seconds
  conversion_rate DECIMAL(5,2) DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(date, page_path)
);

-- Business KPI tracking
CREATE TABLE kpi_metrics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  date DATE NOT NULL,
  metric_name TEXT NOT NULL,
  metric_value DECIMAL(15,2) NOT NULL,
  metric_unit TEXT, -- 'count', 'percentage', 'currency', 'time'
  category TEXT NOT NULL, -- 'financial', 'operational', 'marketing', 'patient_satisfaction'
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(date, metric_name)
);

-- =============================================
-- INVENTORY AND OPERATIONS TABLES
-- =============================================

-- Inventory items
CREATE TABLE inventory_items (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  category TEXT NOT NULL, -- 'medical_supplies', 'equipment', 'medication', 'consumables'
  sku TEXT UNIQUE,
  barcode TEXT UNIQUE,
  unit_of_measure TEXT NOT NULL, -- 'piece', 'box', 'bottle', 'kg', 'liter'
  unit_cost DECIMAL(10,2),
  current_stock INTEGER DEFAULT 0,
  minimum_stock INTEGER DEFAULT 0,
  maximum_stock INTEGER,
  reorder_point INTEGER,
  supplier TEXT,
  supplier_contact TEXT,
  storage_location TEXT,
  expiry_tracking BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  created_by uuid REFERENCES admin_users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Inventory transactions
CREATE TABLE inventory_transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  item_id uuid REFERENCES inventory_items(id) ON DELETE CASCADE,
  transaction_type TEXT NOT NULL, -- 'purchase', 'use', 'adjustment', 'waste', 'return'
  quantity INTEGER NOT NULL, -- positive for in, negative for out
  unit_cost DECIMAL(10,2),
  total_cost DECIMAL(10,2),
  reference_id uuid, -- appointment_id, purchase_order_id, etc.
  reference_type TEXT, -- 'appointment', 'purchase_order', 'manual_adjustment'
  notes TEXT,
  batch_number TEXT,
  expiry_date DATE,
  created_by uuid REFERENCES admin_users(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Admin users indexes
CREATE INDEX idx_admin_users_role ON admin_users(role);
CREATE INDEX idx_admin_users_is_active ON admin_users(is_active);
CREATE INDEX idx_admin_users_department ON admin_users(department);

-- CMS indexes
CREATE INDEX idx_cms_pages_slug ON cms_pages(slug);
CREATE INDEX idx_cms_pages_published ON cms_pages(is_published);
CREATE INDEX idx_cms_pages_type ON cms_pages(page_type);
CREATE INDEX idx_cms_pages_created_at ON cms_pages(created_at DESC);
CREATE INDEX idx_cms_media_mime_type ON cms_media(mime_type);
CREATE INDEX idx_cms_media_active ON cms_media(is_active);

-- CRM indexes
CREATE INDEX idx_customer_interactions_patient ON customer_interactions(patient_id);
CREATE INDEX idx_customer_interactions_type ON customer_interactions(interaction_type);
CREATE INDEX idx_customer_interactions_created_at ON customer_interactions(created_at DESC);
CREATE INDEX idx_customer_notes_patient ON customer_notes(patient_id);
CREATE INDEX idx_customer_notes_type ON customer_notes(note_type);

-- Marketing indexes
CREATE INDEX idx_marketing_campaigns_status ON marketing_campaigns(status);
CREATE INDEX idx_marketing_campaigns_scheduled ON marketing_campaigns(scheduled_at);
CREATE INDEX idx_campaign_recipients_campaign ON campaign_recipients(campaign_id);
CREATE INDEX idx_campaign_recipients_status ON campaign_recipients(status);

-- Analytics indexes
CREATE INDEX idx_analytics_events_type ON analytics_events(event_type);
CREATE INDEX idx_analytics_events_category ON analytics_events(event_category);
CREATE INDEX idx_analytics_events_patient ON analytics_events(patient_id);
CREATE INDEX idx_analytics_events_created_at ON analytics_events(created_at DESC);
CREATE INDEX idx_website_metrics_date ON website_metrics(date DESC);
CREATE INDEX idx_kpi_metrics_date ON kpi_metrics(date DESC);

-- Inventory indexes
CREATE INDEX idx_inventory_items_category ON inventory_items(category);
CREATE INDEX idx_inventory_items_sku ON inventory_items(sku);
CREATE INDEX idx_inventory_items_low_stock ON inventory_items(current_stock) WHERE current_stock <= minimum_stock;
CREATE INDEX idx_inventory_transactions_item ON inventory_transactions(item_id);
CREATE INDEX idx_inventory_transactions_type ON inventory_transactions(transaction_type);
CREATE INDEX idx_inventory_transactions_created_at ON inventory_transactions(created_at DESC);

-- =============================================
-- ROW LEVEL SECURITY POLICIES
-- =============================================

-- Enable RLS on all admin tables
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE cms_pages ENABLE ROW LEVEL SECURITY;
ALTER TABLE cms_media ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_segments ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE marketing_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE campaign_recipients ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE website_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE kpi_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_transactions ENABLE ROW LEVEL SECURITY;

-- Helper function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin_user(user_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 FROM admin_users 
    WHERE id = user_id AND is_active = true
  );
$$;

-- Helper function to get admin role
CREATE OR REPLACE FUNCTION get_admin_role(user_id uuid)
RETURNS admin_role
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT role FROM admin_users 
  WHERE id = user_id AND is_active = true;
$$;

-- Helper function to check admin permission
CREATE OR REPLACE FUNCTION has_admin_permission(user_id uuid, permission_name text)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT 
    CASE 
      WHEN get_admin_role(user_id) = 'super_admin' THEN true
      ELSE COALESCE((permissions->permission_name)::boolean, false)
    END
  FROM admin_users 
  WHERE id = user_id AND is_active = true;
$$;

-- Basic admin access policy (used by multiple tables)
CREATE POLICY "Admin users can manage content" ON cms_pages
  FOR ALL USING (is_admin_user(auth.uid()));

CREATE POLICY "Admin users can manage media" ON cms_media
  FOR ALL USING (is_admin_user(auth.uid()));

CREATE POLICY "Admin users can manage categories" ON blog_categories
  FOR ALL USING (is_admin_user(auth.uid()));

CREATE POLICY "Admin users can manage tags" ON blog_tags
  FOR ALL USING (is_admin_user(auth.uid()));

CREATE POLICY "Admin users can manage segments" ON customer_segments
  FOR ALL USING (is_admin_user(auth.uid()));

CREATE POLICY "Admin users can manage interactions" ON customer_interactions
  FOR ALL USING (is_admin_user(auth.uid()));

CREATE POLICY "Admin users can manage templates" ON email_templates
  FOR ALL USING (is_admin_user(auth.uid()));

CREATE POLICY "Admin users can manage campaigns" ON marketing_campaigns
  FOR ALL USING (is_admin_user(auth.uid()));

CREATE POLICY "Admin users can view recipients" ON campaign_recipients
  FOR SELECT USING (is_admin_user(auth.uid()));

CREATE POLICY "Admin users can view analytics" ON analytics_events
  FOR SELECT USING (is_admin_user(auth.uid()));

CREATE POLICY "Admin users can view metrics" ON website_metrics
  FOR SELECT USING (is_admin_user(auth.uid()));

CREATE POLICY "Admin users can manage kpis" ON kpi_metrics
  FOR ALL USING (is_admin_user(auth.uid()));

CREATE POLICY "Admin users can manage inventory" ON inventory_items
  FOR ALL USING (is_admin_user(auth.uid()));

CREATE POLICY "Admin users can manage transactions" ON inventory_transactions
  FOR ALL USING (is_admin_user(auth.uid()));

-- More restrictive policies for sensitive data
CREATE POLICY "Super admins and managers can manage admin users" ON admin_users
  FOR ALL USING (
    get_admin_role(auth.uid()) IN ('super_admin', 'admin') 
    OR auth.uid() = id -- Users can view/edit their own profile
  );

CREATE POLICY "Admin users can view their own profile" ON admin_profiles
  FOR ALL USING (
    is_admin_user(auth.uid()) AND (
      get_admin_role(auth.uid()) IN ('super_admin', 'admin') 
      OR auth.uid() = id
    )
  );

-- Customer notes privacy policy
CREATE POLICY "Admin users can manage customer notes" ON customer_notes
  FOR ALL USING (
    is_admin_user(auth.uid()) AND (
      NOT is_private 
      OR created_by = auth.uid() 
      OR get_admin_role(auth.uid()) IN ('super_admin', 'admin')
    )
  );

-- =============================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =============================================

-- Update timestamps trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;

-- Apply update triggers to relevant tables
CREATE TRIGGER update_admin_users_updated_at
  BEFORE UPDATE ON admin_users
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_admin_profiles_updated_at
  BEFORE UPDATE ON admin_profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cms_pages_updated_at
  BEFORE UPDATE ON cms_pages
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customer_interactions_updated_at
  BEFORE UPDATE ON customer_interactions
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customer_notes_updated_at
  BEFORE UPDATE ON customer_notes
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_email_templates_updated_at
  BEFORE UPDATE ON email_templates
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_marketing_campaigns_updated_at
  BEFORE UPDATE ON marketing_campaigns
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_inventory_items_updated_at
  BEFORE UPDATE ON inventory_items
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- SAMPLE DATA FOR TESTING
-- =============================================

-- Insert sample admin roles and permissions
INSERT INTO admin_users (id, role, department, employee_id) VALUES
  ('11111111-1111-1111-1111-111111111111', 'super_admin', 'Management', 'EMP001'),
  ('22222222-2222-2222-2222-222222222222', 'admin', 'Operations', 'EMP002'),
  ('33333333-3333-3333-3333-333333333333', 'content_editor', 'Marketing', 'EMP003');

-- Insert sample blog categories
INSERT INTO blog_categories (name, slug, description, color) VALUES
  ('{"en": "Health Tips", "th": "เคล็ดลับสุขภาพ"}', 'health-tips', '{"en": "General health and wellness advice", "th": "คำแนะนำด้านสุขภาพและความเป็นอยู่ที่ดี"}', '#10B981'),
  ('{"en": "Beauty Treatments", "th": "การรักษาความงาม"}', 'beauty-treatments', '{"en": "Information about cosmetic procedures", "th": "ข้อมูลเกี่ยวกับขั้นตอนการรักษาความงาม"}', '#F59E0B'),
  ('{"en": "Clinic News", "th": "ข่าวสารคลินิก"}', 'clinic-news', '{"en": "Updates and announcements from the clinic", "th": "ข้อมูลอัพเดตและประกาศจากคลินิก"}', '#3B82F6');

-- Insert sample customer segments
INSERT INTO customer_segments (name, description, criteria, color) VALUES
  ('VIP Patients', 'High-value customers with premium service needs', '{"total_spent": {"gte": 50000}, "appointment_count": {"gte": 5}}', '#EF4444'),
  ('New Patients', 'Patients who joined in the last 30 days', '{"created_at": {"gte": "30 days ago"}}', '#10B981'),
  ('Inactive Patients', 'Patients who haven''t booked in 6 months', '{"last_appointment": {"lte": "6 months ago"}}', '#6B7280');

-- Insert sample email templates
INSERT INTO email_templates (name, category, subject, html_content, text_content, variables) VALUES
  ('Welcome New Patient', 'welcome', 
   '{"en": "Welcome to Lullaby Clinic!", "th": "ยินดีต้อนรับสู่ Lullaby Clinic!"}',
   '{"en": "<h1>Welcome {{patientName}}!</h1><p>Thank you for choosing Lullaby Clinic.</p>", "th": "<h1>ยินดีต้อนรับ {{patientName}}!</h1><p>ขอบคุณที่เลือก Lullaby Clinic</p>"}',
   '{"en": "Welcome {{patientName}}! Thank you for choosing Lullaby Clinic.", "th": "ยินดีต้อนรับ {{patientName}}! ขอบคุณที่เลือก Lullaby Clinic"}',
   ARRAY['patientName', 'clinicName', 'contactPhone']);

COMMENT ON TABLE admin_users IS 'Administrative users with role-based access control';
COMMENT ON TABLE cms_pages IS 'Dynamic website pages with multilingual content support';
COMMENT ON TABLE customer_segments IS 'Patient segmentation for targeted marketing and communications';
COMMENT ON TABLE marketing_campaigns IS 'Email and SMS marketing campaigns with performance tracking';
COMMENT ON TABLE analytics_events IS 'User behavior tracking for website and app analytics';
COMMENT ON TABLE inventory_items IS 'Medical supplies and equipment inventory management';