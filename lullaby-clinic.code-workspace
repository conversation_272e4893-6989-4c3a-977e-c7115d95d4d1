{"folders": [{"name": "🏥 Lullaby Clinic", "path": "."}], "settings": {"typescript.preferences.useAliasesForRenames": false, "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "files.associations": {"*.tsx": "typescriptreact", "*.ts": "typescript"}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.git": true, "**/bun.lockb": true, "**/package-lock.json": true}, "files.exclude": {"**/node_modules": true, "**/dist": true, "**/.git": true}, "explorer.fileNesting.enabled": true, "explorer.fileNesting.expand": false, "explorer.fileNesting.patterns": {"*.ts": "${capture}.js", "*.tsx": "${capture}.js", "tsconfig.json": "tsconfig.*.json", "package.json": "package-lock.json, bun.lockb, yarn.lock, pnpm-lock.yaml", "tailwind.config.*": "postcss.config.*, autoprefixer.config.*", ".cursorrules": ".rule, .gitignore"}, "tailwindCSS.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "editor.quickSuggestions": {"strings": true}, "css.validate": false, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}, "extensions": {"recommendations": ["bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "ms-vscode.vscode-typescript-next", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-json", "usernamehw.errorlens", "gruntfuggly.todo-tree", "ms-vscode.vscode-eslint"]}, "tasks": {"version": "2.0.0", "tasks": [{"label": "🚀 Start Development Server", "type": "shell", "command": "npm run dev", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "🔧 Build Project", "type": "shell", "command": "npm run build", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "👁️ Preview Build", "type": "shell", "command": "npm run preview", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "🔍 Explore Project Structure", "type": "shell", "command": "./scripts/explore-project.sh", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": []}, {"label": "📦 Show Components", "type": "shell", "command": "./scripts/explore-project.sh components", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": []}, {"label": "🧹 Lint Code", "type": "shell", "command": "npm run lint", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}, "launch": {"version": "0.2.0", "configurations": [{"name": "🐛 Debug in Chrome", "type": "chrome", "request": "launch", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/src", "sourceMaps": true}]}}