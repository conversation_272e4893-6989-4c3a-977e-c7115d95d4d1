[build]
  command = "npm run build"
  publish = "dist"

[build.environment]
  VITE_APP_ENVIRONMENT = "production"
  NODE_VERSION = "18"

# SPA redirect for client-side routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Language-specific redirects
[[redirects]]
  from = "/th"
  to = "/?lang=th"
  status = 302

[[redirects]]
  from = "/en"
  to = "/?lang=en"
  status = 302

[[redirects]]
  from = "/zh"
  to = "/?lang=zh"
  status = 302

# Legacy redirect
[[redirects]]
  from = "/home"
  to = "/"
  status = 301

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"
    Strict-Transport-Security = "max-age=********; includeSubDomains"

# Cache static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/*.js"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/*.css"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/*.png"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/*.jpg"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/*.svg"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Form handling for contact forms
[[forms]]
  name = "contact"