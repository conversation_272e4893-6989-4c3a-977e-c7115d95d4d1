
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    
    <!-- Primary Meta Tags -->
    <title>Lullaby Clinic - Premium Beauty & Medical Aesthetics</title>
    <meta name="title" content="Lullaby Clinic - Premium Beauty & Medical Aesthetics" />
    <meta name="description" content="Transform your dreams into reality at Lullaby Clinic. Leading beauty clinic with cutting-edge technology and expert medical team for comprehensive aesthetic treatments." />
    <meta name="keywords" content="beauty clinic, medical aesthetics, botox, fillers, laser treatments, skincare, cosmetic surgery, lullaby clinic, bangkok" />
    <meta name="author" content="Lullaby Clinic" />
    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://lullabyclinic.com/" />
    
    <!-- Language alternates -->
    <link rel="alternate" hreflang="en" href="https://lullabyclinic.com/" />
    <link rel="alternate" hreflang="th" href="https://lullabyclinic.com/th" />
    <link rel="alternate" hreflang="zh" href="https://lullabyclinic.com/zh" />
    <link rel="alternate" hreflang="x-default" href="https://lullabyclinic.com/" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://lullabyclinic.com/" />
    <meta property="og:title" content="Lullaby Clinic - Premium Beauty & Medical Aesthetics" />
    <meta property="og:description" content="Leading beauty clinic with cutting-edge technology and expert medical team for comprehensive aesthetic treatments." />
    <meta property="og:image" content="https://lullabyclinic.com/logo.svg" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:locale" content="en_US" />
    <meta property="og:locale:alternate" content="th_TH" />
    <meta property="og:locale:alternate" content="zh_CN" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://lullabyclinic.com/" />
    <meta property="twitter:title" content="Lullaby Clinic - Premium Beauty & Medical Aesthetics" />
    <meta property="twitter:description" content="Leading beauty clinic with cutting-edge technology and expert medical team for comprehensive aesthetic treatments." />
    <meta property="twitter:image" content="https://lullabyclinic.com/logo.svg" />
    <meta name="twitter:site" content="@lullaby_clinic" />
    <meta name="twitter:creator" content="@lullaby_clinic" />

    <!-- Favicons and Icons -->
    <link rel="icon" type="image/svg+xml" href="/logo.svg" />
    <link rel="icon" type="image/png" sizes="32x32" href="/logo.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/logo.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/logo.png" />
    <link rel="mask-icon" href="/logo.svg" color="#EF476F" />
    
    <!-- Theme and App Meta -->
    <meta name="theme-color" content="#EF476F" />
    <meta name="msapplication-TileColor" content="#EF476F" />
    <meta name="msapplication-navbutton-color" content="#EF476F" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Lullaby Clinic" />
    <meta name="format-detection" content="telephone=yes" />
    
    <!-- Performance Optimization -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="dns-prefetch" href="//www.google-analytics.com" />
    <link rel="dns-prefetch" href="//fonts.googleapis.com" />
    
    <!-- Fonts with display=swap for better performance -->
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    
    <!-- Preload critical resources -->
    <link rel="modulepreload" href="/src/main.tsx" />
    
    <!-- Medical Business Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "MedicalBusiness",
      "name": "Lullaby Clinic",
      "description": "Leading beauty clinic with cutting-edge technology and expert medical team for comprehensive aesthetic treatments.",
      "url": "https://lullabyclinic.com",
      "logo": "https://lullabyclinic.com/logo.svg",
      "image": "https://lullabyclinic.com/logo.svg",
      "telephone": "+66-64-646-8656",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "123 Medical Center",
        "addressLocality": "Bangkok",
        "addressRegion": "Bangkok",
        "postalCode": "10110",
        "addressCountry": "TH"
      },
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": "13.7563",
        "longitude": "100.5018"
      },
      "openingHoursSpecification": [
        {
          "@type": "OpeningHoursSpecification",
          "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
          "opens": "09:00",
          "closes": "18:00"
        },
        {
          "@type": "OpeningHoursSpecification",
          "dayOfWeek": ["Saturday"],
          "opens": "09:00",
          "closes": "17:00"
        }
      ],
      "medicalSpecialty": ["Dermatology", "Plastic Surgery", "Cosmetic Surgery", "Aesthetic Medicine"],
      "serviceType": ["Botox Injections", "Dermal Fillers", "Laser Treatments", "Facial Treatments", "Body Contouring"],
      "priceRange": "$$-$$$",
      "currenciesAccepted": "THB",
      "paymentAccepted": "Cash, Credit Card, Bank Transfer",
      "languagesSpoken": ["Thai", "English", "Chinese"],
      "sameAs": [
        "https://www.facebook.com/lullabyclinic",
        "https://www.instagram.com/lullabyclinic",
        "https://line.me/lullabyclinic"
      ]
    }
    </script>
  </head>

  <body>
    <!-- Loading screen for better perceived performance -->
    <div id="loading-screen" style="
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #EF476F 0%, #FF6B8A 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: opacity 0.3s ease-out;
    ">
      <div style="
        text-align: center;
        color: white;
      ">
        <div style="
          width: 60px;
          height: 60px;
          border: 3px solid rgba(255,255,255,0.3);
          border-top: 3px solid white;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 20px;
        "></div>
        <h1 style="
          font-family: 'Poppins', sans-serif;
          font-size: 24px;
          font-weight: 600;
          margin: 0;
          letter-spacing: -0.5px;
        ">Lullaby Clinic</h1>
        <p style="
          font-family: 'Poppins', sans-serif;
          font-size: 14px;
          margin: 8px 0 0;
          opacity: 0.9;
        ">Loading your beauty journey...</p>
      </div>
      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>
    </div>

    <div id="root"></div>
    
    <!-- Remove loading screen when React app loads -->
    <script>
      window.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
          const loadingScreen = document.getElementById('loading-screen');
          if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            setTimeout(() => loadingScreen.remove(), 300);
          }
        }, 100);
      });
    </script>
    
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Critical CSS inlined for faster rendering -->
    <style>
      /* Critical above-the-fold styles */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        line-height: 1.6;
        color: #1a1a1a;
        background-color: #ffffff;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      
      /* Prevent layout shift */
      img {
        max-width: 100%;
        height: auto;
        display: block;
      }
      
      /* Hide loading screen when app is ready */
      .app-loaded #loading-screen {
        display: none;
      }
    </style>
  </body>
</html>
